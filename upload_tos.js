const { TosClient } = require('@volcengine/tos-sdk');
const fs = require('fs');

// out-endpoint: tos-cn-beijing.volces.com
// inner-endpoint: tos-cn-beijing.ivolces.com
// region: cn-beijing
// access-key-id: AKLTZjI3ZjM2MTgxNGQwNDJkMDk1OWJkODg3NThlNmYyZjk
// access-key-secret: WkRNNE1USmxaREl5WmpCak5EQmpPVGxtTnpRNU5qSXhZV1JqTVdKbFpEYw==
// cms-bucket-name: pengwin-cms-video
// saas-bucket-name: pengwin-saas-video
// local-path: /material/
// 创建客户端
const client = new TosClient({
  accessKeyId: 'AKLTNTY1MjU5M2Q4OGFjNDJmNmJjYmY3MGI4ZjY5OTFiYmQ',
  accessKeySecret:
    'WkRnd09HTXlNbVV6TWpoa05EYzJPRGcwTkdRM1ltWmhPV1JsTm1OaFpUZw==',
  region: 'cn-beijing', // 填写 Bucket 所在地域
  endpoint: 'tos-cn-beijing.volces.com', // 填写域名地址
});

const bucketName = 'cms-bj-tos-cdn';
async function uploadAction(objectName, filePath) {
  console.log('开始上传', objectName, filePath);
  try {
    // const bucketName = 'cms-bj-tos-cdn';
    // const objectName = 'example_dir/example.txt';
    // 本地 example_dir 文件夹下的 example.txt 文件
    // const filePath = './example_dir/example.txt';
    // 上传对象
    const res = await client.putObjectFromFile({
      bucket: bucketName,
      key: objectName,
      filePath,
    });

    /// / 查询刚刚上传对象的大小
    // const { data } = await client.headObject({
    //  bucket: bucketName,
    //  key: objectName,
    // });
    console.log('上传成功', objectName);
  } catch (error) {
    console.error(error);
  }
}

async function scanDirectory(directory) {
  // 读取目录内容
  const files = fs.readdirSync(directory);

  for (let i = 0; i < files.length; i += 1) {
    const file = files[i];
    // 获取完整路径
    let path = `${directory}/${file}`;

    // 判断是否为文件或者子目录
    if (fs.statSync(path).isFile()) {
      // console.log(`文件名：${file}`);
      // eslint-disable-next-line no-await-in-loop
      await uploadAction(
        process.argv[3] + path.slice(process.argv[2].length),
        path
      );
      // 如果需要输出文件内容，则可以在这里添加相应的逻辑
    } else if (fs.statSync(path).isDirectory()) {
      // console.log(`子目录：${file}`);

      // 如果需要进入子目录并扫描其中的文件，则可以在这里调用scanDirectory函数
      scanDirectory(path);
    }
  }
}

if (process.argv[2] || process.argv[3]) {
  console.log(`上传目录为 ${process.argv[2]}`);
  scanDirectory(process.argv[2]);
} else {
  console.error('请传入路径');
}

// node upload_tos.js ../juwei-cms-publish/$filename codestatic/cms/$filename

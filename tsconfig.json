{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ES2020",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "lib": ["es2021", "dom"],
    "skipLibCheck": true,
    "allowJs": true,
  },
  "include": ["src/**/*", "src/**/*.vue"],
  "exclude": ["node_modules"]
}

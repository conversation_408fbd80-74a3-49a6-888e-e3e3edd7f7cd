import { mergeConfig } from 'vite';
import eslint from 'vite-plugin-eslint';
import baseConfig from './vite.config.base';

export default mergeConfig(baseConfig, {
  mode: 'development',
  server: {
    port: 4026,
    host: '0.0.0.0',
    open: true,
    fs: {
      strict: true,
    },
    // 设置反向代理，跨域
    proxy: {
      '/api': {
        // 测试环境后台
        target: 'http://************:9162',
        // target: 'https://getclue-v2.pengwin.com',
        changeOrigin: true,
      },
    },
  },
  plugins: [
    eslint({
      cache: false,
      include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
      exclude: ['node_modules'],
    }),
  ],
});

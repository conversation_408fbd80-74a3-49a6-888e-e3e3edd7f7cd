import { mergeConfig } from 'vite';
import { resolve } from 'path';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configArcoResolverPlugin from './plugin/arcoResolver';
import configImageminPlugin from './plugin/imagemin';

export default mergeConfig(baseConfig, {
  base: 'https://cms-static.pengwin.com/codestatic/travel/getclue/',
  mode: 'production',
  plugins: [
    configCompressPlugin('gzip'),
    configVisualizerPlugin(),
    // configArcoResolverPlugin(), //按需引入的路线，打开后自定义主题会被覆盖
    configImageminPlugin(),
  ],
  build: {
    outDir: resolve(__dirname, `../dist/getclue`),
    rollupOptions: {
      output: {
        manualChunks: {
          arco: ['@arco-design/web-vue'],
          chart: ['echarts', 'vue-echarts'],
          vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
        },
      },
    },
    chunkSizeWarningLimit: 2000,
  },
});

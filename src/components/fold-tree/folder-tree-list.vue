<template>
  <div
    class="layout-left-side-content"
    :class="{ 'need-min-width': needMinWidth }"
  >
    <a-spin
      :loading="loading"
      style="min-height: 200px; width: 100%"
      tip="加载中..."
    >
      <div class="title-box">
        <div>
          <icon-folder />
          <span v-if="wholeTitle" class="ml-5">
            {{ wholeTitle }}
          </span>
          <span v-else class="ml-5"> {{ preTitle }}文件夹 </span>
        </div>
        <a-button
          v-if="apis.save"
          size="mini"
          type="text"
          @click="showAddModal({})"
        >
          <icon-plus /> 新建
        </a-button>
      </div>
      <a-divider :margin="10" />
      <a-tree
        v-model:selected-keys="selectedKeys"
        v-model:expanded-keys="expandedKeys"
        size="small"
        :data="treeData"
        @select="onSelect"
      >
        <!-- <template v-if="showMenu" #extra="nodeData">
          <a-popover
            v-if="nodeData.key && showPopMenu(nodeData)"
            :content-style="{ padding: '6px 10px' }"
            position="right"
          >
            <icon-more-vertical style="position: absolute; left: 0px" />
            <template #content>
              <a-space direction="vertical">
                <a-link
                  v-if="apis.save && nodeData.level_one !== 'share'"
                  @click="() => saveAction(nodeData)"
                >
                  编辑 &nbsp;
                  <icon-edit />
                </a-link>
                <a-popconfirm
                  v-if="apis.del"
                  :content="`确定要删除【${nodeData.title}】吗?`"
                  position="left"
                  @ok="delClick(nodeData)"
                >
                  <a-link>
                    删除 &nbsp;
                    <icon-delete />
                  </a-link>
                </a-popconfirm>
                <a-link
                  v-if="nodeData.level_one !== 'share' && apis.share"
                  @click="() => shareAction(nodeData)"
                >
                  分享 &nbsp;
                  <icon-share-alt />
                </a-link>
              </a-space>
            </template>
          </a-popover>
        </template> -->
        <template #icon="{ node }">
          <icon-code-sandbox v-if="node.is_collect_account == 1" />
          <icon-folder v-else />
        </template>
        <template #title="node">
          <div class="title-wrapper">
            <span class="folder-name">{{ node.title }}</span>
            <span
              v-if="node.is_collect_account == 1"
              class="collect-icon"
              @click="createFn(node)"
            >
              <a-tooltip content="手动采集此账号内容？">
                <icon-robot-add />
              </a-tooltip>
            </span>
            <a-popover
              v-if="showMenu && node.key && showPopMenu(node)"
              :content-style="{ padding: '6px 10px' }"
              position="right"
            >
              <icon-more-vertical size="12" />
              <template #content>
                <a-space direction="vertical">
                  <a-link
                    v-if="apis.save && node.level_one !== 'share'"
                    @click="() => saveAction(node)"
                  >
                    编辑 &nbsp;
                    <icon-edit />
                  </a-link>
                  <a-popconfirm
                    v-if="apis.del"
                    :content="`确定要删除【${node.title}】吗?`"
                    position="left"
                    @ok="delClick(node)"
                  >
                    <a-link>
                      删除 &nbsp;
                      <icon-delete />
                    </a-link>
                  </a-popconfirm>
                  <a-link
                    v-if="node.level_one !== 'share' && apis.share"
                    @click="() => shareAction(node)"
                  >
                    分享 &nbsp;
                    <icon-share-alt />
                  </a-link>
                </a-space>
              </template>
            </a-popover>
            <span v-if="showResourceCount" class="resource-count"
              >({{ node.resource_count || 0 }})</span
            >
          </div>
        </template>
      </a-tree>
    </a-spin>
    <save-folder-modal
      v-if="apis.save"
      ref="modalFolderRef"
      :api="apis.save"
      :multi-level="multiLevel"
      :tree-data="treeDataCanSelect"
      :send-params="sendParams"
      @refresh="getDept"
    ></save-folder-modal>
    <share-folder-modal
      v-if="apis.share"
      ref="shareFolderRef"
      :api="apis.share"
      :send-params="sendParams"
    ></share-folder-modal>
    <collect-rule-config-modal ref="configModal" type-title="手动" />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed, watch } from 'vue';
  import request from '@/api/request';
  import ShareFolderModal from '@/components/fold-tree/share-folder-modal.vue';
  import { isArray } from 'lodash';
  import CollectRuleConfigModal from '@/views/tools/content-collect/components/collect-rule-config-modal.vue';
  import SaveFolderModal from './save-folder-modal.vue';

  const props = defineProps({
    dirId: {
      type: [String, Number],
      default: () => '',
    },
    preTitle: {
      type: [String],
      default: '',
    },
    apis: {
      type: [Object],
      default: () => ({}),
      required: true,
    },
    showMenu: {
      type: Boolean,
      default: true,
    },
    showPopMenu: {
      type: Function,
      default: () => () => true,
    },
    // 是否多层级
    multiLevel: {
      type: Boolean,
      default: true,
    },
    sendParams: {
      type: [Object],
      default: null,
    },
    disableSelectShare: {
      type: [Boolean],
      default: false,
    },
    needMinWidth: {
      type: [Boolean],
      default: false,
    },
    showResourceCount: {
      type: [Boolean],
      default: false,
    },
    wholeTitle: {
      type: [String],
      default: '',
    },
  });
  const emit = defineEmits(['change', 'update:dirId']);
  const treeData = ref([]);
  const selectedKeys = ref<any>([0]);
  const expandedKeys = ref([0]);
  const loading = ref(false);
  const isAll = ref(true);
  const modalFolderRef = ref();
  const shareFolderRef = ref();

  const configModal = ref();
  const createFn = (record: any) => {
    configModal.value?.show({
      title: record.title,
    });
  };

  watch(
    () => props.dirId,
    () => {
      if (selectedKeys.value[0] !== props.dirId) {
        selectedKeys.value = [props.dirId];
      }
    }
  );

  const treeDataCanSelect = computed(
    () =>
      treeData.value.filter((item: any) => item.level_one !== 'share')[0]
        ?.children || []
  );

  // 创建文件夹弹窗
  const showAddModal = (record: any) => {
    modalFolderRef.value.show(record);
  };

  function calleArr(arr: any[], level_one = ''): any {
    return arr
      .map((item: any) => {
        item.title = item.dir_name;
        item.key = item.id;
        if (level_one) {
          item.level_one = level_one;
        }
        if (item.key === 'share') {
          item.level_one = 'share';
        }
        if (item.children) {
          item.children = calleArr(item.children, item.level_one);
        }
        return item;
      })
      .filter(
        (item: any) =>
          !props.disableSelectShare ||
          (props.disableSelectShare && item.level_one !== 'share')
      );
  }

  const getDept = async () => {
    if (props.apis.list) {
      loading.value = true;
      request(props.apis.list, {
        ...props.sendParams,
      })
        .then((res) => {
          treeData.value = calleArr(isArray(res.data) ? res.data : [res.data]);
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  const onSelect = async (val: any, data: any) => {
    isAll.value = val.length === 0;
    emit('update:dirId', val?.length === 0 ? '' : val?.slice(-1)[0]);
    emit('change', val?.length === 0 ? '' : val?.slice(-1)[0], data);
  };
  const saveAction = (item: any) => {
    showAddModal({
      dir_name: item.title,
      parent_id: item.parent_id,
      id: item.key,
    });
  };

  const shareAction = (item: any) => {
    shareFolderRef.value?.show({
      dir_name: item.title,
      dir_id: item.key,
    });
  };

  const delClick = (item: any) => {
    if (props.apis.del) {
      loading.value = true;
      request(props.apis.del, {
        dir_id: item.key,
        ...props.sendParams,
      })
        .then((res) => {
          getDept();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  onMounted(() => {
    getDept();
  });

  defineExpose({ getDept });
</script>

<style lang="less" scoped>
  .layout-left-side-content {
    // width: 200px;

    &.need-min-width {
      width: 180px;

      :deep(.arco-tree-node-title-text) {
        width: 130px;
      }
    }
  }

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  :deep(.arco-tree-node-title-text) {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    // width: 130px;
    width: 100%;
  }
  :deep(.arco-tree-node-title) {
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .folder-name {
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .resource-count {
    color: #86909c;
    font-size: 10px;
    margin-left: px;
    flex-shrink: 0;
  }

  .collect-icon {
    display: flex;
    align-items: center;
    opacity: 0;
    color: rgb(var(--primary-6));
    cursor: pointer;
  }

  .title-wrapper:hover {
    .collect-icon {
      opacity: 1;
    }
  }
</style>

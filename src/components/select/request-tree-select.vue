<template>
  <a-tree-select
    v-model="curVal"
    :data="dataList"
    :placeholder="placeholder"
    :loading="loading"
    :multiple="multiple"
    :max-tag-count="maxTagCount"
    :field-names="fieldNames"
    @change="handleChange"
  ></a-tree-select>
</template>

<script lang="ts" setup>
  import { computed, onMounted, ref, watch } from 'vue';
  import request from '@/api/request';
  import { cloneDeep, toString } from 'lodash';
  import BaseSelect from './base-select.vue';

  const props = defineProps({
    placeholder: {
      type: String,
      default: () => '请选择',
    },
    getDataList: {
      type: Function,
      default: null,
    },
    modelValue: {
      type: [String, Number, Array, Object],
      default: () => '',
    },
    valueKey: {
      type: [String],
      default: () => 'id',
    },
    labelKey: {
      type: [String],
      default: () => 'name',
    },
    childKey: {
      type: [String],
      default: () => 'children',
    },
    sendParams: {
      type: Object,
      default: () => {
        return {};
      },
    },
    multiple: {
      type: Boolean,
      default: () => false,
    },
    maxTagCount: {
      type: [Number],
      default: () => 0,
    },
    requestUrl: {
      type: String,
      default: () => '',
    },
    api: {
      type: String,
      default: () => '',
    },
  });
  const emit = defineEmits(['input', 'change', 'update:modelValue']);
  const loading = ref(false);
  const curVal = ref<any>('');
  const curParams = ref(cloneDeep(props.sendParams));
  const initFlag = ref(false);

  watch(
    () => props.modelValue,
    (newVal) => {
      if (!newVal) {
        curVal.value = '';
      } else {
        curVal.value = newVal;
      }
    },
    {
      immediate: true,
    }
  );
  const dataList = ref([]);
  const handleChange = (val: any) => {
    let paentItemData = {};
    let itemData = {};
    if (val) {
      dataList.value.some((item: any) => {
        if (item[props.valueKey] === val) {
          itemData = item;
          paentItemData = item;
          return true;
        }
        return item[props.childKey]?.some((cItem: any) => {
          if (cItem[props.valueKey] === val) {
            paentItemData = item;
            itemData = cItem;
            return true;
          }
          return false;
        });
      });
    }
    emit('update:modelValue', val);
    emit('change', val, itemData, paentItemData);
  };

  const apis: any = {
    user: {
      url: '/api/userList',
      labelKey: 'realname',
    },
  };
  const apiUrl = computed(() => props.requestUrl || apis[props.api]?.url);
  const curlabelKey = computed(
    () => apis[props.api]?.labelKey || props.labelKey
  );

  const fieldNames = computed(() => ({
    key: props.valueKey,
    title: curlabelKey.value,
    children: props.childKey,
  }));

  const getList = async () => {
    if (!apiUrl.value) return null;
    initFlag.value = true;
    loading.value = true;
    curParams.value = cloneDeep(props.sendParams);
    const res = await request(apiUrl.value, props.sendParams);
    if (props.getDataList) {
      dataList.value = props.getDataList(res.data);
    } else {
      dataList.value = res.data.data || res.data || [];
    }
    loading.value = false;
  };
  watch(
    () => props.sendParams,
    (newVal) => {
      // 如果和curParams不同或者没有初始化 发起请求 缓存当前的新值
      if (
        JSON.stringify(newVal) !== JSON.stringify(curParams.value) ||
        !initFlag.value
      ) {
        getList();
      }
    },
    {
      deep: true,
    }
  );

  onMounted(() => {
    getList();
  });
</script>

<style lang="less"></style>

<template>
  <BaseSelect
    v-model="curVal"
    :data-list="dataList"
    :value-key="valueKey"
    :label-key="curlabelKey"
    :placeholder="placeholder"
    :loading="loading"
    :multiple="multiple"
    :max-tag-count="maxTagCount"
    :allow-create="allowCreate"
    v-bind="$attrs"
    :trigger-props="{
      preventFocus: false,
    }"
    @change="handleChange"
    @popup-visible-change="visibleChange"
  >
  </BaseSelect>
</template>

<script lang="ts" setup>
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import request from '@/api/request';
  import { cloneDeep } from 'lodash';
  import BaseSelect from './base-select.vue';

  const apis = {
    user: {
      url: '/api/userList',
      labelKey: 'realname',
    },
    allUser: {
      url: '/api/user/userList',
      labelKey: 'user_name',
    },
    role: {
      url: '/api/roleList',
      labelKey: 'role_name',
    },
    product: {
      url: '/api/thread/selectList',
      labelKey: 'area',
    },
  };
  const props = defineProps({
    placeholder: {
      type: String,
      default: () => '请选择',
    },
    getDataList: {
      type: Function,
      default: null,
    },
    modelValue: {
      type: [String, Number, Array, Object],
      default: () => '',
    },
    valueKey: {
      type: [String],
      default: () => 'id',
    },
    labelKey: {
      type: [String],
      default: () => 'name',
    },
    sendParams: {
      type: Object,
      default: () => {
        return {};
      },
    },
    multiple: {
      type: Boolean,
      default: () => false,
    },
    maxTagCount: {
      type: [Number],
      default: () => 0,
    },
    requestUrl: {
      type: String,
      default: () => '',
    },
    addUrl: {
      type: String,
      default: () => '',
    },
    addKey: {
      type: String,
      default: () => '',
    },
    api: {
      type: String as PropType<keyof typeof apis>,
      default: () => '',
    },
    selectFirst: {
      type: Boolean,
      default: () => false,
    },
    allowCreate: {
      type: Boolean,
      default: () => false,
    },
  });
  const emit = defineEmits(['input', 'change', 'update:modelValue', 'save']);
  const loading = ref(false);
  const curVal = ref<any>('');
  const curParams = ref({});
  const initFlag = ref(false);
  watch(
    () => props.modelValue,
    (newVal) => {
      if (!newVal) {
        curVal.value = '';
      } else {
        curVal.value = newVal;
      }
    },
    {
      immediate: true,
    }
  );
  const dataList = ref([]);
  const handleChange = (val: any, item: any) => {
    emit('update:modelValue', val);
    emit('change', val, item);
  };

  const apiUrl = computed(() => props.requestUrl || apis[props.api]?.url);
  const curlabelKey = computed(
    () => apis[props.api]?.labelKey || props.labelKey
  );
  const getList = async () => {
    if (!apiUrl.value) return null;
    loading.value = true;
    initFlag.value = true;
    curParams.value = cloneDeep(props.sendParams);
    const res = await request(apiUrl.value, props.sendParams);
    if (props.getDataList) {
      dataList.value = props.getDataList(res.data);
    } else {
      dataList.value = res.data.data || res.data;
    }
    if (
      props.selectFirst &&
      dataList.value?.length &&
      (!props.modelValue ||
        (Array.isArray(props.modelValue) && !props.modelValue.length))
    ) {
      if (props.multiple || Array.isArray(props.modelValue)) {
        handleChange(
          [dataList.value[0]?.[props.valueKey]],
          [dataList.value[0]]
        );
      } else {
        handleChange(dataList.value[0]?.[props.valueKey], dataList.value[0]);
      }
    }

    loading.value = false;
  };
  watch(
    () => props.sendParams,
    (newVal) => {
      // 如果和curParams不同或者没有初始化 发起请求 缓存当前的新值
      if (
        JSON.stringify(newVal) !== JSON.stringify(curParams.value) ||
        !initFlag.value
      ) {
        getList();
      }
    },
    {
      deep: true,
    }
  );

  function addAction(val: string) {
    return request(props.addUrl, {
      [props.addKey]: val,
    });
  }

  function visibleChange(visible: boolean) {
    if (
      !visible &&
      props.allowCreate &&
      props.addUrl &&
      curVal.value &&
      !loading.value
    ) {
      let diff: string[] = [];
      // 检查需要需要新增的项
      if (props.multiple || Array.isArray(props.modelValue)) {
        // 多选
        diff = curVal.value.filter(
          (item: any) =>
            !dataList.value.find((i: any) => i[props.valueKey] === item)
        );
      } else if (
        // 单选
        !dataList.value.find((i: any) => i[props.valueKey] === curVal.value)
      ) {
        diff = [curVal.value];
      }
      if (diff.length) {
        // 已跟后端沟通 姓名重复不入库
        Promise.all(diff.map((item: any) => addAction(item))).then(async () => {
          emit('save');
          await getList();
          if (props.multiple || Array.isArray(props.modelValue)) {
            let items = dataList.value.filter((i: any) => {
              if (
                diff.includes(i[props.labelKey]) ||
                // @ts-ignore
                props.modelValue.includes(i[props.valueKey])
              ) {
                return true;
              }
              return false;
            });
            handleChange(
              items.map((item) => item[props.valueKey]),
              items
            );
          } else {
            let items = dataList.value.find(
              (i: any) =>
                diff.includes(i[props.labelKey]) ||
                props.modelValue === i[props.valueKey]
            );
            handleChange(items?.[props.valueKey], items);
          }
        });
      }
    }
  }

  onMounted(() => {
    getList();
  });
  defineExpose({
    getList,
  });
</script>

<style lang="less"></style>

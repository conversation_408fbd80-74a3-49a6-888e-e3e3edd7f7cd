<template>
  <div v-if="visible" class="preview-modal" @click.self="close">
    <!-- 关闭按钮 - 移至整个屏幕右上角 -->
    <button class="close-btn" @click="close">×</button>

    <div class="preview-modal-content">
      <!-- 左侧媒体区 -->
      <div v-if="currentMedia" class="media-section">
        <!-- 页码标签 - 移至左上角 -->
        <div v-if="list.length > 1" class="page-counter">
          {{ currentIndex + 1 }}/{{ list.length }}
        </div>

        <template
          v-if="currentMedia.type === 'image' || currentMediaType === 'image'"
        >
          <img :src="currentMedia.url" class="media-img" />
        </template>
        <template
          v-else-if="
            currentMedia.type === 'video' || currentMediaType === 'video'
          "
        >
          <video
            ref="videoRef"
            class="media-video"
            controls
            :autoplay="isAutoPlay"
            :style="{ opacity: isReady ? 1 : 0 }"
            @canplay="handleCanPlay"
            @error="handleVideoError"
            @loadeddata="handleVideoLoaded"
          >
            <source
              v-for="(item, index) in videoSrc"
              :key="index"
              :src="item"
            />
          </video>
          <img
            v-if="!isReady"
            class="loading-indicator"
            src="./loading.gif"
            alt="Loading"
          />
        </template>

        <!-- 导航按钮 - 重新设计为左右两侧 -->
        <div v-if="list.length > 1" class="media-nav">
          <button
            class="nav-btn prev-btn"
            :disabled="currentIndex === 0"
            @click="prev"
          >
            <span class="nav-icon">‹</span>
          </button>
          <button
            class="nav-btn next-btn"
            :disabled="currentIndex === list.length - 1"
            @click="next"
          >
            <span class="nav-icon">›</span>
          </button>
        </div>
      </div>
      <!-- 右侧信息区 -->
      <div v-if="currentMedia" class="info-section">
        <div class="file-info">
          <div><b>文件名：</b>{{ currentMedia.title || '-' }}</div>
          <div v-if="currentMedia.size"
            ><b>大小：</b>{{ currentMedia.size }}</div
          >
        </div>
        <!-- {{ currentMedia }} -->
        <div class="extra-info">
          <slot name="extra">
            <!-- ai标识 -->
            <div class="extra-info-item">
              <!--  是否是爬虫  -->
              <IconCodeSandbox
                v-if="currentMedia.extra?.media_content_id"
                size="30"
                style="margin-right: 5px"
              />
              <!-- AI标识 -->
              <img
                v-else-if="currentMedia.extra?.ai_flag == 1"
                class="ai-logo"
                src="@/assets/images/ai-icon-3.png"
                alt=""
              />
            </div>
            <!-- 上传时间 -->
            <div v-if="currentMedia.extra?.add_time" class="extra-info-item"
              >上传时间：{{ currentMedia.extra?.add_time }}</div
            >
            <!-- 发布次数 -->
            <div v-if="currentMedia.extra?.publish_num" class="extra-info-item"
              >已发布次数：{{ currentMedia.extra?.publish_num }}次</div
            >
            <!-- 上传者 -->
            <div
              v-if="currentMedia.extra?.create_user_name"
              class="extra-info-item"
              >上传人：{{ currentMedia.extra?.create_user_name }}</div
            >
            <!-- 内容区域 -->
            <div class="content-area">
              <div
                v-if="currentMedia.extra?.title || currentMedia.extra?.name"
                class="extra-info-item"
              >
                <div class="vertical-title">标题：</div>
                <div>
                  {{ currentMedia.extra?.title || currentMedia.extra?.name }}
                </div>
              </div>
              <div v-if="currentMedia.extra?.describe" class="extra-info-item">
                <div class="vertical-title">正文：</div>
                <div
                  v-if="
                    currentMedia.extra?.describe &&
                    currentMedia.extra?.describe.length > 100
                  "
                  class="extra-info-item-content pb"
                >
                  <pre>{{ currentMedia.extra?.describe }}</pre>
                </div>
                <div v-else class="extra-info-item-content">
                  <span>{{ currentMedia.extra?.describe }}</span>
                </div>
              </div>
            </div>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  // / 1. 直接传入URL
  // this.$refs.previewPlayer.play('https://example.com/video.mp4');

  // // 2. 传入单个媒体对象
  // this.$refs.previewPlayer.play({
  //   url: 'xxx.mp4',
  //   title: '视频标题',
  //   id: '1'
  // });

  // // 3. 传入媒体列表
  // this.$refs.previewPlayer.play([
  //   { url: 'xxx.mp4', title: '视频1' },
  //   { url: 'xxx.jpg', title: '图片1' }
  // ]);

  // // 4. 传入完整配置（原有方式）
  // this.$refs.previewPlayer.play({
  //   mediaList: [...],
  //   activeUrl: 'xxx.mp4',
  //   extra: '额外信息'
  // });

  // // 5. 兼容原有方式
  // this.$refs.previewPlayer.play({
  //   video_list: [...],
  //   videoUrl: 'xxx.mp4',
  //   extraInfo: '额外信息'
  // });

  // 图片场景
  // this.$refs.previewPlayer.play([
  //   'https://cms-static.pengwin.com/saas_clue/image/20250512/7454111747013973788903.jpg',
  //   'https://cms-static.pengwin.com/saas_clue/image/20250512/9326061747013973283811.jpg',
  //   'https://cms-static.pengwin.com/saas_clue/image/20250512/5025131747013973747375.jpg',
  // ]);

  import { getNoMarkUrl, getFileType } from '@/utils/util';
  import { IconCodeSandbox } from '@arco-design/web-vue/es/icon';

  export default {
    name: 'PopupPreviewPlayer',
    components: {
      IconCodeSandbox,
    },
    data() {
      return {
        visible: false,
        list: [], // 媒体列表
        currentIndex: 0,
        isAutoPlay: true,
        isReady: true,
        extra: '', // 额外信息
        onClose: null,
        preloadedImages: new Set(), // 用于存储已预加载的图片
        videoLoadAttempts: 0, // 添加视频加载尝试次数
      };
    },
    computed: {
      currentMedia() {
        return this.list[this.currentIndex] || null;
      },
      currentMediaType() {
        return getFileType(this.currentMedia?.url);
      },
      videoSrc() {
        if (
          this.currentMedia &&
          (this.currentMedia.type === 'video' ||
            this.currentMediaType === 'video') &&
          this.currentMedia.url
        ) {
          try {
            return [
              getNoMarkUrl(this.currentMedia.url),
              this.currentMedia.url,
            ].filter(Boolean);
          } catch (e) {
            console.error('Error generating video sources:', e);
            return [this.currentMedia.url];
          }
        }
        return [];
      },
    },
    watch: {
      visible(val) {
        if (val) {
          document.addEventListener('keydown', this.keydown);
        } else {
          document.removeEventListener('keydown', this.keydown);
          this.reset();
        }
      },
      currentIndex() {
        this.isReady = this.currentMedia?.type !== 'video';
        this.preloadNextImage(); // 每次索引变化时预加载下一张
      },
    },
    beforeUnmount() {
      document.removeEventListener('keydown', this.keydown);
    },
    methods: {
      play(data, index = 0) {
        // 处理不同的传参场景
        try {
          console.log('PopupPreviewPlayer.play called with:', data);
          let mediaList = [];

          if (!data) {
            console.error('No data provided to preview player');
            return;
          }

          if (Array.isArray(data)) {
            // 直接传入数组
            // 适配一下图片场景
            if (
              (data.length > 0 && data[0].includes('image')) ||
              data[0].includes('jpg') ||
              data[0].includes('png') ||
              data[0].includes('gif') ||
              data[0].includes('bmp') ||
              data[0].includes('webp')
            ) {
              mediaList = data.map((item) => ({
                url: item,
                type: 'image',
                title: item.split('/').pop(),
              }));
            } else {
              mediaList = data;
            }
          } else if (typeof data === 'string') {
            // 直接传入URL字符串
            mediaList = [{ url: data }];
          } else if (data.url) {
            // 传入单个媒体对象
            mediaList = [data];
          } else {
            // 传入配置对象
            mediaList = data.mediaList ||
              data.video_list || [
                {
                  url: data.videoUrl || data.activeUrl || data.url,
                  title: data.title,
                  id: data.id,
                  extra: data.extra || data.extraInfo,
                },
              ];
          }

          if (!mediaList.length || !mediaList[0]?.url) {
            console.error('No valid media found in:', data);
            return;
          }

          // 处理媒体列表
          this.list = mediaList
            .map((item) => {
              if (!item) return null;

              // 处理字符串类型（直接传URL的情况）
              const mediaItem = typeof item === 'string' ? { url: item } : item;
              if (!mediaItem.url) return null;

              // 自动识别类型
              let { type } = mediaItem;
              if (!type) {
                if (/\.(mp4|mov|webm|ogg)$/i.test(mediaItem.url))
                  type = 'video';
                else if (/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(mediaItem.url))
                  type = 'image';
                else type = 'image';
              }
              return { ...mediaItem, type };
            })
            .filter(Boolean);

          if (!this.list.length) {
            console.error('No valid media items found after processing');
            return;
          }

          // 设置当前项
          this.currentIndex = index;
          if (data.activeUrl || data.videoUrl) {
            const targetUrl = data.activeUrl || data.videoUrl;
            const idx = this.list.findIndex((item) => item.url === targetUrl);
            if (idx > -1) this.currentIndex = idx;
          }

          // 设置额外信息
          if (typeof data === 'object') {
            this.extra = data.extra || data.extraInfo || '';
            this.onClose = data.onClose || null;
          }

          this.reset();

          // 先设置是否是视频，再显示预览
          this.isReady = this.currentMedia?.type !== 'video';
          this.isAutoPlay = true;

          // 最后设置可见性，确保其他数据已经准备好
          this.$nextTick(() => {
            this.visible = true;
          });

          // 在设置完列表后预加载下一张图片
          this.$nextTick(() => {
            this.preloadNextImage();
          });

          console.log('PopupPreviewPlayer initialized with:', {
            currentMedia: this.currentMedia,
            mediaType: this.currentMedia?.type,
            isReady: this.isReady,
          });
        } catch (error) {
          console.error('Error in PopupPreviewPlayer.play:', error);
        }
      },
      handleCanPlay() {
        console.log('Video can play now');
        this.isReady = true;
      },
      handleVideoError(e) {
        console.error('Video error:', e);
        this.isReady = true; // 即使出错也显示视频元素
        this.videoLoadAttempts = 0;
      },
      handleVideoLoaded() {
        console.log('Video loaded successfully');
        this.isReady = true;
        this.videoLoadAttempts = 0;
      },
      async resetVideo() {
        if (this.$refs.videoRef) {
          try {
            this.$refs.videoRef.pause();
            this.$refs.videoRef.currentTime = 0;
            this.$refs.videoRef.load(); // 强制重新加载视频
          } catch (e) {
            console.error('Error resetting video:', e);
          }
        }
      },
      async prev() {
        if (this.currentIndex > 0) {
          await this.resetVideo();
          this.isReady = false;
          this.currentIndex -= 1;
          this.preloadNextImage();
        }
      },
      async next() {
        if (this.currentIndex < this.list.length - 1) {
          await this.resetVideo();
          this.isReady = false;
          this.currentIndex += 1;
          this.preloadNextImage();
        }
      },
      close() {
        this.resetVideo();
        this.visible = false;

        if (typeof this.onClose === 'function') {
          this.$nextTick(() => {
            this.onClose(this);
          });
        }
      },
      pauseVideo() {
        try {
          if (this.$refs.videoRef) {
            this.$refs.videoRef.pause();
            this.$refs.videoRef.currentTime = 0;
          }
        } catch (e) {
          console.error('Error pausing video:', e);
        }
      },
      reset() {
        this.isAutoPlay = true;
        this.isReady = true;
        this.videoLoadAttempts = 0;
        this.resetVideo();
      },
      keydown(e) {
        if (!this.visible) return;
        switch (e.keyCode) {
          case 27: // esc
            this.close();
            break;
          case 37: // left
            this.prev();
            break;
          case 39: // right
            this.next();
            break;
          default:
            break;
        }
      },
      preloadNextImage() {
        const nextIndex = this.currentIndex + 1;
        if (nextIndex < this.list.length) {
          const nextMedia = this.list[nextIndex];
          if (
            nextMedia.type === 'image' &&
            !this.preloadedImages.has(nextMedia.url)
          ) {
            const img = new Image();
            img.src = nextMedia.url;
            this.preloadedImages.add(nextMedia.url);
          }
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .preview-modal {
    position: fixed;
    inset: 0;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;

    .close-btn {
      position: fixed;
      top: 30px;
      right: 30px;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #fff;
      color: #333;
      border: none;
      font-size: 24px;
      box-shadow: 0 2px 8px #0003;
      cursor: pointer;
      padding-bottom: 4px;
      z-index: 10000; /* 确保在最上层 */
      &:hover {
        background: #f44;
        color: #fff;
      }
    }

    .preview-modal-content {
      display: flex;
      border-radius: 18px;
      overflow: hidden;
      box-shadow: 0 8px 32px #0005;
      background: #fff;
      min-width: 800px;
      height: 80vh;
      position: relative;
      .media-section {
        background: #000;
        width: 45vh;
        height: 100%;
        position: relative;
        overflow: hidden;
        padding-top: 56.25%; /* 16:9 ratio (9/16 * 100) */

        /* 页码标签样式 */
        .page-counter {
          position: absolute;
          top: 20px;
          right: 20px;
          background: rgba(0, 0, 0, 0.6);
          color: #fff;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 14px;
          z-index: 10;
        }

        .media-img,
        .media-video {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: contain;
          background: #111;
          transition: opacity 0.3s ease;
          will-change: opacity;
          backface-visibility: hidden;
          transform: translateZ(0);
        }

        .loading-indicator {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 300px;
          z-index: 1;
          opacity: 0;
          transition: opacity 0.3s ease;
          animation: pulse 1.5s ease-in-out infinite;
          will-change: transform, opacity;
        }

        .loading-indicator[src] {
          opacity: 1;
        }

        @keyframes pulse {
          0% {
            transform: translate(-50%, -50%) scale(0.95);
            opacity: 0.7;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.05);
            opacity: 1;
          }
          100% {
            transform: translate(-50%, -50%) scale(0.95);
            opacity: 0.7;
          }
        }

        /* 重新设计的导航按钮 */
        .media-nav {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          pointer-events: none; /* 允许点击穿透到下层元素 */

          .nav-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.4);
            border: none;
            color: #fff;
            font-size: 34px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 20px;
            transition: all 0.5s ease;
            pointer-events: auto; /* 恢复按钮的点击事件 */
            padding-bottom: 6px;
            padding-left: 6px;

            &:disabled {
              opacity: 0.3;
              cursor: not-allowed;
              transform: none;
              &:hover {
                scale: 1;
              }
            }
            &:hover {
              // transform: scale(1.05);
              scale: 1.2;
            }
          }
        }
      }
      .info-section {
        flex: 1;
        background: #fff;
        padding: 32px 24px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        width: 60vh;
        .file-info {
          font-size: 16px;
          color: #333;
          // margin-bottom: 12px;
        }
        .extra-info {
          font-size: 15px;
          color: #666;
          .extra-info-item {
            margin: 10px 0;
            .ai-logo {
              height: 32px;
              width: 32px;
            }
          }
        }
        .content-area {
          margin: 20px 0;
          .extra-info-item {
            margin-bottom: 20px;
            .vertical-title {
              margin-bottom: 10px;
            }
            .extra-info-item-content {
              max-height: 42vh;
              overflow-y: auto;
              font-size: 14px;
              background: var(--color-fill-1);
              padding: 10px;
              border-radius: 12px;
              // &.pb {
              // padding-bottom: 12vh;
              // }
            }
          }
        }
      }
    }
  }
</style>

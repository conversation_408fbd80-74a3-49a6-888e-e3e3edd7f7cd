<template>
  <a-row justify="space-between" align="center">
    <a-breadcrumb class="container-breadcrumb">
      <a-breadcrumb-item>
        <a-link @click="goHome"> <icon-home /> </a-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item v-for="item in items" :key="item.path || item">
        <a-link @click="goPage(item)">
          {{ item.label || $t(item) || item }}
        </a-link>
      </a-breadcrumb-item>
    </a-breadcrumb>
    <div v-show="showBackBtn || showBack" class="back-btn" @click="goBack">
      <icon-left /> 返回
    </div>
  </a-row>
</template>

<script lang="ts" setup>
  import { defineComponent, PropType, ref, watch } from 'vue';
  // 引入路由组件
  import { useRouter, useRoute } from 'vue-router';
  import { DEFAULT_ROUTE_NAME } from '@/router/constants';

  const props = defineProps({
    items: {
      type: Array as PropType<string[]>,
      default() {
        return [];
      },
    },
    showBack: {
      type: Boolean,
      default: false,
    },
  });
  const router = useRouter();
  const route = useRoute();
  const showBackBtn = ref(false);
  const goBack = () => {
    router.back();
  };
  // 回首页
  const goHome = () => {
    router.push({ name: DEFAULT_ROUTE_NAME });
  };
  const goPage = (item: string) => {
    router.push({ name: item.path || item.split('.').slice(-1)[0] });
  };
  // 监听路由变化，meta中的showBack存在时 展示返回按钮
  watch(
    () => route.meta,
    (val) => {
      if (val.showBackBtn) {
        showBackBtn.value = true;
      } else {
        showBackBtn.value = false;
      }
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped lang="less">
  .container-breadcrumb {
    margin: 5px 0 10px 0;
    :deep(.arco-breadcrumb-item) {
      color: rgb(var(--gray-6));
      &:last-child {
        color: rgb(var(--gray-8));
      }
    }
  }
  .back-btn {
    cursor: pointer;
    color: rgb(var(--primary-6));
  }
</style>

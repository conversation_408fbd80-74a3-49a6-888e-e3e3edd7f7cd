<template>
  <div class="navbar">
    <div class="left-side">
      <a-space>
        <img
          alt="logo"
          src="@/assets/images/logo-h.png"
          style="width: 150px; cursor: pointer"
          @click="router.push({ name: 'overview' })"
        />
        <icon-menu-fold
          v-if="appStore.device === 'mobile'"
          style="font-size: 22px; cursor: pointer"
          @click="toggleDrawerMenu"
        />
      </a-space>
    </div>
    <div class="center-side">
      <Menu
        v-if="topMenu && appStore.device === 'desktop'"
        :is-need-collapsed="false"
        mode="horizontal"
      />
    </div>
    <ul class="right-side">
      <li>
        <a-badge :count="dataCache.new_system_msg_num" :offset="[-2, 2]">
          <a-tooltip popup-container=".navbar" content="消息提醒">
            <a-button
              class="download-box message-notification-btn"
              :class="{ 'btn-loading': messageButtonLoading }"
              :shape="'circle'"
              :loading="messageButtonLoading"
              @click="goSysMessage"
            >
              <template #icon>
                <icon-notification />
              </template>
            </a-button>
          </a-tooltip>
        </a-badge>
      </li>
      <!-- 下载中心 -->
      <li>
        <a-tooltip popup-container=".navbar" content="下载中心">
          <a-button
            class="download-box download-center-btn"
            :class="{ 'btn-loading': downloadButtonLoading }"
            :shape="'circle'"
            :loading="downloadButtonLoading"
            @click="goTaskCenter"
          >
            <template #icon>
              <icon-cloud-download />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <!--<li>
        <a-tooltip :content="$t('settings.search')">
          <a-button class="nav-btn" type="outline" :shape="'circle'">
            <template #icon>
              <icon-search />
            </template>
          </a-button>
        </a-tooltip>
      </li>-->
      <!--多语言切换-->
      <!--<li>
        <a-tooltip :content="$t('settings.language')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setDropDownVisible"
          >
            <template #icon>
              <icon-language />
            </template>
          </a-button>
        </a-tooltip>
        <a-dropdown trigger="click" @select="changeLocale as any">
          <div ref="triggerBtn" class="trigger-btn"></div>
          <template #content>
            <a-doption
              v-for="item in locales"
              :key="item.value"
              :value="item.value"
            >
              <template #icon>
                <icon-check v-show="item.value === currentLocale" />
              </template>
              {{ item.label }}
            </a-doption>
          </template>
        </a-dropdown>
      </li>-->
      <!--<li>
        <a-tooltip
          :content="
            theme === 'light'
              ? $t('settings.navbar.theme.toDark')
              : $t('settings.navbar.theme.toLight')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="handleToggleTheme"
          >
            <template #icon>
              <icon-sun-fill v-if="theme === 'dark'" />
              <icon-moon-fill v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>-->
      <!-- <li>
        <a-tooltip
          :content="
            isFullscreen
              ? $t('settings.navbar.screen.toExit')
              : $t('settings.navbar.screen.toFull')
          "
        >
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="toggleFullScreen"
          >
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->
      <!--消息通知-->
      <!--<li>
        <a-tooltip :content="$t('settings.navbar.alerts')">
          <div class="message-box-trigger">
            <a-badge :count="9" dot>
              <a-button
                class="nav-btn"
                type="outline"
                :shape="'circle'"
                @click="setPopoverVisible"
              >
                <icon-notification />
              </a-button>
            </a-badge>
          </div>
        </a-tooltip>
        <a-popover
          trigger="click"
          :arrow-style="{ display: 'none' }"
          :content-style="{ padding: 0, minWidth: '400px' }"
          content-class="message-popover"
        >
          <div ref="refBtn" class="ref-btn"></div>
          <template #content>
            <message-box />
          </template>
        </a-popover>
      </li>-->

      <!--页面布局相关设置-->
      <!--<li>
        <a-tooltip :content="$t('settings.title')">
          <a-button
            class="nav-btn"
            type="outline"
            :shape="'circle'"
            @click="setVisible"
          >
            <template #icon>
              <icon-settings />
            </template>
          </a-button>
        </a-tooltip>
      </li>-->
      <li>
        <UserGuide></UserGuide>
      </li>
      <li
        v-if="userStore.hasPermission(3) && userStore.service_status === '在线'"
      >
        <a-button type="primary">接单中</a-button>
      </li>
      <!-- <li>
        <RelationGuide></RelationGuide>
      </li>-->
      <!-- <li>
        <a-dropdown trigger="hover" position="br">
          <a-avatar
            :size="30"
            :style="{
              marginRight: '8px',
              cursor: 'pointer',
              backgroundColor: '#0063fa',
            }"
          >
            <IconUser />
          </a-avatar>
          <template #content>
            <a-doption>
              <a-space @click="switchRoles">
                <icon-tag />
                <span>
                  {{ $t('messageBox.switchRoles') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="$router.push({ name: 'Info' })">
                <icon-user />
                <span>
                  {{ $t('messageBox.userCenter') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="$router.push({ name: 'Setting' })">
                <icon-settings />
                <span>
                  {{ $t('messageBox.userSettings') }}
                </span>
              </a-space>
            </a-doption>
            <div class="userinfo-box">
              <IconUser /> {{ userStore.user_name }}({{ userStore.account_name }})
            </div>
            <div class="userinfo-box">
              <icon-robot /> {{ versionStore.versionStr }}
            </div>
            <a-doption @click="handleLogout">
              <a-link>
                <template #icon>
                  <icon-export />
                </template>
                {{ $t('messageBox.logout') }}
              </a-link>
            </a-doption>
          </template>
        </a-dropdown>
      </li> -->
    </ul>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, inject, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useDark, useToggle, useFullscreen } from '@vueuse/core';
  import { useAppStore, useUserStore, useDataCacheStore } from '@/store';
  import { LOCALE_OPTIONS } from '@/locale';
  import useLocale from '@/hooks/locale';
  import useUser from '@/hooks/user';
  import Menu from '@/components/menu/index.vue';
  import { useVersionStore } from '@/hooks/check-version';
  import request from '@/api/request';
  import MessageBox from '../message-box/index.vue';
  import VipButton from './vip-button.vue';
  import UserGuide from './user-guide.vue';
  import RelationGuide from './relation-guide.vue';

  const router = useRouter();
  const route = useRoute();

  const appStore = useAppStore();
  const userStore = useUserStore();
  const versionStore = useVersionStore();
  const { logout } = useUser();
  const { changeLocale, currentLocale } = useLocale();
  const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
  const locales = [...LOCALE_OPTIONS];
  const avatar = computed(() => {
    return userStore.avatar;
  });
  const theme = computed(() => {
    return appStore.theme;
  });
  const topMenu = computed(() => appStore.topMenu && appStore.menu);
  const dataCache = useDataCacheStore();

  // 消息按钮加载状态
  const messageButtonLoading = ref(false);
  // 下载中心按钮加载状态
  const downloadButtonLoading = ref(false);

  // const isDark = useDark({
  //  selector: 'body',
  //  attribute: 'arco-theme',
  //  valueDark: 'dark',
  //  valueLight: 'light',
  //  storageKey: 'arco-theme',
  //  onChanged(dark: boolean) {
  //    // overridden default behavior
  //    appStore.toggleTheme(dark);
  //  },
  // });
  // const toggleTheme = useToggle(isDark);
  // const handleToggleTheme = () => {
  //  toggleTheme();
  // };
  // 刷新下载中心数据
  const refreshDownloadCenterData = async () => {
    try {
      // 触发下载中心页面的数据刷新事件
      // 通过全局事件总线通知下载中心页面刷新数据
      window.dispatchEvent(new CustomEvent('refresh-download-center'));

      console.log('✅ 下载中心数据刷新成功');
      return true;
    } catch (error) {
      console.error('❌ 下载中心数据刷新失败:', error);
      Message.error('刷新失败，请稍后重试');
      return false;
    }
  };

  // 跳转或刷新下载中心
  const goTaskCenter = async () => {
    // 防止重复点击
    if (downloadButtonLoading.value) {
      return;
    }

    try {
      downloadButtonLoading.value = true;

      // 检测当前是否已在下载中心页面
      const isOnDownloadCenterPage = route.name === 'download-center';

      if (isOnDownloadCenterPage) {
        // 如果已在下载中心页面，执行数据刷新
        console.log('🔄 当前在下载中心页面，执行数据刷新');
        const refreshSuccess = await refreshDownloadCenterData();

        if (refreshSuccess) {
          // 显示刷新成功的视觉反馈
          Message.success({
            content: '下载中心数据已刷新',
            duration: 2000,
          });
        }
      } else {
        // 如果不在下载中心页面，执行页面跳转
        console.log('🔗 跳转到下载中心页面');
        await router.push({
          name: 'download-center',
        });

        // 跳转后稍微延迟刷新数据，确保页面已加载
        setTimeout(() => {
          refreshDownloadCenterData();
        }, 300);
      }
    } catch (error) {
      console.error('❌ 下载中心操作失败:', error);
      Message.error('操作失败，请稍后重试');
    } finally {
      // 延迟移除加载状态，提供视觉反馈
      setTimeout(() => {
        downloadButtonLoading.value = false;
      }, 500);
    }
  };
  // 刷新系统消息数据
  const refreshSystemMessageData = async () => {
    try {
      // 刷新未读消息数量
      await dataCache.getNoRead();

      // 触发系统消息页面的数据刷新事件
      // 通过全局事件总线通知系统消息页面刷新数据
      window.dispatchEvent(new CustomEvent('refresh-system-message'));

      console.log('✅ 系统消息数据刷新成功');
      return true;
    } catch (error) {
      console.error('❌ 系统消息数据刷新失败:', error);
      Message.error('刷新失败，请稍后重试');
      return false;
    }
  };

  // 跳转或刷新消息中心
  const goSysMessage = async () => {
    // 防止重复点击
    if (messageButtonLoading.value) {
      return;
    }

    try {
      messageButtonLoading.value = true;

      // 检测当前是否已在系统消息页面
      const isOnSystemMessagePage = route.name === 'system-message';

      if (isOnSystemMessagePage) {
        // 如果已在系统消息页面，执行数据刷新
        console.log('🔄 当前在系统消息页面，执行数据刷新');
        const refreshSuccess = await refreshSystemMessageData();

        if (refreshSuccess) {
          // 显示刷新成功的视觉反馈
          Message.success({
            content: '消息数据已刷新',
            duration: 2000,
          });
        }
      } else {
        // 如果不在系统消息页面，执行页面跳转
        console.log('🔗 跳转到系统消息页面');
        await router.push({
          name: 'system-message',
        });

        // 跳转后稍微延迟刷新数据，确保页面已加载
        setTimeout(() => {
          refreshSystemMessageData();
        }, 300);
      }
    } catch (error) {
      console.error('❌ 消息中心操作失败:', error);
      Message.error('操作失败，请稍后重试');
    } finally {
      // 延迟移除加载状态，提供视觉反馈
      setTimeout(() => {
        messageButtonLoading.value = false;
      }, 500);
    }
  };
  const setVisible = () => {
    appStore.updateSettings({ globalSettings: true });
  };
  const refBtn = ref();
  const triggerBtn = ref();

  const setPopoverVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    refBtn.value.dispatchEvent(event);
  };
  const handleLogout = () => {
    logout();
  };
  const setDropDownVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    triggerBtn.value.dispatchEvent(event);
  };
  const switchRoles = async () => {
    // const res = await userStore.switchRoles();
    // Message.success(res as string);
  };
  const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void;

  // 任务中心点击跳转导航
  const gotoTaskRoute = (key: string) => {
    router.push({
      name: 'task-manage',
      query: {
        type: key,
      },
    });
  };
</script>

<style scoped lang="less">
  .navbar {
    display: flex;
    justify-content: space-between;
    height: 100%;
    transition: all 0.2s;
    // background-color: var(--color-bg-2);
    // border-bottom: 1px solid var(--color-border);
    box-shadow: 0 1px 5px #acacac26;
    //&.hasbg {
    //  background-color: var(--color-bg-2);
    //  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    //}
  }

  .left-side {
    display: flex;
    align-items: center;
    padding: 0;
    width: 190px;
    justify-content: center;
  }

  .center-side {
    flex: 1;
    padding-left: 20px;
  }

  .right-side {
    display: flex;
    padding-right: 10px;
    list-style: none;
    :deep(.locale-select) {
      border-radius: 20px;
    }
    li {
      display: flex;
      align-items: center;
      padding: 0 10px 0 0;
    }

    a {
      color: var(--color-text-1);
      text-decoration: none;
    }
    .nav-btn {
      border-color: rgb(var(--gray-2));
      color: rgb(var(--gray-8));
      font-size: 16px;
    }
    .trigger-btn,
    .ref-btn {
      position: absolute;
      bottom: 14px;
    }
    .trigger-btn {
      margin-left: 14px;
    }
  }
  .userinfo-box {
    padding: 10px 15px;
    font-size: 14px;
    opacity: 0.8;
    color: var(--color-text-1);
  }
  .logo-icon {
    height: 12px;
  }
  .logo-text {
    padding-left: 3px;
    font-weight: 500;
    transition: all 0.2s linear;
    &:hover {
      text-decoration: underline;
    }
  }
  .task-center {
    cursor: pointer;
    .task-down-icon {
      transition: all 0.2s linear;
    }
    &:hover {
      .task-down-icon {
        transform: rotate(180deg);
      }
    }
  }
  .download-box {
    background: #fff;
  }

  // 消息提醒按钮交互效果
  .message-notification-btn {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;

    // 点击按下效果
    &:active {
      transform: scale(0.95);
    }

    // 悬停效果
    &:hover {
      background-color: var(--color-primary-light-1);
      color: var(--color-primary);
      box-shadow: 0 2px 8px rgba(var(--arcoblue-6), 0.2);
    }

    // 加载状态样式
    &.btn-loading {
      background-color: var(--color-primary-light-2);
      color: var(--color-primary);

      // 轻微的脉冲动画
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
          circle,
          rgba(var(--arcoblue-6), 0.1) 0%,
          transparent 70%
        );
        animation: pulse-effect 1.5s ease-in-out infinite;
        border-radius: 50%;
      }
    }

    // 按钮图标动画
    .arco-icon {
      transition: transform 0.2s ease-in-out;
    }

    &:active .arco-icon {
      transform: scale(0.9);
    }
  }

  // 下载中心按钮交互效果
  .download-center-btn {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;

    // 点击按下效果
    &:active {
      transform: scale(0.95);
    }

    // 悬停效果
    &:hover {
      background-color: var(--color-primary-light-1);
      color: var(--color-primary);
      box-shadow: 0 2px 8px rgba(var(--arcoblue-6), 0.2);
    }

    // 加载状态样式
    &.btn-loading {
      background-color: var(--color-primary-light-2);
      color: var(--color-primary);

      // 轻微的脉冲动画
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
          circle,
          rgba(var(--arcoblue-6), 0.1) 0%,
          transparent 70%
        );
        animation: pulse-effect 1.5s ease-in-out infinite;
        border-radius: 50%;
      }
    }

    // 按钮图标动画
    .arco-icon {
      transition: transform 0.2s ease-in-out;
    }

    &:active .arco-icon {
      transform: scale(0.9);
    }
  }

  // 脉冲动画关键帧
  @keyframes pulse-effect {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(1.2);
    }
  }
</style>

<style lang="less">
  .message-popover {
    .arco-popover-content {
      margin-top: 0;
    }
  }
</style>

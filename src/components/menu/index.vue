<script lang="tsx">
  import { defineComponent, ref, h, compile, computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute, useRouter } from 'vue-router';
  import type { RouteMeta } from 'vue-router';
  import { useAppStore, useDataCacheStore } from '@/store';
  import { listenerRouteChange } from '@/utils/route-listener';
  import { openWindow, regexUrl } from '@/utils';
  import { AppRouteRecordRaw } from '@/router/routes/types';
  import useMenuTree from './use-menu-tree';

  export default defineComponent({
    emit: ['collapse'],
    props: {
      mode: {
        type: String,
        default: 'vertical',
      },
      isSubMenu: {
        type: Boolean,
        default: false,
      },
      isNeedCollapsed: {
        type: Boolean,
        default: true,
      },
    },
    setup(props) {
      const { t } = useI18n();
      const appStore = useAppStore();
      const router = useRouter();
      const route = useRoute();
      const { menuTree, subMenuTree } = useMenuTree();
      const collapsed = computed({
        get() {
          if (appStore.device === 'desktop' && props.isNeedCollapsed)
            return appStore.menuCollapse;
          return false;
        },
        set(value: boolean) {
          appStore.updateSettings({ menuCollapse: value });
        },
      });

      const topMenu = computed(() => appStore.topMenu);
      const openKeys = ref<string[]>([]);
      const selectedKey = ref<string[]>([]);

      const goto = (item: AppRouteRecordRaw) => {
        // Open external link
        if (item.meta?.link || regexUrl.test(item.path)) {
          openWindow((item.meta?.link || item.path) as string, {
            target: '_blank',
          });
          return;
        }
        // Eliminate external link side effects
        const { hideInMenu, activeMenu } = item.meta as RouteMeta;
        if (route.name === item.name && !hideInMenu && !activeMenu) {
          selectedKey.value = [item.name as string];
          return;
        }
        // Trigger router change
        router.push({
          name: item.name,
        });
      };
      const findMenuOpenKeys = (target: string) => {
        const result: string[] = [];
        let isFind = false;
        const backtrack = (item: AppRouteRecordRaw, keys: string[]) => {
          if (item.name === target) {
            isFind = true;
            result.push(...keys);
            return;
          }
          if (item.children?.length) {
            item.children.forEach((el) => {
              backtrack(el, [...keys, el.name as string]);
            });
          }
        };
        menuTree.value.forEach((el: AppRouteRecordRaw) => {
          if (isFind) return; // Performance optimization
          backtrack(el, [el.name as string]);
        });
        return result;
      };
      listenerRouteChange((newRoute) => {
        const { requiresAuth, activeMenu, hideInMenu } = newRoute.meta;
        if (!hideInMenu || activeMenu) {
          const menuOpenKeys = findMenuOpenKeys(
            (activeMenu || newRoute.name) as string
          );

          const keySet = new Set([...menuOpenKeys, ...openKeys.value]);
          openKeys.value = [...keySet];

          selectedKey.value = [
            activeMenu || menuOpenKeys[menuOpenKeys.length - 1],
          ];
        }
      }, true);
      const setCollapse = (val: boolean) => {
        if (appStore.device === 'desktop')
          appStore.updateSettings({ menuCollapse: val });
      };
      const dataCache = useDataCacheStore();
      const renderSubMenu = () => {
        function travel(_route: AppRouteRecordRaw[], nodes = []) {
          if (_route) {
            _route.forEach((element) => {
              let icon = element?.meta?.icon ? `<${element?.meta?.icon}/>` : ``;
              // 优先支持 iconFont 配置
              if (element?.meta?.iconFont) {
                icon = `<IconFont type="${element?.meta?.iconFont}"/>`;
              }
              const node =
                element?.children &&
                (element?.children.length > 1 || element?.meta?.showAll) ? (
                  <a-sub-menu
                    key={element?.name}
                    popup-max-height={false}
                    v-slots={{
                      icon: () => h(compile(icon)),
                      title: () =>
                        h(
                          <a-badge
                            offset={[8, 0]}
                            count={
                              (element.name === 'clue' &&
                              dataCache.new_chat_message_num
                                ? 1
                                : 0) ||
                              (element.name === 'content-collect' &&
                              dataCache.is_exists_new_collect_content
                                ? 1
                                : 0)
                            }
                            dot
                          >
                            {t(element?.meta?.locale || '')}
                          </a-badge>
                        ),
                    }}
                  >
                    {travel(element?.children)}
                  </a-sub-menu>
                ) : (
                  <a-menu-item
                    key={element?.name}
                    v-slots={{ icon: () => h(compile(icon)) }}
                    onClick={() => goto(element)}
                  >
                    {element.meta?.link || regexUrl.test(element.path) ? (
                      <a
                        class="initA"
                        href={(element.meta?.link || element.path) as string}
                        target="_blank"
                        style="pointer-events: none;"
                      >
                        {
                          // 暂时无法解决点击事件和a标签的跳转同时触发的问题
                        }
                        {t(element?.meta?.locale || '')}
                        {element?.meta?.suffix ? (
                          <span style="color:rgba(255,0,0,0.7);font-size:12px;">
                            {element?.meta?.suffix}
                          </span>
                        ) : null}
                      </a>
                    ) : (
                      <a-badge
                        offset={[8, 0]}
                        count={
                          (element.name === 'sale-clue-list' &&
                          dataCache.new_chat_message_num
                            ? 1
                            : 0) ||
                          (element.name === 'content-collect' &&
                          dataCache.is_exists_new_collect_content
                            ? 1
                            : 0)
                        }
                        dot
                      >
                        <router-link
                          className="initA"
                          to={{ name: element.name }}
                          style="pointer-events: none;"
                        >
                          {t(element?.meta?.locale || '')}
                          {element?.meta?.suffix ? (
                            <span style="color:rgba(255,0,0,0.7);font-size:12px;">
                              {element?.meta?.suffix}
                            </span>
                          ) : null}
                        </router-link>
                      </a-badge>
                    )}
                  </a-menu-item>
                );
              nodes.push(node as never);
            });
          }
          return nodes;
        }
        if (props.isSubMenu) {
          return travel(subMenuTree.value);
        }
        return travel(menuTree.value);
      };
      return () => (
        <a-menu
          mode={props.mode}
          v-model:collapsed={collapsed.value}
          v-model:open-keys={openKeys.value}
          show-collapse-button={
            appStore.device !== 'mobile' && props.isNeedCollapsed
          }
          auto-open={false}
          selected-keys={selectedKey.value}
          auto-open-selected={true}
          accordion={true}
          level-indent={18}
          class="dmenu"
          popup-max-height={false}
          onCollapse={setCollapse}
        >
          {renderSubMenu()}
        </a-menu>
      );
    },
  });
</script>

<style lang="less" scoped>
  .dmenu {
    height: 100%;
    width: 100%;
    background-color: transparent;

    :deep(.arco-menu-inner) {
      & > .arco-menu-item,
      & > .arco-menu-inline {
        // background-color: rgb(var(--arcoblue-1)) !important;
        margin-bottom: 4px !important;
      }
      .arco-menu-inline-header {
        display: flex;
        align-items: center;
        background-color: transparent;
        font-weight: 700;
      }
      .arco-icon {
        &:not(.arco-icon-down) {
          font-size: 18px;
        }
      }
      .arco-menu-pop-header,
      .arco-menu-item {
        background-color: transparent;
        font-weight: 700;
        margin-left: 0;
        line-height: 36px;
        border-radius: 4px;
        transition: all 0.2s ease-in-out;
        border-radius: 8px;
        margin-right: 2px;
        // margin-left: 12px;
        &:hover {
          // background: rgb(var(--arcoblue-3));
          background: var(--color-bg-1);
          box-shadow: 1px 1px 5px var(--color-border);
        }
      }
      .arco-menu-inline-content {
        // background: rgb(var(--arcoblue-2));
        // background: var(--color-primary-light-1);
        .arco-menu-item {
          .arco-menu-icon {
            //display: none;
          }
        }
      }
      .arco-menu-title {
        flex: 1;
        user-select: none;
        // color: var(--color-text-1);
      }
    }
    // 纵向菜单
    &.arco-menu-vertical {
      :deep(.arco-menu-inner) {
        padding-top: 10px;
        .arco-menu-item {
          &.arco-menu-selected {
            position: relative;
            //background: var(--color-bg-2);
            // background: var(--color-primary-light-1);
            background: var(--color-bg-1);
            border-radius: var(--border-radius-large);
            box-shadow: 1px 1px 5px var(--color-border);
            transition: all 0.2s ease-in-out;
            &::before {
              position: absolute;
              content: '';
              left: 0;
              width: 6px;
              height: 100%;
              // background: rgb(var(--primary-6));
              background: rgb(69 90 247);
              // border-radius: 0 4px 4px 0;
              border-radius: 6px 0 0 6px;
            }
          }
        }
      }
    }
    // 横向菜单
    &.arco-menu-horizontal {
      padding: 12px 0 0;
      :deep(.arco-menu-inner) {
        overflow: hidden;
        padding: 0;
        height: auto;
        .arco-menu-icon {
          display: none;
        }
        .arco-menu-selected-label {
          bottom: 0px;
          left: 20px;
          right: 20px;
          display: none;
        }
        .arco-menu-pop-header {
          padding: 0 15px;
        }
      }
    }
  }
  .arco-layout-sider-collapsed {
    .dmenu {
    }
  }
  // 亮色主题的导航
  .arco-menu-light.dmenu {
    :deep(.arco-menu-inner) {
      .arco-menu-pop-header:hover {
        background-color: var(--color-bg-1);
      }
    }
  }

  .initA {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
</style>

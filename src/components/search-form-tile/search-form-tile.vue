<template>
  <a-card size="small">
    <a-form
      v-bind="$attrs"
      ref="formRef"
      :model="formData"
      label-align="right"
      class="search-form"
      auto-label-width
      @submit-success="handleSubmit"
    >
      <a-spin :loading="props.loading">
        <div style="display: flex; flex-wrap: nowrap; align-items: stretch">
          <div
            style="flex: 1"
            class="search-item-box"
            :class="{ more: !collapsed }"
          >
            <div class="inline-form-grid">
              <slot name="formItemGroup"></slot>
            </div>
          </div>
          <div
            style="
              flex: 0 auto;
              text-align: right;
              display: flex;
              align-items: stretch;
              position: relative;
            "
          >
            <a-divider
              style="height: auto; margin: 0 12px"
              direction="vertical"
            />
            <a-space :direction="btnGroupDirection" :size="4">
              <slot name="formBtnGroup">
                <a-button v-if="showResetBtn" type="text" @click="resetHandler">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置条件
                </a-button>
                <a-button v-if="showMore" @click="collapsed = !collapsed">
                  <template #icon>
                    <icon-down v-if="collapsed" />
                    <icon-up v-else />
                  </template>
                  更多条件
                </a-button>
              </slot>
            </a-space>
            <!--more-->
            <a-link
              v-if="showMore"
              class="search-more-btn"
              @click="collapsed = !collapsed"
            >
              <span v-if="collapsed">更多选项</span>
              <span v-else>收起</span>
              <template #icon>
                <icon-down v-if="collapsed" />
                <icon-up v-else />
              </template>
            </a-link>
          </div>
        </div>
      </a-spin>
    </a-form>
  </a-card>
</template>

<script lang="ts" setup>
  import {
    onMounted,
    ref,
    computed,
    reactive,
    watch,
    isRef,
    isReactive,
    useSlots,
  } from 'vue';

  const emits = defineEmits(['search', 'update:formData']);
  const props = defineProps({
    formData: {
      type: Object,
      default: () => {},
    },
    loading: {
      default: false,
      type: Boolean,
    },
    showResetBtn: {
      default: true,
      type: Boolean,
    },
    // 需要保留原值的key
    continueKey: {
      type: Array,
      default: () => ['month'],
    }, // 需要保留原值的key
    maxItems: {
      type: Number,
      default: 4,
    },
    resetAction: {
      type: Function,
      default: null,
    },
    // 表单默认值
    getDefaultFormData: {
      type: Function,
      default: () => ({}),
    },
  });
  const collapsed = ref(true);
  type slotType = {
    formItemGroup?: (arg: any) => [];
  };
  const slots: slotType = useSlots();
  // 提交
  const formRef = ref();
  const handleSubmit = () => {
    emits('search');
  };

  const resetHandler = async () => {
    if (props.resetAction) {
      props.resetAction();
    } else {
      const initFormData = props.getDefaultFormData();
      let keys = Object.keys(initFormData);
      // 需要定义继承原值的Key 不能因为处理缓存导致默认值也被重置
      let continueKeys = [...props.continueKey];
      await keys.map((key: any) => {
        if (continueKeys.includes(key)) {
          initFormData[key] = props.formData[key];
        }
        return null;
      });
      Object.assign(props.formData, initFormData);
      handleSubmit();
    }
  };

  // form 输入项的数量
  const searchItemLength = computed(() => {
    return (
      slots.formItemGroup?.('')?.reduce((sum: number, item: any) => {
        let num = 0;
        if (item.type?.name === 'FormItem') {
          num = 1;
        } else if (item.children?.filter) {
          num = item.children?.filter(
            (citem: any) => citem.type?.name === 'FormItem'
          ).length;
        }
        return sum + num;
      }, 0) || 0
    );
  });

  // 是否显示更多按钮
  const showMore = computed(() => {
    return searchItemLength.value > 10;
  });
  // 单行还是双行模式
  const btnGroupDirection = computed(() => {
    return searchItemLength.value < props.maxItems ? 'horizontal' : 'vertical';
  });

  defineExpose({
    resetHandler,
  });
</script>

<style scoped lang="less">
  .search-form {
    :deep(
        .arco-form-auto-label-width
          .arco-form-item-label-col
          > .arco-form-item-label
      ) {
      width: 100%;
      text-align: justify;
      text-align-last: justify;
    }
  }
  .search-item-box {
    overflow: hidden;
    &.more {
      :deep(.arco-form-item) {
        display: flex;
      }
    }
  }
  .search-more-btn {
    position: absolute;
    right: 0;
    bottom: -15px;
  }
  .inline-form-grid {
    //display: grid;
    //grid-gap: 8px 10px;
    //grid-template-columns: repeat(3, minmax(0, 1fr));
    :deep(.arco-form-item) {
      display: none;
    }
    :deep(.arco-form-item:nth-of-type(-n + 10)) {
      display: flex;
    }
    :deep(.arco-picker) {
      width: 100%;
    }
    :deep(.arco-form-item:last-of-type) {
      margin-bottom: 0;
    }
  }
</style>

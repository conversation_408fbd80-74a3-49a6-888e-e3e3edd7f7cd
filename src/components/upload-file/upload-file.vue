<template>
  <div>
    <a-upload
      v-model:file-list="fileList"
      :accept="fileType"
      action="/api/uploadFile"
      :data="sendParams"
      :headers="headers"
      :limit="limit"
      list-type="picture-card"
      image-preview
      :multiple="multiple"
      :disabled="disabled"
      v-bind="$attrs"
      @success="handleSuccess"
      @before-remove="beforeRemove"
      @error="uploadError"
    >
    </a-upload>
  </div>
</template>

<script lang="ts" setup>
  import { getToken } from '@/utils/auth';
  import { onMounted, ref, watch } from 'vue';
  import { FileItem, Message } from '@arco-design/web-vue';
  import { isArray, toString } from 'lodash';

  const props = defineProps({
    modelValue: {
      type: [Array, String],
      default: () => [],
    },
    fileType: {
      type: String,
      default: '',
    },
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    multiple: {
      type: Boolean,
      default: () => true,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    limit: {
      type: Number,
      default: () => 10,
    },
  });

  const headers = { Authorization: getToken() };
  const emits = defineEmits(['update:modelValue', 'update:list', 'success']);
  const fileList = ref<FileItem[]>([]);

  onMounted(() => {
    if (props.multiple) {
      if (isArray(props.modelValue)) {
        fileList.value = props.modelValue.map(
          (item: string, index: number) =>
            ({
              uid: toString(index),
              status: 'done',
              url: item,
            } as FileItem)
        );
      }
    } else if (props.modelValue) {
      fileList.value = [
        {
          uid: '1',
          status: 'done',
          url: props.modelValue as string,
        },
      ];
    }
  });

  function handleSuccess(fileItem: FileItem) {
    if (fileItem.response?.code === 0) {
      fileItem.url =
        fileItem.response.data?.thumb_url || fileItem.response.data?.url;
      emits(
        'update:modelValue',
        props.multiple
          ? [...props.modelValue, fileItem.response.data.url]
          : fileItem.response.data.url
      );
      setTimeout(() => {
        emits(
          'update:list',
          fileList.value.filter((item) => item.status === 'done')
        );
      }, 500);
      emits('success', fileItem);
    } else {
      fileList.value = fileList.value.filter(
        (item: FileItem) => item.uid !== fileItem.uid
      );
      Message.error(fileItem.response?.msg || '上传失败，请重试');
    }
  }
  function uploadError(fileItem: FileItem) {
    fileList.value = fileList.value.filter(
      (item: FileItem) => item.uid !== fileItem.uid
    );
    if (fileItem.response?.code === 1) {
      Message.error(fileItem.response?.msg || '上传失败，请重试');
    } else {
      Message.error('上传失败，请重试');
    }
  }

  const beforeRemove = (file: FileItem) => {
    return new Promise((resolve, reject) => {
      if (file.status === 'done') {
        if (props.multiple) {
          if (isArray(props.modelValue)) {
            emits(
              'update:modelValue',
              props.modelValue.filter((item: string) => item !== file.url)
            );
          }
        } else {
          emits('update:modelValue', '');
        }
      }
      resolve(true);
      setTimeout(() => {
        emits(
          'update:list',
          fileList.value.filter((item) => item.status === 'done')
        );
      }, 500);
    });
  };
</script>

<style lang="less" scoped></style>

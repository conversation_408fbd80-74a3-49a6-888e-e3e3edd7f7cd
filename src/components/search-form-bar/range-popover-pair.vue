<template>
  <div
    class="range-popover-wrapper"
    :class="{ active: hasValue }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="range-trigger">
      {{ label }}
      <icon-down class="range-trigger-icon" style="margin-left: 4px" />
    </div>
    <div v-show="visible" class="range-panel-pop" @click.stop>
      <div class="range-panel">
        <template v-for="item in ranges" :key="item.label">
          <a-button
            class="range-btn"
            :class="{ selected: isSelected(item) }"
            type="button"
            @click="selectRange(item)"
          >
            {{ item.label }}
          </a-button>
        </template>
        <a-button
          class="range-btn"
          :class="{ selected: showCustom }"
          type="button"
          size="mini"
          @click="showCustom = true"
        >
          自定义
        </a-button>
      </div>
      <div v-if="showCustom" class="custom-input">
        <a-input-number
          v-model="customMin"
          placeholder="自定义"
          class="custom-input-number"
          size="mini"
        />
        <span>至</span>
        <a-input-number
          v-model="customMax"
          size="mini"
          placeholder="自定义"
          :min="Number(props.min || 0)"
          class="custom-input-number"
        />
        <a-button
          size="mini"
          type="primary"
          class="custom-confirm-btn"
          @click="confirmCustom"
          >确定</a-button
        >
        <a-button size="mini" class="custom-reset-btn" @click="resetRange"
          >重置</a-button
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
  import { IconDown } from '@arco-design/web-vue/es/icon';

  const props = defineProps<{
    min: number | undefined;
    max: number | undefined;
    label: string;
    ranges: Array<{ label: string; min?: number; max?: number }>;
  }>();
  const emit = defineEmits(['update:min', 'update:max', 'change']);

  const visible = ref(false);
  const showCustom = ref(false);
  const customMin = ref(props.min);
  const customMax = ref(props.max);

  watch(
    () => [props.min, props.max],
    ([min, max]) => {
      customMin.value = min;
      customMax.value = max;
    }
  );

  const hasValue = computed(() => props.min || props.max);

  function isSelected(item: { min?: number; max?: number }) {
    // 如果自定义，则不进行判断
    if (showCustom.value) {
      return false;
    }
    return props.min === item.min && props.max === item.max;
  }

  function selectRange(item: { min?: number; max?: number }) {
    emit('update:min', item.min);
    emit('update:max', item.max);
    emit('change', { min: item.min, max: item.max });
    showCustom.value = false;
  }

  function confirmCustom() {
    emit('update:min', customMin.value);
    emit('update:max', customMax.value);
    emit('change', { min: customMin.value, max: customMax.value });
    showCustom.value = false;
    visible.value = false;
  }

  function resetRange() {
    emit('update:min', undefined);
    emit('update:max', undefined);
    emit('change', { min: undefined, max: undefined });
    showCustom.value = false;
    visible.value = false;
  }

  function togglePanel() {
    visible.value = true;
  }

  const timer = ref<number | null>(null);
  function handleMouseEnter() {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
    // 鼠标移入200ms后显示 不满200ms不触发展示
    timer.value = window.setTimeout(() => {
      visible.value = true;
    }, 200);
  }
  function handleMouseLeave() {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
    // 鼠标移出200ms后隐藏 不满200ms不隐藏
    timer.value = window.setTimeout(() => {
      visible.value = false;
    }, 200);
  }
  // 点击外部关闭
  function handleClickOutside(e: MouseEvent) {
    const wrapper = document.querySelector('.range-popover-wrapper');
    if (wrapper && !wrapper.contains(e.target as Node)) {
      visible.value = false;
      showCustom.value = false;
    }
  }

  onMounted(() => {
    document.addEventListener('mousedown', handleClickOutside);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('mousedown', handleClickOutside);
  });
</script>

<style lang="less" scoped>
  /* 组件容器 */
  .range-popover-wrapper {
    display: inline-block;
    position: relative;
    &.active {
      color: rgb(var(--primary-6));
    }

    &:hover {
      background: var(--color-bg-1);
      .range-trigger {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: var(--color-bg-1);
        .range-trigger-icon {
          transform: rotate(180deg);
        }
        &::before {
          opacity: 1;
        }
      }
    }
  }

  /* 触发器样式 */
  .range-trigger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 4px 8px;
    height: 32px;
    border-radius: 8px 8px 0 0;
    transition: all 0.2s ease;
    font-size: 14px;
    user-select: none;
    z-index: 1000;
    position: relative;
    &::before {
      opacity: 0;
      display: block;
      content: '';
      width: 100%;
      height: 12px;
      background: var(--color-bg-1);
      position: absolute;
      bottom: -8px;
      left: 0;
      z-index: 100;
      transition: opacity 0.2s ease;
      pointer-events: none;
    }
    .range-trigger-icon {
      transition: transform 0.2s ease;
    }
  }

  /* 弹出面板容器 */
  .range-panel-pop {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    padding: 0;
    border-radius: 0 4px 4px 4px;
    min-width: 320px;
    width: max-content;
    animation: fadeIn 0.15s ease-out;
    z-index: 100;
    border-radius: 12px;

    /* 按钮面板 */
    .range-panel {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding: 10px 10px 8px;
      background: var(--color-bg-1);
      border-radius: 0 8px 8px 8px;
      /* 范围按钮 */
      .range-btn {
        height: 24px;
        min-width: 48px;
        padding: 0 8px;
        border: none;
        outline: none;
        background: var(--color-bg-1);
        color: #333;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.15s ease;
        margin: 0;
        border-radius: 4px;

        // 选中状态
        &.selected {
          background: rgb(var(--primary-6));
          color: #fff;
        }

        // 悬停状态
        &:not(.selected):hover {
          color: rgb(var(--primary-6));
        }
      }
    }

    /* 自定义输入区域 */
    .custom-input {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 12px 10px;
    }

    // 数字输入框
    .custom-input-number {
      width: 100px;
    }
  }

  /* 确认按钮 */
  // .custom-confirm-btn {
  // background: rgb(var(--primary-6));
  // color: #fff;
  // &:hover {
  // background: rgb(var(--primary-5));
  // }
  // }

  /* 重置按钮 */
  .custom-reset-btn {
    margin-left: 4px;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>

<template>
  <div
    class="range-popover-wrapper"
    :class="{ active: hasValue }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="range-trigger">
      {{ label || '排序' }}
      <icon-down class="range-trigger-icon" style="margin-left: 4px" />
    </div>
    <div v-show="visible" class="range-panel-pop">
      <div class="sort-panel">
        <template v-for="field in fields" :key="field.value">
          <a-button
            class="sort-btn"
            :class="{ selected: isSelected(field) }"
            type="button"
          >
            <span class="sort-label" @click="selectField(field, 'desc')">
              {{ field.label }}
            </span>
            <span class="sort-icons">
              <a-tooltip content="降序">
                <icon-sort-ascending
                  class="sort-icon"
                  :class="{
                    active: isSelected(field) && orderType === 'desc',
                  }"
                  size="16"
                  @click="selectField(field, 'desc')"
                />
              </a-tooltip>
              <a-tooltip content="升序">
                <icon-sort-descending
                  class="sort-icon"
                  :class="{ active: isSelected(field) && orderType === 'asc' }"
                  size="16"
                  @click="selectField(field, 'asc')"
                />
              </a-tooltip>
            </span>
          </a-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
  import { IconDown } from '@arco-design/web-vue/es/icon';

  const props = defineProps<{
    fields: Array<{ label: string; value: string }>;
    orderField: string | undefined;
    orderType: 'asc' | 'desc' | undefined;
    label?: string;
  }>();

  const emit = defineEmits(['update:orderField', 'update:orderType', 'change']);

  const visible = ref(false);

  function isSelected(field: { value: string }) {
    return props.orderField === field.value;
  }

  // 判断是否有值
  function hasValue() {
    return props.orderField !== undefined;
  }

  function selectField(field: { value: string }, orderType: 'asc' | 'desc') {
    if (props.orderField === field.value) {
      // 切换顺序
      const newOrder = orderType;
      emit('update:orderType', newOrder);
      emit('change', { field: field.value, order: newOrder });
    } else {
      emit('update:orderField', field.value);
      emit('update:orderType', orderType);
      emit('change', { field: field.value, order: orderType });
    }
    // visible.value = false;
  }

  const timer = ref<number | null>(null);
  function handleMouseEnter() {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
    // 鼠标移入200ms后显示 不满200ms不触发展示
    timer.value = window.setTimeout(() => {
      visible.value = true;
    }, 200);
  }

  function handleMouseLeave() {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
    // 鼠标移出200ms后隐藏 不满200ms不隐藏
    timer.value = window.setTimeout(() => {
      visible.value = false;
    }, 200);
  }
</script>

<style lang="less" scoped>
  .range-popover-wrapper {
    display: inline-block;
    position: relative;
    &.active {
      color: rgb(var(--primary-6));
    }
    &:hover {
      .range-trigger {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: var(--color-bg-1);
        &::before {
          opacity: 1;
        }
        .range-trigger-icon {
          transform: rotate(180deg);
        }
      }
    }
  }
  .range-trigger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 4px 8px;
    height: 32px;
    border-radius: 8px 8px 0 0;
    transition: all 0.2s ease;
    font-size: 14px;
    user-select: none;
    z-index: 1000;
    position: relative;
    .range-trigger-icon {
      transition: transform 0.2s ease;
    }
    &::before {
      opacity: 0;
      display: block;
      content: '';
      width: 100%;
      height: 12px;
      background: var(--color-bg-1);
      position: absolute;
      bottom: -8px;
      left: 0;
      z-index: 100;
      transition: opacity 0.2s ease;
      pointer-events: none;
    }
  }
  .range-panel-pop {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    padding: 0;
    border-radius: 0 4px 4px 4px;
    min-width: 220px;
    width: max-content;
    animation: fadeIn 0.15s ease-out;
    z-index: 100;
    border-radius: 12px;
    .sort-panel {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding: 5px 5px;
      background: var(--color-bg-1);
      border-radius: 0 8px 8px 8px;
      .sort-btn {
        border: none;
        outline: none;
        background: var(--color-bg-1);
        color: #333;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.15s ease;
        padding: 0 5px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        .sort-label {
          display: flex;
          align-items: center;
          height: 24px;
          padding: 0 8px;
          border-radius: var(--border-radius-large);
        }
        .sort-icons {
          margin-left: 5px;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        &.selected {
          color: var(--color-bg-1);
          .sort-label {
            background: rgb(var(--primary-6));
            color: var(--color-bg-1);
            border-radius: var(--border-radius-large);
          }
        }
        &:not(.selected):hover {
          color: rgb(var(--primary-6));
        }
      }
    }
  }
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .sort-icon {
    color: #c0c6cf;
    &.active {
      color: rgb(var(--primary-6));
    }
  }
</style>

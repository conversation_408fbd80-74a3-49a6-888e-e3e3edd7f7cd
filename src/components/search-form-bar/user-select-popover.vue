<template>
  <div
    class="operator-popover-wrapper"
    :class="{ active: hasValue }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="operator-trigger" @click="triggerUserSelectPopover">
      {{ label }}
      <icon-down class="operator-trigger-icon" style="margin-left: 4px" />
    </div>
    <div v-show="visible" class="operator-panel-pop" @click.stop>
      <!-- Tab navigation -->
      <!-- <div class="tab-navigation">
        <div
          class="tab-item"
          :class="{ active: activeTab === '运营团队' }"
          @click="activeTab = '运营团队'"
        >
          运营团队
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === '运营人' }"
          @click="activeTab = '运营人'"
        >
          运营人
        </div>
      </div> -->
      <!-- Search input -->
      <div class="search-container">
        <a-input-search
          v-model="searchValue"
          :placeholder="'搜索人员名称'"
          allow-clear
          size="small"
        />
      </div>

      <!-- Checkbox list -->
      <a-spin :loading="loading" style="width: 100%">
        <div class="checkbox-list">
          <div class="checkbox-item">
            <a-checkbox v-model="selectAll" :indeterminate="indeterminate"
              >全部</a-checkbox
            >
          </div>
          <div class="checkbox-item">
            <a-checkbox v-model="selectNone">无</a-checkbox>
          </div>
          <div
            v-for="item in filteredOperators"
            :key="item.id"
            class="checkbox-item"
            @change="handleOperatorSelect(item)"
          >
            <a-checkbox v-model="item.selected">
              <div class="checkbox-item-text">
                {{ item.name }}
                <span class="checkbox-item-text-account"
                  >({{ item.account_name }})</span
                >
              </div>
            </a-checkbox>
          </div>
        </div>
      </a-spin>

      <!-- Action buttons -->
      <div class="action-buttons">
        <a-button size="mini" @click="resetSelection">
          <template #icon>
            <icon-refresh />
          </template>
          重置
        </a-button>
        <a-space>
          <a-button size="mini" @click="handleCancel">取消</a-button>
          <a-button type="primary" size="mini" @click="handleConfirm"
            >确定</a-button
          >
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    computed,
    watch,
    onMounted,
    onBeforeUnmount,
    setInterval,
  } from 'vue';
  import { IconDown } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';

  const props = defineProps<{
    modelValue: Array<string | number>;
    label: string;
    operators?: Array<{ id: string | number; name: string }>;
    sendParams?: Record<string, any>;
  }>();

  const emit = defineEmits([
    'update:modelValue',
    'change',
    'update:selectedUsers',
  ]);

  const visible = ref(false);
  const activeTab = ref('人员');
  const searchValue = ref('');
  const loading = ref(false);
  const operatorList = ref<any[]>([]);
  const showTimeout = ref<number | null>(null);

  // Select all state
  const selectAll = ref(false);
  const indeterminate = ref(false);
  const selectNone = ref(false);

  const hasValue = computed(
    () => props.modelValue && props.modelValue.length > 0
  );

  // Initialize selected state based on modelValue
  function initSelectedState() {
    if (props.modelValue) {
      operatorList.value.forEach((op) => {
        op.selected = props.modelValue.includes(op.id);
      });
    }
  }

  function handleConfirm() {
    const selectedUsers = operatorList.value.filter((op) => op.selected);
    const selectedIds = selectedUsers.map((op) => op.id);

    emit('update:modelValue', selectedIds);
    emit('change', selectedIds);
    emit('update:selectedUsers', selectedUsers);
    visible.value = false;
  }

  function updateSelectAllState() {
    const selectedCount = operatorList.value.filter((op) => op.selected).length;

    if (selectedCount === 0) {
      selectAll.value = false;
      indeterminate.value = false;
      selectNone.value = true;
    } else if (selectedCount === operatorList.value.length) {
      selectAll.value = true;
      indeterminate.value = false;
      selectNone.value = false;
    } else {
      selectAll.value = false;
      indeterminate.value = true;
      selectNone.value = false;
    }
  }

  // Fetch users from API
  async function fetchUsers() {
    loading.value = true;
    try {
      const res = await request('/api/user/userList', {
        ...props.sendParams,
        page: 1,
        pageSize: 9999,
      });
      const users = res.data.data.map((item: any) => ({
        id: item.id,
        name: item.user_name,
        account_name: item.account_name,
        selected: false,
      }));
      operatorList.value = users;
      initSelectedState();
      updateSelectAllState();
    } catch (error) {
      console.error('Failed to fetch users:', error);
    } finally {
      loading.value = false;
    }
  }

  // Watch for external changes to modelValue
  watch(
    () => props.modelValue,
    () => {
      initSelectedState();
      updateSelectAllState();
    },
    { immediate: true }
  );

  // Watch for changes to sendParams and refetch data
  watch(
    () => props.sendParams,
    () => {
      fetchUsers();
    },
    { deep: true }
  );

  // Filtered operators based on search
  const filteredOperators = computed(() => {
    if (!searchValue.value) return operatorList.value;
    return operatorList.value.filter((op) =>
      op.name.toLowerCase().includes(searchValue.value.toLowerCase())
    );
  });

  // Watch selectAll changes
  watch(selectAll, (val) => {
    if (val) {
      operatorList.value.forEach((op) => {
        op.selected = true;
      });
      selectNone.value = false;
      indeterminate.value = false;
    } else if (!indeterminate.value) {
      operatorList.value.forEach((op) => {
        op.selected = false;
      });
    }
  });

  // Watch selectNone changes
  watch(selectNone, (val) => {
    if (val) {
      operatorList.value.forEach((op) => {
        op.selected = false;
      });
      selectAll.value = false;
      indeterminate.value = false;
    }
  });

  function handleOperatorSelect() {
    updateSelectAllState();
  }

  function triggerUserSelectPopover() {
    // visible.value = !visible.value;
    visible.value = true;
  }

  function handleCancel() {
    // Reset to initial selection state
    initSelectedState();
    visible.value = false;
  }

  const timer = ref<number | null>(null);
  function handleMouseEnter() {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
    // 鼠标移入200ms后显示 不满200ms不触发展示
    timer.value = window.setTimeout(() => {
      visible.value = true;
    }, 200);
  }
  function handleMouseLeave() {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
    // 鼠标移出200ms后隐藏 不满200ms不隐藏
    timer.value = window.setTimeout(() => {
      visible.value = false;
      handleCancel();
    }, 200);
  }

  // Handle clicking outside the popover
  function handleClickOutside(e: MouseEvent) {
    const wrapper = document.querySelector('.operator-popover-wrapper');
    if (wrapper && !wrapper.contains(e.target as Node)) {
      visible.value = false;
    }
  }

  function resetSelection() {
    // Clear all selections
    operatorList.value.forEach((op) => {
      op.selected = false;
    });
    selectAll.value = false;
    indeterminate.value = false;
    selectNone.value = true;
  }

  onMounted(() => {
    document.addEventListener('mousedown', handleClickOutside);
    // Use provided operators if available, otherwise fetch from API
    if (props.operators && props.operators.length > 0) {
      operatorList.value = props.operators.map((op) => ({
        ...op,
        selected: false,
      }));
      initSelectedState();
      updateSelectAllState();
    } else {
      fetchUsers();
    }
  });

  onBeforeUnmount(() => {
    document.removeEventListener('mousedown', handleClickOutside);
  });
</script>

<style lang="less" scoped>
  /* Component container */
  .operator-popover-wrapper {
    display: inline-block;
    position: relative;
    &.active {
      color: rgb(var(--primary-6));
    }

    &:hover {
      background: var(--color-bg-1);
      .operator-trigger {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background: var(--color-bg-1);
      }
      .operator-trigger-icon {
        transform: rotate(180deg);
      }
    }
  }

  /* Trigger button */
  .operator-trigger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 4px 12px;
    height: 32px;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 14px;
    user-select: none;
    z-index: 1000;
    position: relative;
    .operator-trigger-icon {
      transition: transform 0.2s ease;
    }
  }

  /* Popup panel */
  .operator-panel-pop {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: var(--color-bg-1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    padding: 0;
    border-radius: 0 4px 4px 4px;
    min-width: 220px;
    width: 220px;
    animation: fadeIn 0.15s ease-out;
    z-index: 100;
    border-radius: 12px;
    overflow: hidden;
    margin-top: 5px;
  }

  /* Tab navigation */
  .tab-navigation {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f5f5f5;

    .tab-item {
      padding: 12px 16px;
      cursor: pointer;
      font-size: 14px;

      &.active {
        color: rgb(var(--primary-6));
        background-color: #fff;
        border-bottom: 2px solid rgb(var(--primary-6));
      }
    }
  }

  /* Search container */
  .search-container {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  /* Checkbox list */
  .checkbox-list {
    padding: 5px 16px;
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .checkbox-item {
    border-radius: 6px;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    cursor: pointer;
    :deep(.arco-checkbox) {
      width: 100%;
      padding: 6px 8px;
    }
    .checkbox-item-text {
      display: inline-block;
      width: 100%;
      height: 100%;
    }

    &:hover {
      background: #f5f7fa;
    }
  }

  /* Action buttons */
  .action-buttons {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    padding: 10px;
    border-top: 1px solid #f0f0f0;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>

<template>
  <div
    class="search-form-section"
    :class="{
      'no-bottom-margin': noBottomMargin,
    }"
  >
    <div class="search-header">
      <div class="search-main">
        <a-radio-group
          v-if="showContentType"
          v-model="formModel.type"
          type="button"
          @change="handleSearch"
        >
          <a-radio value="">全部</a-radio>
          <a-radio value="video">视频</a-radio>
          <a-radio value="image_text">图文</a-radio>
          <a-radio value="video_image">混合</a-radio>
        </a-radio-group>
        <a-input-search
          v-if="config.basicSearch"
          v-model="formModel[config.basicSearch.field]"
          :placeholder="config.basicSearch.placeholder"
          allow-clear
          style="width: 220px"
          class="basic-search-input"
          @search="handleSearch"
          @keydown.enter="handleSearch"
        />
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
        <a-button type="text" @click="toggleAdvancedSearch">
          <template #icon>
            <icon-filter />
          </template>
          <span v-if="showAdvancedSearch">收起</span>
          <span v-else>更多</span>
        </a-button>

        <!-- <a-button type="text" @click="resetSearch">
          <template #icon>
            <icon-refresh />
          </template>
          重置
        </a-button> -->
        <!-- <a-button
          v-if="config.advancedSearch"
          type="text"
          @click="toggleAdvancedSearch"
        >
          <template #icon>
            <icon-down v-if="!showAdvancedSearch" />
            <icon-up v-else />
          </template>
          {{ showAdvancedSearch ? '收起' : '更多' }}
        </a-button>
        <a-button type="text" @click="resetSearch">
          <template #icon>
            <icon-refresh />
          </template>
          重置
        </a-button> -->
      </div>
      <a-space>
        <slot name="right" />
      </a-space>
    </div>
    <div
      v-if="showAdvancedSearch && showAdvancedSearchList.length > 0"
      class="search-content"
    >
      <div
        class="advanced-search"
        :class="{ 'is-collapsed': !showAdvancedSearch }"
      >
        <div class="search-row">
          <template
            v-for="(item, index) in showAdvancedSearchList"
            :key="index"
          >
            <div class="search-item">
              <template v-if="item.type === 'group'">
                <div class="group-input">
                  <component
                    :is="getComponentType(item.items[0].type)"
                    v-model="formModel[item.items[0].field]"
                    v-bind="item.items[0].props || {}"
                    class="search-input"
                    @change="handleSearch"
                  />
                  <span class="group-split">~</span>
                  <component
                    :is="getComponentType(item.items[1].type)"
                    v-model="formModel[item.items[1].field]"
                    v-bind="item.items[1].props || {}"
                    class="search-input"
                    @change="handleSearch"
                  />
                </div>
              </template>
              <template v-else-if="item.type === 'sort'">
                <range-popover-sort
                  v-model:order-field="formModel.order_field"
                  v-model:order-type="formModel.order_type"
                  v-bind="item.props || {}"
                  class="search-input"
                  @change="handleSearch"
                />
              </template>
              <template v-else-if="item.type === 'requestSelect'">
                <request-select
                  v-model="formModel[item.field]"
                  v-bind="item.props || {}"
                  class="search-input"
                  @change="handleSearch"
                />
              </template>
              <template v-else-if="item.type === 'rangePopoverPair'">
                <component
                  :is="getComponentType(item.type)"
                  v-model:min="formModel[item.props.minField]"
                  v-model:max="formModel[item.props.maxField]"
                  :label="item.label"
                  :ranges="item.props.ranges"
                  @change="handleSearch"
                />
              </template>
              <template v-else-if="item.type === 'rangePicker'">
                <!-- 日期 -->
                <div class="search-date">
                  <dict-radio
                    v-model="dateKey"
                    size="normal"
                    style="margin-right: 5px"
                    :data-list="dateList"
                    @change="handleDateChange(item.field)"
                  />
                  <c-range-picker
                    v-model="formModel[item.field]"
                    style="width: 240px"
                    size="small"
                    type="daterange"
                    allow-clear
                    :need-default="false"
                    @change="handleRangeChange"
                  />
                </div>
              </template>
              <template v-else-if="item.type === 'UserSelectPopover'">
                <component
                  :is="getComponentType(item.type)"
                  v-model="formModel[item.field]"
                  v-model:selected-users="formModel[`${item.field}_users`]"
                  v-bind="item.props || {}"
                  :label="item.label"
                  class="search-input"
                  @change="handleSearch"
                />
              </template>
              <template v-else>
                <component
                  :is="getComponentType(item.type)"
                  v-model="formModel[item.field]"
                  v-bind="item.props || {}"
                  :label="item.label"
                  class="search-input"
                  @change="handleSearch"
                />
              </template>
            </div>
          </template>
        </div>
      </div>
      <!-- 把搜索的参数遍历 使用tag展示出来 支持删除 -->
      <div v-if="hasSearchParams" class="search-tags">
        <div class="tags-wrapper">
          <span class="tag-label">已选</span>
          <template v-for="[field, value] in filteredFormModel" :key="field">
            <a-tag
              v-if="canShowSearchParams(field)"
              closable
              @close="handleClose(field)"
            >
              {{ getFieldLabel(field) }}：{{ formatFieldValue(field, value) }}
            </a-tag>
          </template>
        </div>
        <a-button type="text" size="mini" @click="resetSearch">
          <template #icon>
            <icon-delete />
          </template>
          清空筛选
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, nextTick, computed } from 'vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import dayjs from 'dayjs';
  import RangePopoverPair from './range-popover-pair.vue';
  import UserSelectPopover from './user-select-popover.vue';
  import RangePopoverSort from './range-popover-sort.vue';

  interface SearchItem {
    type:
      | 'input'
      | 'inputNumber'
      | 'select'
      | 'datePicker'
      | 'rangePicker'
      | 'custom'
      | 'group'
      | 'sort';
    label: string;
    field: string;
    props?: Record<string, any>;
    component?: any;
    items?: SearchItem[];
    componentType?: string;
    sortField?: string;
  }

  interface BasicSearch {
    field: string;
    placeholder: string;
  }

  interface AdvancedSearch {
    items: SearchItem[];
  }

  interface SearchConfig {
    basicSearch?: BasicSearch;
    advancedSearch?: AdvancedSearch;
    defaultValues?: Record<string, any>;
  }

  const props = defineProps<{
    config: SearchConfig;
    modelValue: Record<string, any>;
    noBottomMargin?: boolean;
    showContentType?: boolean;
  }>();

  const dateList = [
    { label: '昨天', value: -1 },
    { label: '近7天', value: -6 },
    { label: '近30天', value: -29 },
    { label: '自定义', value: 2 },
  ];
  const dateKey = ref(2);
  const dateRange = ref([]);

  const emit = defineEmits<{
    (e: 'update:modelValue', value: Record<string, any>): void;
    (e: 'search'): void;
    (e: 'reset'): void;
    (e: 'sort', type: string, field: string): void;
  }>();

  const showAdvancedSearch = ref(false);
  const formModel = reactive<Record<string, any>>({});

  // Initialize form model
  watch(
    () => props.modelValue,
    (newVal) => {
      Object.assign(formModel, newVal);
    },
    { immediate: true, deep: true }
  );

  // Watch form model changes
  watch(
    formModel,
    (newVal) => {
      emit('update:modelValue', { ...newVal });
    },
    { deep: true }
  );

  const toggleAdvancedSearch = () => {
    showAdvancedSearch.value = !showAdvancedSearch.value;
  };

  const handleSearch = () => {
    nextTick(() => {
      emit('search');
    });
  };

  const handleDateChange = (field: string) => {
    const today = dayjs();
    let startDate = today;
    let endDate = today;

    switch (dateKey.value) {
      case -1: // 昨天
        startDate = today.subtract(1, 'day');
        endDate = today.subtract(1, 'day');
        break;
      case -6: // 近7天
        startDate = today.subtract(6, 'day');
        break;
      case -29: // 近30天
        startDate = today.subtract(29, 'day');
        break;
      case 2: // 自定义
        return; // 保持当前选择
      default:
        return; // 处理未知的日期类型
    }

    // 设置时间为当天的开始和结束
    startDate = startDate.startOf('day');
    endDate = endDate.endOf('day');

    formModel[field] = [
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
    ];
    handleSearch();
  };

  const handleRangeChange = (dates: any, field: string) => {
    handleSearch();
    // 匹配dateKey
    dateKey.value = 2;
  };

  const changeSort = (field: string) => {
    nextTick(() => {
      emit('sort', 'desc', field);
    });
  };
  const handleSort = (type: string) => {
    formModel.order_type = type;
    nextTick(() => {
      emit('search');
    });
  };

  const resetSearch = () => {
    // Reset to default values or empty
    Object.keys(formModel).forEach((key) => {
      formModel[key] = props.config.defaultValues?.[key] ?? null;
    });
    nextTick(() => {
      emit('reset');
      emit('search');
    });
  };

  const getComponentType = (type: string) => {
    const componentMap: Record<string, any> = {
      input: 'a-input',
      inputNumber: 'a-input-number',
      select: 'a-select',
      datePicker: 'a-date-picker',
      rangePicker: 'a-range-picker',
      requestSelect: 'request-select',
      rangePopoverPair: RangePopoverPair,
      UserSelectPopover,
      rangePopoverSort: RangePopoverSort,
    };
    return componentMap[type] || type;
  };

  const hasDatePicker = computed(() => {
    return props.config.advancedSearch?.items.some(
      (item) => item.type === 'rangePicker'
    );
  });

  const showAdvancedSearchList = computed(() => {
    // const blackList = ['rangePicker', 'datePicker'];
    const blackList = [];
    return props.config.advancedSearch?.items.filter(
      (item) => !blackList.includes(item.type)
    );
  });

  const filteredFormModel = computed(() => {
    // 定义需要过滤的参数
    const filterList = [
      'dir_id',
      'page',
      'pageSize',
      'total',
      'type',
      'order_type',
      'add_time',
      'create_user_ids_users',
    ];
    return Object.entries(formModel).filter(
      ([key, value]) =>
        value !== null &&
        value !== undefined &&
        value !== '' &&
        !(Array.isArray(value) && value.length === 0) &&
        !filterList.includes(key)
    );
  });

  const canShowSearchParams = (field: string) => {
    // 根据不同的field 判断是否显示
    switch (field) {
      case 'order_field':
        // 不是默认的排序，则显示
        if (
          formModel.order_field === 'add_time' &&
          formModel.order_type === 'desc'
        ) {
          return false;
        }
        return true;
      case 'add_time':
        return formModel.add_time !== null && formModel.add_time.length > 0;
      case 'create_user_ids':
        return (
          formModel.create_user_ids !== null &&
          formModel.create_user_ids.length > 0
        );
      case 'publish_num_min':
        // 只有最大值不存在时，才显示最小值
        return !formModel.publish_num_max && formModel.publish_num_min !== 0;
      case 'publish_num_max':
        // 最大值不存在时，不显示
        return formModel.publish_num_max !== null;
      default:
        return formModel[field];
    }
  };

  const hasSearchParams = computed(() => {
    // 如果只有排序字段和排序方式，且是默认的排序，则不显示
    // 把key提取出来
    const keys = filteredFormModel.value.map(([key]) => key);
    // 走一遍canShowSearchParams
    const canShow = keys.some((key) => canShowSearchParams(key));
    return canShow;
  });

  const getFieldLabel = (field: string) => {
    // 从配置中获取字段标签
    const { basicSearch } = props.config;
    if (basicSearch && basicSearch.field === field) {
      return '关键词';
    }

    const { advancedSearch } = props.config;
    if (advancedSearch) {
      const matchedItem = advancedSearch.items.find(
        (item) => item.field === field
      );
      if (matchedItem) {
        return matchedItem.label;
      }
    }

    // 默认字段名称映射
    const defaultLabels: Record<string, string> = {
      order_field: '排序',
      order_type: '排序方式',
      add_time: '创建时间',
      status: '状态',
      type: '类型',
      publish_num_max: '发布次数',
      publish_num_min: '发布次数',
      // 添加更多默认字段映射
    };

    return defaultLabels[field] || field;
  };

  const formatFieldValue = (field: string, value: any) => {
    // 处理用户选择的情况
    if (field.endsWith('_user_ids')) {
      const users =
        formModel[`${field.replace('_user_ids', '_user_ids_users')}`];
      return users.map((user: any) => user.name).join(', ');
    }
    if (Array.isArray(value)) {
      if (value.length === 2 && value[0] && value[1]) {
        return `${value[0]} ~ ${value[1]}`;
      }
      return value.join(', ');
    }

    // 特殊字段值的格式化
    const valueFormatters: Record<string, (val: any) => string> = {
      order_type: (val) => {
        if (val === 'asc') return '升序';
        if (val === 'desc') return '降序';
        return val;
      },
      status: (val) => {
        const statusMap: Record<string, string> = {
          '0': '禁用',
          '1': '启用',
          // 添加更多状态映射
        };
        return statusMap[val] || val;
      },
      // 添加更多值格式化器
      // 排序
      order_field: (val) => {
        const orderFieldMap: Record<string, string> = {
          add_time: '创建时间',
          update_time: '更新时间',
          publish_num: '发布次数',
        };
        // 判断正序还是倒序
        let orderType = '';
        if (formModel.order_type === 'asc') {
          orderType = '（升序）';
        } else if (formModel.order_type === 'desc') {
          orderType = '（降序）';
        }
        return `${orderFieldMap[val] || val} ${orderType}`;
      },
      // 发布次数
      publish_num_max: (val) => {
        return `${formModel.publish_num_min || 0} ~ ${val} 次`;
      },
      publish_num_min: (val) => {
        return `${val} ~ ${formModel.publish_num_max || '不限'}`;
      },
    };

    const formatter = valueFormatters[field];
    if (formatter) {
      return formatter(value);
    }

    return value;
  };

  const handleClose = (field: string) => {
    formModel[field] = props.config.defaultValues?.[field] ?? null;
    if (field === 'publish_num_min' || field === 'publish_num_max') {
      formModel.publish_num_max = null;
      formModel.publish_num_min = null;
    }
    handleSearch();
  };
</script>

<style scoped lang="less">
  .search-date {
    display: flex;
  }
  .search-form-section {
    margin-bottom: 10px;
    &.no-bottom-margin {
      margin-bottom: 0;
    }

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;

      .search-main {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--color-text-1);
        font-weight: 500;
        flex: 1;
        min-width: 280px;

        .arco-icon {
          font-size: 16px;
        }
      }
    }

    .search-content {
      border-bottom: 1px solid var(--color-fill-1);
      border-radius: var(--border-radius-large);
      padding: 10px 0 10px;

      .advanced-search {
        transition: all 0.3s ease;

        &.is-collapsed {
          height: 0;
          margin: 0;
          opacity: 0;
        }

        &:not(.is-collapsed) {
          opacity: 1;
        }
      }
    }

    .search-row {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
      flex-wrap: wrap;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .search-group {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      width: 100%;

      .search-item {
        display: flex;
        align-items: center;
        gap: 8px;
        // width: 300px;
        flex-shrink: 0;

        .group-label {
          color: var(--color-text-2);
          white-space: nowrap;
          font-size: 14px;
          flex-shrink: 0;
        }

        .search-input {
          width: 100%;
        }

        .group-input {
          display: flex;
          align-items: center;
          gap: 8px;
          width: 100%;

          .search-input {
            flex: 1;
          }

          .group-split {
            color: var(--color-text-2);
            white-space: nowrap;
            // padding: 0 3px;
          }
        }

        .sort-group {
          display: flex;
          align-items: center;
          gap: 8px;
          width: 100%;

          .search-input {
            flex: 1;
          }

          .sort-icon {
            cursor: pointer;
            &.active {
              color: rgb(var(--primary-6));
            }
          }
        }
      }
    }

    .search-wrapper {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 10px;
    }

    .search-tags {
      margin-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 8px;

      .tags-wrapper {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        flex: 1;
        .tag-label {
          color: var(--color-text-2);
          font-size: 13px;
        }
      }

      .clear-all-btn {
        flex-shrink: 0;
        .arco-icon {
          margin-right: 4px;
        }
      }

      .arco-tag {
        margin: 0;
        padding: 0 8px;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        background-color: var(--color-fill-2);
        color: var(--color-text-2);
        border: 1px solid var(--color-fill-3);

        &:hover {
          background-color: var(--color-fill-3);
        }

        .arco-icon {
          margin-left: 4px;
          font-size: 12px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .search-form-section {
      .search-header {
        flex-direction: column;
        align-items: stretch;

        .search-main {
          width: 100%;
        }
      }

      .search-row {
        flex-direction: column;
        gap: 12px;
      }

      .search-group {
        flex-direction: column;
        width: 100%;

        .search-item {
          width: 100%;
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          .group-label {
            min-width: auto;
          }

          .group-input,
          .sort-group {
            width: 100%;
          }
        }
      }
    }
  }
</style>

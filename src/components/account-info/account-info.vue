<template>
  <div class="df ai-cen">
    <a-avatar
      :width="48"
      :height="48"
      :fit="'contain'"
      :image-url="
        info.avatar_url || info.profile_photo || './icons/common/avatar.png'
      "
    />
    <div class="ml-10 df fd-cl" style="text-align: left; flex: 1">
      <a-typography-text ellipsis class="mb-0">
        {{ info.account_name }}
      </a-typography-text>
      <span class="description">ID: {{ info.account_id }}</span>
    </div>
    <img
      width="24"
      :src="`./icons/platform/${info.platform}.png`"
      :alt="info.platform"
    />
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  });
</script>

<style scoped lang="less">
  .description {
    font-size: 12px;
    color: var(--color-text-3);
  }
</style>

<template>
  <div class="account-display">
    <!-- Single account display -->
    <template v-if="!multiple">
      <div class="account-info">
        <div class="avatar-wrap">
          <img
            :src="account.profile_photo"
            :alt="account.name"
            class="avatar"
          />
          <img
            :src="getPlatformIcon(account.platform)"
            :alt="account.platform"
            class="platform-icon"
          />
        </div>
        <div class="account-detail">
          <span class="account-name">{{ account.account_name }}</span>
          <span class="platform-name">{{ account.account_id }}</span>
        </div>
      </div>
    </template>

    <!-- Multiple accounts display -->
    <template v-else>
      <!-- Popover with accounts list -->
      <a-popover
        v-model:popup-visible="tooltipVisible"
        position="right"
        trigger="click"
        class="no-padding-popover"
      >
        <div class="accounts-trigger">
          <div class="accounts-avatars">
            <template v-for="(acc, index) in displayAccounts" :key="index">
              <div
                class="avatar-wrap"
                :style="{ zIndex: displayAccounts.length - index }"
              >
                <img :src="acc.profile_photo" :alt="acc.name" class="avatar" />
                <img
                  :src="getPlatformIcon(acc.platform)"
                  :alt="acc.platform"
                  class="platform-icon"
                />
              </div>
            </template>
            <div v-if="remainingCount > 0" class="more-count"
              >+{{ remainingCount }}</div
            >
          </div>
          <div class="accounts-info">
            <div class="account-names">
              <template v-for="(acc, index) in displayAccounts" :key="index">
                <span class="account-name">{{ acc.account_name }}</span>
                <span
                  v-if="
                    index < displayAccounts.length - 1 || remainingCount > 0
                  "
                  class="separator"
                  >,</span
                >
              </template>
              <span v-if="remainingCount > 0" class="more-text"
                >等{{ remainingCount }}个账号</span
              >
            </div>
            <div class="account-ids">
              <template v-for="(acc, index) in displayAccounts" :key="index">
                <span class="account-id">{{ acc.account_id }}</span>
                <span
                  v-if="
                    index < displayAccounts.length - 1 || remainingCount > 0
                  "
                  class="separator"
                  >,</span
                >
              </template>
              <span v-if="remainingCount > 0" class="more-text"
                >等{{ remainingCount }}个账号</span
              >
            </div>
          </div>
        </div>
        <template #content>
          <div class="accounts-popover-content">
            <div class="accounts-list">
              <div
                v-for="(acc, index) in accounts"
                :key="index"
                class="account-item"
                @mouseenter="currentAccount = acc"
                @mouseleave="currentAccount = null"
              >
                <div class="avatar-wrap">
                  <img
                    :src="acc.profile_photo"
                    :alt="acc.name"
                    class="avatar"
                  />
                  <img
                    :src="getPlatformIcon(acc.platform)"
                    :alt="acc.platform"
                    class="platform-icon"
                  />
                </div>
                <div class="account-detail">
                  <span class="account-name">{{ acc.account_name }}</span>
                  <span class="platform-name">{{ acc.account_id }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </a-popover>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';

  interface Account {
    profile_photo: string;
    name: string;
    account_name: string;
    account_id: string;
    platform: string;
  }

  const props = defineProps<{
    account?: Account;
    accounts?: Account[];
    multiple?: boolean;
    maxDisplay?: number;
  }>();

  const tooltipVisible = ref(false);
  const tooltipTrigger = ref<HTMLElement | null>(null);
  const currentAccount = ref<Account | null>(null);
  const popupContainer = ref<HTMLElement | null>(null);
  let tooltipTimeout: number | null = null;

  const maxDisplayCount = computed(() => props.maxDisplay || 3);

  const displayAccounts = computed(() => {
    if (!props.accounts) return [];
    return props.accounts.slice(0, maxDisplayCount.value);
  });

  const remainingCount = computed(() => {
    if (!props.accounts) return 0;
    return Math.max(0, props.accounts.length - maxDisplayCount.value);
  });

  onMounted(() => {
    popupContainer.value = document.body;
  });

  onUnmounted(() => {
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
    }
  });

  const getPlatformIcon = (platform: string) => {
    return new URL('@/assets/images/instagram.png', import.meta.url).href;
  };

  const showTooltip = () => {
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
    }
    tooltipVisible.value = true;
  };

  const hideTooltip = () => {
    tooltipTimeout = window.setTimeout(() => {
      tooltipVisible.value = false;
      currentAccount.value = null;
    }, 100);
  };
</script>

<style scoped lang="less">
  .account-display {
    display: inline-block;
    width: 100%;
  }

  .account-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;

    .avatar-wrap {
      position: relative;
      width: 38px;
      height: 38px;
      flex-shrink: 0;

      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 1px solid var(--color-fill-3);
      }

      .platform-icon {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 1px solid var(--color-fill-2);
        background: var(--color-fill-2);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .account-detail {
      display: flex;
      flex-direction: column;
      min-width: 0;

      .account-name {
        font-size: 12px;
        color: var(--color-text-1);
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .platform-name {
        font-size: 11px;
        color: var(--color-text-3);
        display: flex;
        align-items: center;
        gap: 3px;
      }
    }
  }

  .accounts-trigger {
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .accounts-avatars {
    display: flex;
    align-items: center;
    position: relative;
    height: 32px;
    flex-shrink: 0;

    .avatar-wrap {
      position: relative;
      width: 38px;
      height: 38px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--color-fill-3);
      transition: all 0.2s ease;
      margin-left: -8px;

      &:first-child {
        margin-left: 0;
      }

      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .platform-icon {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 1px solid var(--color-fill-2);
        background: var(--color-fill-2);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .more-count {
      position: relative;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: var(--color-fill-2);
      border: 1px solid var(--color-fill-3);
      margin-left: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-text-2);
      font-size: 12px;
      font-weight: 500;
    }
  }

  .accounts-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;

    .account-names,
    .account-ids {
      // display: flex;
      align-items: center;
      gap: 4px;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1;
    }

    .account-name {
      font-size: 13px;
      color: var(--color-text-1);
      font-weight: 500;
    }

    .account-id {
      font-size: 12px;
      color: var(--color-text-3);
    }

    .separator {
      color: var(--color-text-3);
      font-size: 12px;
    }

    .more-text {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  .accounts-trigger:hover {
    .avatar-wrap {
      border-color: var(--color-primary-light-2);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .more-count {
      border-color: var(--color-primary-light-2);
      background: var(--color-primary-light-1);
      color: var(--color-primary-6);
    }
  }

  .accounts-popover-content {
    padding: 8px;
    min-width: 240px;
    max-height: 450px;
    overflow-y: auto;
  }

  .accounts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .account-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px;
      border-radius: var(--border-radius-large);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--color-fill-2);
      }

      .avatar-wrap {
        position: relative;
        width: 38px;
        height: 38px;
        flex-shrink: 0;

        .avatar {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
          border: 1px solid var(--color-fill-3);
        }

        .platform-icon {
          position: absolute;
          right: -2px;
          bottom: -2px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 1px solid var(--color-fill-2);
          background: var(--color-fill-2);
        }
      }

      .account-detail {
        flex: 1;
        min-width: 0;
        line-height: 1;

        .account-name {
          font-size: 13px;
          color: var(--color-text-1);
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .platform-name {
          font-size: 12px;
          color: var(--color-text-3);
          display: flex;
          align-items: center;
          gap: 3px;
        }
      }
    }
  }
</style>

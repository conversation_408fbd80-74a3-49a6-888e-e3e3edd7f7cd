<template>
  <a-badge
    class="status-badge"
    :color="color"
    :style="{
      color: color,
    }"
    :text="text"
  />
</template>

<script setup lang="ts">
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import { computed, PropType } from 'vue';

  const props = defineProps({
    list: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    value: {
      type: [String, Number],
      default: '',
    },
  });
  const color = computed(() => {
    return getColor(props.list, props.value) || 'gray';
  });
  const text = computed(() => {
    return getText(props.list, props.value) || '-';
  });
</script>

<style scoped lang="less">
  .status-badge :deep(.arco-badge-status-text) {
    color: inherit;
  }
</style>

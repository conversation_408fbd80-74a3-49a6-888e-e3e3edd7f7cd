<template>
  <div v-if="videoInfo" class="xhs-post-inline-simple" @click="onXhsPostClick">
    <img :src="videoInfo.cover_url" class="xhs-post-cover-simple" alt="cover" />
    <div class="xhs-post-info-simple">
      <div class="xhs-post-title-simple">
        {{ videoInfo.title || '无标题' }}
      </div>
      <div
        :class="[
          'xhs-post-desc-simple',
          { 'xhs-post-desc-more': !showCountBar && !showContentId },
          { 'xhs-post-desc-more-4': !showCountBar && showContentId },
        ]"
      >
        {{ videoInfo.desc || '' }}
      </div>
      <div v-if="showCountBar" class="xhs-post-inline-meta">
        <span class="xhs-post-inline-meta-item">
          <icon-heart class="xhs-post-inline-icon" />
          <span>{{ videoInfo.like_count || 0 }}</span>
        </span>
        <span class="xhs-post-inline-meta-item">
          <icon-message class="xhs-post-inline-icon" />
          <span>{{ videoInfo.comment_count || 0 }}</span>
        </span>
        <!-- 收藏 -->
        <span class="xhs-post-inline-meta-item">
          <icon-star class="xhs-post-inline-icon" />
          <span>{{ videoInfo.favorite_count || 0 }}</span>
        </span>
      </div>
      <span v-if="showContentId" class="content-id-label"
        >ID：{{ videoInfo.media_video_id }}</span
      >
    </div>
  </div>
  <div v-else class="parent-comment-empty">无</div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue';
  import {
    IconHeartFill,
    IconMessage,
    IconStar,
  } from '@arco-design/web-vue/es/icon';

  const props = defineProps({
    videoInfo: {
      type: Object,
      required: false,
      default: null,
    },
    showContentId: {
      type: Boolean,
      default: false,
    },
    showCountBar: {
      type: Boolean,
      default: false,
    },
  });

  function onXhsPostClick() {
    if (props.videoInfo && props.videoInfo.video_url) {
      window.open(props.videoInfo.video_url, '_blank');
    }
  }
</script>

<style scoped lang="less">
  .xhs-post-inline-simple {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    box-shadow: none;
    padding: 0;
    max-width: 300px;
    min-width: 0;
    cursor: pointer;
    transition: background 0.15s;
    &:hover {
      background: #f6f8fa;
    }
  }
  .xhs-post-cover-simple {
    width: 100px;
    height: 100px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
  }
  .xhs-post-info-simple {
    min-width: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100px;
    justify-content: flex-start;
    gap: 2px;
  }
  .xhs-post-title-simple {
    font-size: 13px;
    font-weight: 600;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #222;
  }
  .xhs-post-desc-simple {
    font-size: 12px;
    color: #666;
    line-height: 1.35;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-line;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .xhs-post-desc-more {
    -webkit-line-clamp: 3;
    max-height: 7em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    line-height: 2;
  }
  .xhs-post-desc-more-4 {
    line-height: 1.2;
    -webkit-line-clamp: 4;
    max-height: 6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
  }
  .xhs-post-inline-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 4px;
  }
  .xhs-post-inline-meta-item {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 12px;
    color: #888;
  }
  .xhs-post-inline-icon {
    font-size: 14px;
    vertical-align: middle;
  }
  .parent-comment-empty {
    color: #b0b3b8;
    font-style: italic;
    text-align: center;
    padding: 10px 0;
  }
  .xhs-post-heart-red {
    color: #ff4d4f;
  }
  @media (max-width: 600px) {
    .xhs-post-inline-simple {
      max-width: 100%;
      gap: 6px;
    }
    .xhs-post-cover-simple {
      width: 36px;
      height: 36px;
    }
    .xhs-post-title-simple,
    .xhs-post-desc-simple {
      max-width: 120px;
    }
  }
  .content-id-label {
    display: inline-block;
    color: #a3adc3;
    font-size: 11px;
    font-weight: 400;
    border-radius: 3px;
    margin-right: 6px;
    vertical-align: middle;
    letter-spacing: 0.2px;
    box-shadow: none;
    border: none;
  }
</style>

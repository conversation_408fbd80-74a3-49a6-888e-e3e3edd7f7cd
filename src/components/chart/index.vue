<template>
  <vue-echarts
    v-if="renderChart"
    :option="options"
    :autoresize="autoResize"
    :theme="theme"
    :style="{ width, height }"
  />
</template>

<script lang="ts" setup>
  import { ref, nextTick, computed } from 'vue';
  import { cloneDeep } from 'lodash';
  import { useAppStore } from '@/store';
  import vueEcharts from 'vue-echarts';

  const props = defineProps({
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  });
  const appStore = useAppStore();
  const theme = computed(() => {
    if (appStore.theme === 'dark') return 'dark';
    return '';
  });
  const renderChart = ref(false);
  // wait container expand
  nextTick(() => {
    renderChart.value = true;
  });
</script>

<style scoped lang="less"></style>

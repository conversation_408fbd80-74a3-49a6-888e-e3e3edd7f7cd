interface dictType {
  [name: string]: any;
}

export const getText = (dictList: dictType[], key: any, typeKey?: string) => {
  if (Array.isArray(key)) {
    return (
      dictList
        ?.filter((item: any) => key.includes(item.value))
        ?.map((item: any) => (typeKey && item.labels?.[typeKey]) || item.label)
        .join(',') || key
    );
  }
  let citem = dictList?.find((item: any) => item.value === key);
  return (typeKey && citem?.labels?.[typeKey]) || citem?.label || '';
};

export const getColor = (dictList: dictType[], key: string | number) => {
  let citem = dictList?.find((item: any) => item.value === key);
  return citem?.color;
};

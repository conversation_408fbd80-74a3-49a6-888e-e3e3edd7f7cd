<template>
  <BaseSelect
    v-bind="props"
    :model-value="modelValue"
    style="width: 100%"
    :data-list="dataList"
    label-key="label"
    value-key="value"
    @change="handleChange"
  >
    <template #option="{ data }">
      <a-space>
        <img
          width="20"
          :src="`/icons/platform/${data.value}.png`"
          :alt="data.label"
        />
        <span>{{ data.label }}</span>
      </a-space>
    </template>
  </BaseSelect>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import BaseSelect from '../select/base-select.vue';

  const props = defineProps({
    placeholder: {
      type: String,
      default: '请选择',
    },
    modelValue: {
      type: [String, Number, Array],
      default: () => null,
    },
    multiple: {
      type: [Boolean],
      default: () => false,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(['input', 'change', 'update:modelValue']);
  const dataList = ref(contactWayListM);
  function handleChange() {
    // @ts-ignore
    // eslint-disable-next-line
    emit('update:modelValue', arguments[0]);
    // @ts-ignore
    // eslint-disable-next-line
    emit('change', ...arguments);
  }
</script>

<style lang="less" scoped></style>

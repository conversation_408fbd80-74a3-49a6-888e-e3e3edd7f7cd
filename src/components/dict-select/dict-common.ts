export const colors = {
  primary: 'rgb(var(--arcoblue-6))', // 全局主色
  link: 'rgb(var(--arcoblue-6))', // 链接色
  success: 'rgb(var(--green-6))', // 成功色
  warning: 'rgb(var(--orange-6))', // 警告色
  info: 'rgb(var(--green-4))', // 信息
  error: 'rgb(var(--red-6))', // 错误色
  disable: 'var(--color-neutral-6)', // 禁用
};

// 通用
export const switchM = [
  { value: 'off', label: '不启用' },
  { value: 'on', label: '启用' },
];

// 通用
export const stateM = [
  { value: -1, label: '停用' },
  { value: 1, label: '正常' },
];

// 通用
export const stateM2 = [
  { value: '', label: '全部' },
  { value: -1, label: '停用' },
  { value: 1, label: '正常' },
];

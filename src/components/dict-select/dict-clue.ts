// 线索来源类型
import { colors } from '@/components/dict-select/dict-common';

export const clueSuorceM = [
  { label: '爬虫关键词配置', value: '爬虫关键词配置' },
  { label: '商业化抖音号配置', value: '商业化抖音号配置' },
  { label: '直播间监控', value: '直播间监控' },
  { label: '客服新增', value: '客服新增' },
  { label: '接粉专员新增', value: '接粉专员新增' },
];

// 线索分配规则
export const clueAssignM = [{ label: '线索补齐分配', value: '线索补齐分配' }];

// 销售线索状态
export const clueStatusTwoM = [
  { label: '未留资', value: '未留资', color: colors.warning },
  { label: '已留资', value: '已留资', color: colors.success },
];
// 线索阶段
// 1:未建联 2:未回应 3:未留资 4:已留资
export const clueStatusThreeM = [
  { label: '未建联', value: 1, color: colors.disable },
  { label: '未回应', value: 2, color: colors.error },
  { label: '未留资', value: 3, color: colors.warning },
  { label: '已留资', value: 4, color: colors.success },
];
// 建联方式
//  1:主动陌拜 2:用户进线
export const contactWayTypeM = [
  { label: '主动陌拜', value: 1, color: colors.primary },
  { label: '用户进线', value: 2, color: colors.success },
];

// 二级线索状态
export const clueStatusM = [
  { label: '待分配', value: '待分配', color: 'rgb(var(--arcoblue-2))' },
  { label: '已分配', value: '已分配', color: colors.warning },
  { label: '已跟进', value: '已跟进', color: colors.primary },
  { label: '已成交', value: '已成交', color: colors.success },
];

// 二级线索状态
export const msgStatusM = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
  { label: '发送中', value: '发送中' },
  { label: '发送失败', value: '发送失败' },
];

// AI意向等级
export const intentionalityIntentM = [
  { label: '有意向', value: 'positive' },
  { label: '可能有意向', value: 'potential_positive' },
  { label: '可能无意向', value: 'potential_negative' },
  { label: '无意向', value: 'negative' },
];

// 回复模板宏
export const replayTemplateM = [
  { label: '用户昵称', value: '{用户昵称}' },
  { label: '关键词', value: '{关键词}' },
];

// 询问联系方式模版
export const askContactTemplateM = [
  { label: '用户昵称', value: '{用户昵称}' },
  { label: '关键词', value: '{关键词}' },
  { label: '分配人员信息', value: '{分配人员信息}' },
];

// 回复模板状态
export const replayTemplateStateM = [
  { label: '已挑选可用', value: 1 },
  { label: '润色待挑选', value: 2 },
];

// 回复模板类型
export const replayTemplateTypeM = [
  { label: '关键词', value: 'keyword' },
  { label: '内容号', value: 'tk_account' },
  { label: '直播间', value: 'live_room' },
];

export const clueSourceTypeM = [
  { label: '关键词', value: '关键词' },
  { label: '内容号(自有)', value: '内容号(自有)' },
  { label: '内容号(非自有)', value: '内容号(非自有)' },
  { label: '直播间', value: '直播间' },
];

export const clueConfigStateM = [
  { label: '有效', value: 1 },
  { label: '无效', value: -1 },
  { label: '全部', value: '' },
];

// 平台
export const cluePlatformM = [
  { label: '全部', value: '' },
  { label: '小红书', value: 'xhs' },
  { label: '抖音', value: 'douyin' },
  { label: 'INSTAGRAM', value: 'ins' },
  { label: 'TikTok', value: 'tiktok' },
];

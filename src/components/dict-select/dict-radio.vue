<template>
  <a-radio-group
    v-model="curVal"
    :type="type"
    size="large"
    :class="{
      'max-width': maxWidth,
      'radio-group-wrap': true,
    }"
    :disabled="disabled"
    @change="handleChange"
  >
    <a-radio v-if="showAll" :key="''" :value="''" class="radio-item">
      全部
    </a-radio>
    <a-radio
      v-for="item in showList"
      :key="item[valueKey]"
      :value="item[valueKey]"
      class="radio-item"
      :disabled="
        disabledValues.includes(item[valueKey]) ||
        Boolean(
          enabledValues?.length && !enabledValues.includes(item[valueKey])
        )
      "
    >
      <div class="df ai-cen">
        <img
          v-if="showIcon && item.icon"
          width="20"
          style="margin-right: 5px; display: inline-block"
          :src="item.icon"
          alt=""
        />
        <span>{{ getLabel ? getLabel(item) : getItemLabel(item) }}</span>
        <div v-if="item.tips" style="white-space: nowrap">{{ item.tips }}</div>
        <a-tooltip v-if="item.tooltip">
          <icon-question-circle />
          <template #content>
            <div style="white-space: pre-line">
              {{ item.tooltip }}
            </div>
          </template>
        </a-tooltip>
      </div>
    </a-radio>
  </a-radio-group>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';

  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: () => '',
    },
    valueKey: {
      type: [String, Number],
      default: () => 'value',
    },
    labelKey: {
      type: [String, Number],
      default: () => 'label',
    },
    labelsKey: {
      type: [String, Number],
      default: () => 'labels',
    },
    dataList: {
      type: Array,
      default: () => [],
      required: true,
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    showAll: {
      type: Boolean,
      default: false,
    },
    typeKey: {
      type: String,
      default: '',
    },
    // 需要禁用的值
    disabledValues: {
      type: Array,
      default: () => [],
    },
    // 需要启用的值
    enabledValues: {
      type: Array,
      default: () => [],
    },
    // 列表范围
    dataScope: {
      type: Array,
      default: null,
    },
    getLabel: {
      type: Function,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: 'button',
    },
    maxWidth: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['input', 'change', 'update:modelValue']);
  const curVal = ref<string | number>('');

  const showList = computed(() =>
    props.dataScope?.length
      ? props.dataList?.filter((item: any) =>
          props.dataScope?.includes(item[props.valueKey])
        )
      : props.dataList || []
  );
  const getItemLabel = (item: any) => {
    if (props.typeKey && item[props.labelsKey]?.[props.typeKey]) {
      return item[props.labelsKey][props.typeKey];
    }
    return item[props.labelKey];
  };

  watch(
    () => props.modelValue,
    (newVal) => {
      curVal.value = newVal;
    },
    {
      immediate: true,
    }
  );
  const handleChange = (val: any) => {
    emit('update:modelValue', val);
    emit('change', val);
  };
</script>

<style scoped>
  .max-width {
    width: 100%;
  }

  .radio-group-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .radio-item {
    margin-right: 0 !important;
  }
</style>

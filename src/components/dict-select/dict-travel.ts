import { colors } from '@/components/dict-select/dict-common';

export const contactWayListM = [
  { label: '抖音', value: '抖音', icon: 'icons/platform/抖音.png' },
  // { label: '视频号', value: '视频号', icon: 'icons/platform/视频号.png' },
  { label: '小红书', value: '小红书', icon: 'icons/platform/小红书.png' },
  // { label: '线下', value: '线下' },
];

export const priceStatusListM = [
  { label: '待跟进', value: '待跟进' },
  { label: '已支付订金', value: '已支付订金' },
  { label: '已作废', value: '已作废' },
];

export const orderStatusListM = [
  { label: '已支付定金（1级）', value: '已支付订金' },
  { label: '订单已确认（2级）', value: '订单已确认' },
  { label: '已发确认函（3级）', value: '已发确认函' },
  { label: '已核销', value: '已核销' },
  { label: '已出行', value: '已出行' },
  { label: '已回访', value: '已回访' },
  { label: '已退款', value: '已退款' },
  { label: '已反馈', value: '已反馈' },
  { label: '已作废', value: '已作废' },
];

export const customerServiceStatusListM = [
  { label: '全部', value: '' },
  { label: '在线', value: '在线', color: colors.success },
  { label: '离线', value: '离线', color: colors.disable },
  { label: '离职', value: '离职', color: colors.error },
];

export const trackingTypeListM = [
  { label: '订单', value: '订单' },
  { label: '询价单', value: '询价单' },
];

export const travelTypeListM = [
  { label: '亲子', value: '亲子' },
  { label: '团建', value: '团建' },
  { label: '朋友', value: '朋友' },
];

export const yesOrNo = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
];

export const yesOrNo2 = [
  { label: '是', value: 1 },
  { label: '否', value: -1 },
];

export const yesOrNoOrNull = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
  { label: '未选择', value: '未选择' },
];

export const payTypeM = [
  { label: '线上', value: '线上' },
  { label: '线下', value: '线下' },
];

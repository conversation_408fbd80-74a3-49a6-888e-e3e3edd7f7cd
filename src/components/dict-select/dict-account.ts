import { colors } from '@/components/dict-select/dict-common';

export const deviceStatusM = [
  { label: '在线', value: 'connected', color: colors.success },
  { label: '离线', value: 'disconnected', color: colors.error },
  { label: '停用', value: 'deleted', color: colors.disable },
];

export const accountStatusM = [
  { label: '在线', value: 'active', color: colors.success },
  { label: '离线', value: 'suspened', color: colors.error },
  { label: '停用', value: 'disable', color: colors.disable },
];
export const accountStatusLastM = [
  { label: '在线', value: 'active', color: colors.success },
  { label: '未连接', value: 'disconnected', color: colors.disable },
  { label: '异常', value: 'suspened', color: colors.error },
];

export const contentPublishEntryM = [
  { label: '普通入口', value: '普通入口' },
  { label: '创意灵感', value: '创意灵感' },
];

export const contentPublishEntryTypeM = [
  { label: '为你推荐', value: 'for_you' },
  { label: '实时追热', value: 'trending' },
  { label: '大家在搜', value: 'popular' },
  { label: '为你推荐', value: 'recommended' },
];

export const publishStatusM = [
  { label: '进行中', value: 1, color: colors.primary },
  { label: '已完成', value: 2, color: colors.success },
  { label: '失败', value: 3, color: colors.error },
];

export const publishStatusM2 = [
  { label: '进行中', value: 1, color: colors.primary },
  { label: '已完成', value: 2, color: colors.success },
];

export const publishTaskStatusM2 = [
  { label: '排队中', value: -1, color: colors.warning },
  { label: '发布中', value: 1, color: colors.primary },
  { label: '已完成', value: 2, color: colors.success },
];

export const publishNoteStatusM = [
  { label: '已发布', value: 1, color: colors.success },
  { label: '已置顶', value: 2, color: colors.primary },
  { label: '已隐藏', value: 3, color: colors.disable },
  { label: '已删除', value: 4, color: colors.error },
];

// 评论
export const commentStatusM = [
  { label: '正常', value: 1, color: colors.success },
  { label: '置顶', value: 2, color: colors.primary },
  { label: '删除', value: 3, color: colors.error },
  { label: '归档', value: 4, color: colors.disable },
];

export const publishContentStatusM2 = [
  { label: '排队中', value: -1, color: colors.warning },
  { label: '发布中', value: 1, color: colors.primary },
  { label: '发布成功', value: 2, color: colors.success },
  { label: '发布失败', value: 3, color: colors.error },
  { label: '取消发布', value: 4, color: colors.disable },
];

//  1:等待中 2:进行中 3:已完成 4:失败
export const collectStatusM = [
  { label: '等待中', value: 1, color: colors.primary },
  { label: '进行中', value: 2, color: colors.primary },
  { label: '已完成', value: 3, color: colors.success },
  { label: '失败', value: 4, color: colors.error },
];

// 状态 1:采集中 -1:未采集
export const collectAccountStatusM = [
  { label: '采集中', value: 1, color: colors.warning },
  { label: '未采集', value: -1, color: colors.success },
];

// 排序字段
export const sortFieldM = [
  { label: '上传时间', value: 'add_time' },
  { label: '发布次数', value: 'publish_num' },
];

// 下载中心
// 0排队中1执行中2成功3失败
export const downloadCenterStatusM = [
  { label: '排队中', value: 0, color: colors.warning },
  { label: '下载中', value: 1, color: colors.primary },
  { label: '成功', value: 2, color: colors.success },
  { label: '失败', value: 3, color: colors.error },
];

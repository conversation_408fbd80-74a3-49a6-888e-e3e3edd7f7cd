<template>
  <d-modal
    :visible="visible"
    width="800px"
    title="请输入内容"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form :model="{ inputTxt }" auto-label-width>
      <a-form-item label="内容">
        <a-textarea
          v-if="isArea"
          v-model="inputTxt"
          :auto-size="{ minRows: 4 }"
        >
        </a-textarea>
        <a-input v-else v-model="inputTxt" />
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { ref } from 'vue';

  const props = defineProps({
    isArea: {
      type: Boolean,
      default: false,
    },
  });

  const inputTxt = ref('');
  const visible = ref(false);
  const emits = defineEmits(['save', 'close']);
  function handleCancel() {
    visible.value = false;
    emits('close');
  }
  function handleBeforeOk() {
    emits('save', inputTxt.value);
    handleCancel();
  }
  function show() {
    visible.value = true;
    inputTxt.value = '';
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

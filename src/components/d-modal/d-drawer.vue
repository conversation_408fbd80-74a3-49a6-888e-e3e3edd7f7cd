<template>
  <a-drawer v-if="isMounted" v-bind="$attrs">
    <template v-for="(slotItem, slotKey) in $slots" :key="slotKey" #[slotKey]>
      <slot :name="slotKey"></slot>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
  import { ref, useAttrs, watch } from 'vue';

  const atts = useAttrs();
  const isMounted = ref(false);
  watch(
    () => atts.visible,
    (val) => {
      if (val && !isMounted.value) {
        isMounted.value = true;
      }
    }
  );
</script>

<style scoped></style>

<template>
  <div v-if="avatar || name || id" class="account-info-row">
    <img :src="avatar" class="account-info-avatar" alt="avatar" />
    <div class="account-info-col">
      <span class="account-info-name">{{ name || '匿名' }}</span>
      <span class="account-info-id">ID：{{ id || '-' }}</span>
    </div>
  </div>
  <div v-else class="parent-comment-empty">无</div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue';

  const props = defineProps({
    avatar: String,
    name: String,
    id: String,
  });
</script>

<style scoped lang="less">
  .account-info-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .account-info-avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
    background: #f4f6fa;
    border: 1px solid #e5e6eb;
  }
  .account-info-col {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  .account-info-name {
    font-size: 13px;
    color: #222;
    font-weight: 500;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .account-info-id {
    font-size: 11px;
    color: #a3adc3;
    margin-top: 2px;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .parent-comment-empty {
    color: #b0b3b8;
    font-style: italic;
    text-align: center;
    padding: 10px 0;
  }
</style>

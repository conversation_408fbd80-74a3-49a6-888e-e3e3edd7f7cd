<!--
  通用聊天弹窗组件 - 用于评论区点击头像打开聊天窗口
  功能特性：
  - 支持陌拜场景和正常聊天
  - 使用评论私信接口发送消息
  - 模态框形式，支持关闭操作
-->
<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="getDialogTitle()"
    width="600px"
    :footer="false"
    :mask-closable="true"
    class="chat-dialog-modal"
    @cancel="handleClose"
    @close="handleClose"
  >
    <template #title>
      <div>
        {{ getDialogTitle() }}
        <a-avatar :size="22">
          <img
            :src="messageData?.avatar_url"
            alt="用户头像"
            @error="handleAvatarError"
          />
        </a-avatar>
        的私信对话
      </div>
    </template>
    <div class="chat-dialog-container">
      <!-- 聊天内容区域 -->
      <div class="chat-content">
        <!-- 空状态显示 -->
        <div v-if="chatMessages.length === 0" class="chat-empty-state">
          <div class="empty-content">
            <icon-message class="empty-icon" />
            <span class="empty-text">开启新的对话吧</span>
          </div>
        </div>

        <!-- 消息列表 -->
        <div
          v-if="chatMessages.length > 0"
          ref="chatMessagesRef"
          class="chat-messages"
        >
          <div
            v-for="msg in chatMessages"
            :key="msg.id"
            :class="['chat-message-wrapper', msg.type]"
          >
            <!-- 接收消息布局 -->
            <div v-if="msg.type === 'received'" class="message-row received">
              <div class="message-avatar">
                <a-avatar :size="32">
                  <img
                    v-if="messageData?.avatar_url"
                    :src="messageData?.avatar_url"
                    alt="用户头像"
                    @error="handleAvatarError"
                  />
                  <span v-else>
                    {{ messageData?.account_name?.charAt(0) || '用' }}
                  </span>
                </a-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="username">{{
                    messageData?.account_name || '用户'
                  }}</span>
                  <span class="message-time">{{ msg.time }}</span>
                </div>
                <div class="message-bubble received">
                  <chat-emoji-text :content="msg.content" />
                </div>
              </div>
            </div>

            <!-- 发送消息布局 -->
            <div v-else-if="msg.type === 'sent'" class="message-row sent">
              <div class="message-content">
                <div class="message-header">
                  <span class="username">{{ getSentMessageUsername() }}</span>
                  <span class="message-time">{{ msg.time }}</span>
                </div>
                <div class="message-bubble sent">
                  <chat-emoji-text :content="msg.content" />

                  <!-- 发送消息状态图标（在气泡内部） -->
                  <div class="message-status-icon">
                    <icon-loading
                      v-if="msg.send_status === 1"
                      class="primary_color"
                      :spin="true"
                    />
                    <icon-check-circle
                      v-else-if="msg.send_status === 3"
                      class="success_icon"
                      size="14"
                    />
                    <icon-exclamation-circle
                      v-else-if="msg.send_status === 5"
                      :style="{
                        fontSize: '14px',
                      }"
                    />
                  </div>
                </div>
              </div>
              <div class="message-avatar">
                <a-avatar :size="32">
                  <img
                    v-if="getSentMessageAvatar()"
                    :src="getSentMessageAvatar()"
                    alt="我的头像"
                    @error="handleAvatarError"
                  />
                  <span v-else>
                    {{ getSentMessageAvatarText() }}
                  </span>
                </a-avatar>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入模块 -->
      <div class="chat-input-module">
        <div class="input-main-area">
          <!-- 输入工具栏 -->
          <div class="input-toolbar">
            <chat-emoji
              class="emoji-btn"
              :platform="messageData?.platform || '小红书'"
              @select="selectEmoji"
            >
              <img
                class="toolbar-btn"
                src="@/assets/images/msg-smile.png"
                alt="表情"
              />
            </chat-emoji>

            <!-- 发送名片按钮 - 只有在 is_receive === 1 && isDamao === true 时显示 -->
            <span v-if="isDamao" @click="sendCard">
              <icon-loading v-if="sendingStatus === 'sending'" :size="20" />
              <icon-idcard v-else :size="20" />
            </span>
          </div>

          <div class="input-content">
            <a-textarea
              ref="inputRef"
              :model-value="inputMessage"
              placeholder="输入私信内容..."
              class="message-textarea"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              :allow-clear="false"
              @update:model-value="handleInputChange"
              @keydown.enter="handleSendMessage"
            />
            <div class="send-action">
              <a-button
                type="primary"
                size="small"
                :disabled="!inputMessage?.trim()"
                :loading="sendingStatus === 'sending'"
                @click="handleSendMessage"
              >
                <template #icon>
                  <icon-loading v-if="sendingStatus === 'sending'" />
                  <icon-send v-else />
                </template>
                发送
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';
  import {
    IconMessage,
    IconSend,
    IconLoading,
    IconCheck,
    IconCheckCircle,
    IconExclamation,
    IconExclamationCircle,
  } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import ChatEmoji from '@/views/travel-clue/sale-clue-list/chat-emoji.vue';
  import ChatEmojiText from '@/views/travel-clue/sale-clue-list/chat-emoji-text.vue';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import { useUserStore } from '@/store';

  // Props 定义
  interface Props {
    visible: boolean;
    messageData?: any;
    accountData?: any;
    isReceive?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    messageData: null,
    accountData: null,
    isReceive: 0, // 默认为陌拜场景
  });

  const userStore = useUserStore();
  const isDamao = computed(() => {
    return userStore.userInfo.isDamao;
  });

  // Emits 定义
  const emit = defineEmits<{
    'update:visible': [visible: boolean];
    'close': [];
  }>();

  // 响应式状态
  const dialogVisible = ref(false);
  const inputMessage = ref('');
  const sendingStatus = ref<'idle' | 'sending' | 'success' | 'failed'>('idle');
  const chatMessages = ref<any[]>([]);

  // DOM 引用
  const chatMessagesRef = ref<HTMLElement>();
  const inputRef = ref();

  // 心跳检测相关
  const heartbeatTimer = ref<number | null>(null);
  const HEARTBEAT_INTERVAL = 5000; // 5秒间隔
  const currentHeartbeatParams = ref<{
    account_id: string;
    platform: string;
    self_account_id: string;
  } | null>(null);

  // 智能滚动相关
  const SCROLL_THRESHOLD = 50; // 判断用户是否在底部的阈值（像素）
  const lastMessageIds = ref<Set<string | number>>(new Set()); // 记录上次的消息ID集合

  // 转换 API 数据为组件所需格式
  const transformApiData = (apiData: any[]): any[] => {
    return apiData.map((item) => ({
      id: item.id || `msg_${Date.now()}_${Math.random()}`,
      content: item.message || '',
      time: dayjs(item.send_time).format('YYYY-MM-DD HH:mm'),
      type: (() => {
        if (item.msg_show === 'right') return 'sent';
        if (item.msg_show === 'left') return 'received';
        return item.send_user === '我' ? 'sent' : 'received';
      })(),
      send_status: item.send_status,
      message_type: item.message_type || 'text',
      send_user: item.send_user || '用户',
      avatar:
        item.msg_show === 'right'
          ? item.account_profile_photo
          : item.received_user_avatar,
    }));
  };

  // 检查用户是否在聊天窗口底部
  const isUserAtBottom = (): boolean => {
    if (!chatMessagesRef.value) return true;

    const container = chatMessagesRef.value;
    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    return distanceFromBottom <= SCROLL_THRESHOLD;
  };

  // 滚动到底部
  const scrollToBottom = (smooth = false) => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTo({
        top: chatMessagesRef.value.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };

  // 检测新消息并返回新消息信息
  const detectNewMessages = (
    newMessages: any[]
  ): {
    hasNew: boolean;
    newCount: number;
    newMessageIds: Set<string | number>;
  } => {
    const currentMessageIds = new Set(newMessages.map((msg) => msg.id));
    const previousIds = lastMessageIds.value;

    // 找出新增的消息ID
    const newIds = new Set(
      [...currentMessageIds].filter((id) => !previousIds.has(id))
    );

    return {
      hasNew: newIds.size > 0,
      newCount: newIds.size,
      newMessageIds: currentMessageIds,
    };
  };

  // 智能滚动处理函数
  const handleSmartScroll = (newMessages: any[], isHeartbeat = false) => {
    if (!isHeartbeat) {
      // 非心跳检测的情况（如初始加载、用户发送消息），直接滚动到底部
      scrollToBottom(false);
      lastMessageIds.value = new Set(newMessages.map((msg) => msg.id));
      return;
    }

    // 心跳检测的情况，需要智能判断
    const { hasNew, newCount, newMessageIds } = detectNewMessages(newMessages);

    if (!hasNew) {
      // 没有新消息，不需要处理
      return;
    }

    console.log(`💬 检测到 ${newCount} 条新消息`);

    // 更新消息ID记录
    lastMessageIds.value = newMessageIds;

    // 检查用户是否在底部
    const userAtBottom = isUserAtBottom();

    if (userAtBottom) {
      // 用户在底部，自动滚动到最新消息
      console.log('📍 用户在底部，自动滚动到最新消息');
      scrollToBottom(true); // 使用平滑滚动
    } else {
      console.log('📍 用户不在底部，不自动滚动');
    }
  };

  // 加载历史聊天记录
  const loadChatHistory = async (isHeartbeat = false) => {
    if (!props.messageData?.account_id || !props.accountData?.account_id) {
      if (!isHeartbeat) {
        console.warn('缺少必要参数，无法加载聊天记录');
      }
      return;
    }

    // 心跳检测时，验证参数是否与当前活跃会话一致，避免消息串扰
    if (isHeartbeat && currentHeartbeatParams.value) {
      const currentParams = currentHeartbeatParams.value;
      if (
        props.messageData.account_id !== currentParams.account_id ||
        props.messageData.platform !== currentParams.platform ||
        props.accountData.account_id !== currentParams.self_account_id
      ) {
        console.log('⚠️ 心跳检测参数不匹配，跳过本次请求，避免消息串扰');
        return;
      }
    }

    try {
      if (!isHeartbeat) {
        console.log('📤 加载历史聊天记录');
      }

      const requestParams = {
        account_id: props.messageData.account_id,
        platform: props.messageData.platform,
        self_account_id: props.accountData.account_id,
      };

      const response = await request('/api/thread/chat_record', requestParams);

      if (response && response.data) {
        // 再次验证：确保响应时参数仍然匹配当前选中的会话
        if (isHeartbeat && currentHeartbeatParams.value) {
          const currentParams = currentHeartbeatParams.value;
          if (
            props.messageData.account_id !== currentParams.account_id ||
            props.messageData.platform !== currentParams.platform ||
            props.accountData.account_id !== currentParams.self_account_id
          ) {
            console.log('⚠️ 响应时参数已变化，丢弃本次心跳检测结果');
            return;
          }
        }

        const apiData =
          response.data.list || response.data.data || response.data || [];
        const transformedMessages = transformApiData(apiData);
        chatMessages.value = transformedMessages;

        if (!isHeartbeat) {
          console.log(
            `✅ 聊天记录加载成功: ${transformedMessages.length} 条消息`
          );
        }

        // 使用智能滚动处理
        await nextTick();
        handleSmartScroll(transformedMessages, isHeartbeat);
      } else {
        throw new Error('API返回数据格式错误');
      }
    } catch (err) {
      // 心跳检测失败时静默处理，避免频繁错误提示
      if (!isHeartbeat) {
        console.error('❌ 加载聊天记录失败:', err);
      }
    }
  };

  // 停止心跳检测
  const stopHeartbeat = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value);
      heartbeatTimer.value = null;
      currentHeartbeatParams.value = null;
      console.log('❌ 聊天弹窗心跳检测已停止');
    }
  };

  // 启动心跳检测
  const startHeartbeat = () => {
    // 清除现有定时器
    stopHeartbeat();

    // 只有当同时存在 messageData 和 accountData 时才启动心跳检测
    if (
      !props.messageData?.account_id ||
      !props.accountData?.account_id ||
      !dialogVisible.value
    ) {
      console.log('⚠️ 缺少必要参数或弹窗未打开，无法启动心跳检测');
      return;
    }

    // 保存当前心跳检测的参数，用于消息隔离验证
    currentHeartbeatParams.value = {
      account_id: props.messageData.account_id,
      platform: props.messageData.platform,
      self_account_id: props.accountData.account_id,
    };

    console.log(
      '💓 启动聊天弹窗心跳检测，间隔:',
      HEARTBEAT_INTERVAL / 1000,
      '秒',
      {
        account_id: currentHeartbeatParams.value.account_id,
        platform: currentHeartbeatParams.value.platform,
        self_account_id: currentHeartbeatParams.value.self_account_id,
      }
    );

    heartbeatTimer.value = window.setInterval(() => {
      // 静默获取数据，不显示加载状态，标记为心跳检测请求
      loadChatHistory(true);
    }, HEARTBEAT_INTERVAL);
  };

  // 处理弹窗打开
  const handleDialogOpen = async () => {
    console.log('🎯 聊天弹窗打开，is_receive:', props.messageData?.is_receive);

    // 重置状态
    inputMessage.value = '';
    sendingStatus.value = 'idle';
    chatMessages.value = [];
    lastMessageIds.value = new Set();

    // 加载历史聊天记录
    await loadChatHistory(false);

    // 启动心跳检测
    startHeartbeat();

    // 聚焦输入框
    await nextTick();
    inputRef.value?.focus();
  };

  // 监听 visible prop 变化
  watch(
    () => props.visible,
    (newVisible) => {
      dialogVisible.value = newVisible;
      if (newVisible) {
        handleDialogOpen();
      } else {
        // 弹窗关闭时停止心跳检测
        stopHeartbeat();
      }
    },
    { immediate: true }
  );

  // 监听内部 visible 状态变化
  watch(dialogVisible, (newVisible) => {
    emit('update:visible', newVisible);
    if (!newVisible) {
      // 弹窗关闭时停止心跳检测
      stopHeartbeat();
      emit('close');
    }
  });

  // 组件销毁时清理定时器
  onUnmounted(() => {
    console.log('🔥 ChatDialog 组件销毁，清理心跳检测');
    stopHeartbeat();
  });

  // 获取弹窗标题
  const getDialogTitle = () => {
    const username = props.messageData?.account_name || '用户';
    return `与 ${username}`;
  };

  // 发送消息的 API 函数
  const sendChatMessage = async (messageContent: string) => {
    try {
      console.log(
        '📤 调用发送消息 API:',
        messageContent,
        'is_receive:',
        props.messageData?.is_receive
      );

      if (props.messageData?.is_receive === 0) {
        // 陌拜场景：使用评论私信接口
        const params = {
          content: messageContent,
          content_type: 'text',
          from_account_id: props.accountData?.account_id,
          tread_id: props.messageData?.tread_id || props.messageData?.id,
        };

        const response = await request('/api/account/sendMsgComment', params);

        if (response && response.code === 0) {
          console.log('✅ 陌拜消息发送成功');
          return true;
        }
        console.error('❌ 陌拜消息发送失败:', response?.msg || '未知错误');
        Message.error(response?.msg || '发送失败');
        return false;
      }
      // 正常聊天场景：使用原有聊天接口
      const params = {
        platform: props.messageData?.platform || '',
        account_id: props.messageData?.account_id || '',
        clue_id: props.messageData?.clue_id || props.messageData?.id,
        message: messageContent,
        type: 'text',
      };

      const response = await request('/api/thread/sendDYChatV2', params);

      if (response && response.code === 0) {
        console.log('✅ 正常聊天消息发送成功');
        return true;
      }
      console.error('❌ 正常聊天消息发送失败:', response?.msg || '未知错误');
      Message.error(response?.msg || '发送失败');
      return false;
    } catch (err) {
      console.error('❌ 发送消息 API 调用失败:', err);
      Message.error('发送失败，请重试');
      return false;
    }
  };

  // 发送名片功能
  const sendCard = async () => {
    // 检查是否有必要的数据
    if (!props.messageData?.clue_id && !props.messageData?.id) {
      console.warn('没有线索ID，无法发送名片');
      Message.warning('缺少必要信息，无法发送名片');
      return;
    }

    // 检查是否正在发送中
    if (sendingStatus.value === 'sending') {
      console.warn('消息正在发送中，请稍候');
      return;
    }

    try {
      // 设置发送状态
      sendingStatus.value = 'sending';
      const clueId = props.messageData?.clue_id || props.messageData?.id;
      console.log('📇 开始发送名片，线索ID:', clueId);

      // 第一步：获取名片信息
      const cardResponse = await request('/api/clue/getCardInfo', {
        clue_id: clueId,
      });

      if (!cardResponse || cardResponse.code !== 0) {
        throw new Error(cardResponse?.msg || '获取名片信息失败');
      }

      const cardInfo = cardResponse.data;
      if (!cardInfo || !cardInfo.image_url || !cardInfo.text) {
        throw new Error('名片信息不完整');
      }

      console.log('✅ 名片信息获取成功:', cardInfo);

      // 第二步：按顺序发送两条消息
      // 先发送图片消息
      console.log('📤 发送名片图片...');
      const imageParams = {
        platform: props.messageData?.platform || '',
        account_id: props.messageData?.account_id || '',
        clue_id: clueId,
        image_url: cardInfo.image_url,
        type: 'image',
      };

      const imageResponse = await request(
        '/api/thread/sendDYChatV2',
        imageParams
      );

      if (!imageResponse || imageResponse.code !== 0) {
        throw new Error('发送名片图片失败');
      }

      // 再发送文本消息
      console.log('📤 发送名片文本...');
      const textParams = {
        platform: props.messageData?.platform || '',
        account_id: props.messageData?.account_id || '',
        clue_id: clueId,
        message: cardInfo.text,
        type: 'text',
      };

      const textResponse = await request(
        '/api/thread/sendDYChatV2',
        textParams
      );

      if (!textResponse || textResponse.code !== 0) {
        throw new Error('发送名片文本失败');
      }

      // 发送成功
      sendingStatus.value = 'success';
      console.log('✅ 名片发送成功');
      Message.success('名片发送成功');

      // 刷新消息列表
      setTimeout(() => {
        loadChatHistory(false);
      }, 1000);
    } catch (err: any) {
      // 发送失败
      sendingStatus.value = 'failed';
      console.error('❌ 名片发送失败:', err);
    } finally {
      // 重置发送状态
      setTimeout(() => {
        sendingStatus.value = 'idle';
      }, 1000);
    }
  };

  // 处理发送消息事件
  const handleSendMessage = async (event?: KeyboardEvent) => {
    // 如果是回车键事件，检查是否按住了Shift键
    if (event && event.key === 'Enter') {
      if (event.shiftKey) {
        return;
      }
      event.preventDefault();
    }

    const messageContent = inputMessage.value?.trim();
    if (!messageContent) {
      return;
    }

    if (sendingStatus.value === 'sending') {
      return;
    }

    try {
      sendingStatus.value = 'sending';

      // 创建新消息对象并添加到聊天列表
      const newMessage = {
        id: `temp_${Date.now()}`,
        content: messageContent,
        time: dayjs().format('HH:mm'),
        type: 'sent',
        send_status: 1, // 发送中
      };

      chatMessages.value.push(newMessage);
      inputMessage.value = '';

      // 滚动到底部
      await nextTick();
      scrollToBottom();

      // 调用 API 发送消息
      const success = await sendChatMessage(messageContent);

      if (success) {
        newMessage.send_status = 3;
        sendingStatus.value = 'success';
        console.log('✅ 消息发送成功');

        // 发送成功后立即刷新聊天记录
        setTimeout(() => {
          loadChatHistory(false);
        }, 1000);
      } else {
        newMessage.send_status = 5;
        sendingStatus.value = 'failed';
      }
    } catch (err) {
      console.error('❌ 发送消息失败:', err);
      sendingStatus.value = 'failed';
    } finally {
      setTimeout(() => {
        sendingStatus.value = 'idle';
      }, 2000);
    }
  };

  // 处理输入框内容变化
  const handleInputChange = (value: string) => {
    inputMessage.value = value;
  };

  // 表情包选择处理
  const selectEmoji = (emoji: string) => {
    if (!inputRef.value?.textareaRef) {
      console.warn('输入框引用不存在');
      return;
    }

    const textareaElement = inputRef.value.textareaRef;
    const currentValue = inputMessage.value || '';
    const cursorPosition =
      textareaElement.selectionStart || currentValue.length;

    // 在光标位置插入表情包
    const newValue =
      currentValue.slice(0, cursorPosition) +
      emoji +
      currentValue.slice(cursorPosition);

    // 更新输入框内容
    inputMessage.value = newValue;

    // 设置光标位置到表情包后面
    nextTick(() => {
      textareaElement.focus();
      const newCursorPosition = cursorPosition + emoji.length;
      textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
    });

    console.log('😊 表情包已插入:', emoji);
  };

  // 获取发送消息时的用户名
  const getSentMessageUsername = () => {
    if (props.accountData?.account_name) {
      return props.accountData.account_name;
    }
    if (props.accountData?.name) {
      return props.accountData.name;
    }
    return '我';
  };

  // 获取发送消息时的头像URL
  const getSentMessageAvatar = () => {
    if (props.accountData?.avatar) {
      return props.accountData.avatar;
    }
    if (props.accountData?.avatar_url) {
      return props.accountData.avatar_url;
    }
    return null;
  };

  // 获取发送消息时的头像文字
  const getSentMessageAvatarText = () => {
    const username = getSentMessageUsername();
    return username.charAt(0);
  };

  // 处理关闭
  const handleClose = () => {
    dialogVisible.value = false;
  };

  // 处理头像加载错误
  const handleAvatarError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    if (!img.src.includes('/icons/common/avatar.png')) {
      img.src = '/icons/common/avatar.png';
    } else {
      img.removeAttribute('src');
    }
  };
</script>

<style scoped lang="less">
  // 弹窗样式
  .chat-dialog-modal {
    :deep(.arco-modal-body) {
      padding: 0;
      height: 500px;
      overflow: hidden;
    }
  }

  .chat-dialog-container {
    height: 500px;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #f8f9fb 0%, #f2f4f7 100%);
    border-radius: var(--border-radius-large);
  }

  // 聊天头部信息
  .chat-header {
    flex-shrink: 0;
    padding: 16px 20px;
    border-bottom: 1px solid var(--color-border-1);
    // background: var(--color-bg-2);

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .user-details {
        flex: 1;

        .username {
          font-size: 16px;
          font-weight: 500;
          color: var(--color-text-1);
          margin-bottom: 4px;
        }

        .user-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: var(--color-text-3);

          .platform {
            padding: 2px 6px;
            background: var(--color-fill-2);
            border-radius: 4px;
          }

          .scenario-tag {
            padding: 2px 6px;
            background: var(--color-primary-light-1);
            color: var(--color-primary-6);
            border-radius: 4px;
            font-weight: 500;
          }
        }
      }
    }
  }

  // 聊天内容区域
  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // background: linear-gradient(180deg, #f8f9fb 0%, #f2f4f7 100%);
    // border-radius: var(--border-radius-large);

    // 空状态
    .chat-empty-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .empty-icon {
          font-size: 48px;
          color: var(--color-text-4);
        }

        .empty-text {
          color: var(--color-text-3);
          font-size: 14px;
        }
      }
    }

    // 消息列表 - 复用 ChatWindow 样式
    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      position: relative;

      // 修改滚动条颜色
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        transition: background-color 0.15s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }
      }

      .chat-message-wrapper {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      // 消息行布局 - 复用 ChatWindow 样式
      .message-row {
        display: flex;
        gap: 8px;
        align-items: flex-start;

        // 接收消息布局
        &.received {
          justify-content: flex-start;

          .message-content {
            max-width: 70%;
            align-items: flex-start;

            .message-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 2px;

              .username {
                font-size: 12px;
                color: var(--color-text-3);
                font-weight: 500;
              }

              .message-time {
                font-size: 11px;
                color: var(--color-text-3);
              }
            }

            .message-bubble {
              position: relative;
              background: var(--color-bg-1);
              color: var(--color-text-1);
              border: 1px solid #e1e4e8;
              text-align: left;
              box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
              padding: 8px 12px;
              border-radius: 12px;
              font-size: 14px;
              line-height: 1.5;
              word-wrap: break-word;
              max-width: max-content;
            }
          }
        }

        // 发送消息布局
        &.sent {
          justify-content: flex-end;

          .message-content {
            display: flex;
            flex-direction: column;
            max-width: 70%;
            align-items: flex-end;

            .message-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 2px;
              justify-content: flex-end;

              .username {
                font-size: 12px;
                color: var(--color-text-3);
                font-weight: 500;
              }

              .message-time {
                font-size: 11px;
                color: var(--color-text-3);
              }
            }

            .message-bubble {
              position: relative;
              background: #0984e3;
              color: #ffffff;
              border: none;
              text-align: left;
              box-shadow: 0 3px 12px rgba(116, 185, 255, 0.25);
              padding: 8px 12px;
              border-radius: 12px;
              font-size: 14px;
              line-height: 1.5;
              word-wrap: break-word;
              max-width: max-content;

              // 发送消息状态图标样式
              .message-status-icon {
                position: absolute;
                left: -20px;
                bottom: 0;
              }

              .success_icon {
                color: rgb(var(--success-6));
              }
            }
          }
        }

        // 头像样式 - 复用 ChatWindow 样式
        .message-avatar {
          flex-shrink: 0;

          .arco-avatar {
            border: 1px solid var(--color-bg-1);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

            // 当没有头像图片时，显示带背景色的默认头像
            &:not([src]) {
              font-weight: 500;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  // 聊天输入模块 - 复用 ChatWindow 样式
  .chat-input-module {
    flex-shrink: 0;

    // 主输入区域
    .input-main-area {
      padding: 10px 10px 5px;
      background: var(--color-bg-2);
      margin: 0 20px 10px;
      border-radius: var(--border-radius-large);
      box-shadow: 0 0px 4px rgba(0, 0, 0, 0.05);

      // 输入工具栏样式
      .input-toolbar {
        display: flex;
        gap: 8px;
        padding-left: 8px;

        .toolbar-btn {
          border-radius: 8px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
          }
          width: 20px;
          height: 20px;
          cursor: pointer;
        }

        .emoji-btn {
          color: var(--color-text-2);

          &:hover {
            color: var(--color-primary-6);
            background: var(--color-fill-2);
          }
        }
      }

      .input-content {
        position: relative;
        display: flex;
        gap: 12px;
        align-items: flex-end;
        margin-top: 5px;

        .message-textarea {
          flex: 1;
          border-radius: 12px;
          border: none;
          background: var(--color-bg-1);
          font-size: 14px;
          line-height: 1.5;
          transition: all 0.2s ease;

          &:focus {
            border-color: var(--color-primary-6);
            box-shadow: 0 0 0 2px rgba(var(--primary-6), 0.1);
          }

          // 隐藏滚动条
          -ms-overflow-style: none;

          &::-webkit-scrollbar {
            display: none;
          }

          // 深度选择器，确保内部元素也隐藏滚动条
          :deep(.arco-textarea) {
            -ms-overflow-style: none;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          :deep(textarea) {
            -ms-overflow-style: none;
            scrollbar-width: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          // 确保 Arco Design 内部组件也应用动画效果
          &:focus-within {
            :deep(.arco-textarea) {
              border: none !important;
              box-shadow: none !important;
            }
          }
        }

        .send-action {
          position: absolute;
          right: 5px;
          bottom: 5px;
          z-index: 999;

          .arco-btn {
            border-radius: 12px;
            padding: 8px 20px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(var(--primary-6), 0.3);
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(var(--primary-6), 0.4);
            }

            // 发送成功状态
            &.success {
              background: var(--color-success-6);
              border-color: var(--color-success-6);
              box-shadow: 0 2px 4px rgba(var(--success-6), 0.3);

              &:hover {
                background: var(--color-success-5);
                border-color: var(--color-success-5);
              }
            }

            // 发送失败状态
            &.failed {
              background: var(--color-danger-6);
              border-color: var(--color-danger-6);
              box-shadow: 0 2px 4px rgba(var(--danger-6), 0.3);

              &:hover {
                background: var(--color-danger-5);
                border-color: var(--color-danger-5);
              }
            }

            // 禁用状态
            &:disabled {
              opacity: 0.6;
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .chat-dialog-container {
      height: 400px;
    }

    .chat-content .chat-messages {
      padding: 12px;

      .message-row .message-content {
        max-width: 80%;
      }
    }

    .chat-input-module .input-main-area {
      margin: 0 12px 8px;
      padding: 8px;
    }
  }
</style>

<!--弹窗表格-无操作-->
<template>
  <d-modal
    v-model:visible="visible"
    :title="title"
    width="800px"
    unmount-on-close
    :footer="null"
    :body-style="{ maxHeight: '60vh' }"
  >
    <a-card>
      <div style="white-space: pre-line">
        {{ content }}
      </div>
    </a-card>
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { ref } from 'vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
  });

  const visible = ref(false);
  const content = ref('');

  const show = (data: any) => {
    visible.value = true;
    content.value = data;
  };

  defineExpose({
    show,
  });
</script>

<style scoped></style>

<template>
  <div v-show="visible" class="popup-layer">
    <div class="video-player-wrapper">
      <video
        v-if="videoUrl"
        ref="videoRef"
        :autoplay="isAutoPlay"
        class="video-player-ele"
        :style="{ opacity: isReady ? 1 : 0 }"
        controls
        @canplay="isReady = true"
      >
        <source v-for="(item, index) in playUrl" :key="index" :src="item" />
      </video>
      <img
        v-if="!isReady"
        style="position: fixed; width: 400px"
        src="./loading.gif"
        alt=""
      />
    </div>
    <i v-if="showClose" class="popup-layer-btn-close" @click="close">x</i>
    <template v-if="video_list && video_list.length">
      <IconLeft
        v-if="play_index !== 0"
        class="popup-layer-btn popup-layer-btn-left"
        @click="playVideo(play_index - 1)"
      />
      <IconRight
        v-if="play_index + 1 < video_list.length"
        class="popup-layer-btn popup-layer-btn-right"
        @click="playVideo(play_index + 1)"
      />
    </template>
    <div v-if="playInfo && playInfo.title" class="popup-layer-title">
      <span v-if="video_list.length > 0">({{ play_index + 1 }})</span>
      <span class="title-label"> 视频名称: </span>{{ playInfo.title || '-' }}
      <span class="title-label"> 视频ID: </span>{{ playInfo.id || '-' }}
    </div>
  </div>
</template>

<script>
  import { getNoMarkUrl } from '@/utils/util';
  import { IconLeft, IconRight } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';

  export default {
    name: 'PopupVideoPlayer',
    components: {
      IconLeft,
      IconRight,
    },
    emits: ['update:visible'],
    data() {
      return {
        visible: false,
        videoUrl: '',
        message: '',
        onClose: null,
        showClose: true,
        closed: false,
        isAutoPlay: true,
        video_list: [], // {url,title} 需要播放的视频列表
        play_index: 0, // 当前正在播放的视频索引
        isReady: false, // 视频是否加载就绪（防闪烁）
        canSwitch: true, // 视频是否切换（防闪烁）
      };
    },
    computed: {
      playUrl() {
        return [getNoMarkUrl(this.videoUrl), this.videoUrl];
      },
      playInfo() {
        return this.video_list[this.play_index];
      },
    },
    watch: {
      visible(val) {
        if (val) {
          document.addEventListener('keydown', this.keydown);
        } else {
          document.removeEventListener('keydown', this.keydown);
        }
      },
    },
    created() {
      if (this.video_list?.length > 1) {
        this.play_index =
          this.video_list.findIndex((item) => item.url === this.videoUrl) || 0;
      }
    },
    expose: ['play'],
    methods: {
      playMediaVideo() {
        if (this.videoUrl?.includes('oceanengine.com')) {
          let url = this.videoUrl;
          this.close();
          let arg = `\u003cscript\u003elocation.replace("${url}")\u003c/script\u003e`;
          // eslint-disable-next-line no-script-url
          window.open('javascript:window.name;', arg);
        } else {
          this.visible = true;
        }
      },
      play(data) {
        Object.keys(data).forEach((key) => {
          this[key] = data[key];
        });
        this.playMediaVideo();
      },
      playVideo(index) {
        if (index > -1 && index < this.video_list.length) {
          this.play_index = index;
          this.videoUrl = '';
          this.isReady = false;
          this.$nextTick(() => {
            this.videoUrl = this.video_list[this.play_index].url;
          });
        }
      },
      close() {
        this.visible = false;
        this.videoUrl = '';
        this.play_index = 0;
        this.video_list = [];
        this.isReady = false;
        this.isAutoPlay = true;
        if (this.$refs.videoRef) {
          this.$refs.videoRef.pause();
        }
        if (typeof this.onClose === 'function') {
          this.onClose(this);
        }
        // setTimeout(() => {
        //   this.$destroy(true);
        //   this.$el.parentNode.removeChild(this.$el);
        // }, 500);
      },
      keydown(e) {
        switch (e.keyCode) {
          // esc关闭消息
          case 27:
            this.close();
            break;
          // 下一个
          case 39:
            if (
              this.play_index + 1 < this.video_list.length &&
              this.canSwitch
            ) {
              this.playVideo(this.play_index + 1);
              this.canSwitch = false;
              setTimeout(() => {
                this.canSwitch = true;
              }, 500);
            }
            break;
          // 上一个
          case 37:
            if (this.play_index !== 0 && this.canSwitch) {
              this.playVideo(this.play_index - 1);
              this.canSwitch = false;
              setTimeout(() => {
                this.canSwitch = true;
              }, 500);
            }
            break;
          default:
            break;
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .popup-layer {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.9);
    color: #fff;
    z-index: 9999;

    .video-player-wrapper {
      position: absolute;
      inset: 0;
      display: flex;
      justify-content: center;
      align-items: center;

      .video-player-ele {
        width: calc(100% - 5vw);
        height: calc(100% - 5vw);
        object-fit: scale-down;
      }

      .no-video-tips {
      }
    }

    .popup-layer-btn-close {
      position: absolute;
      right: 20px;
      top: 20px;
      width: 50px;
      background: rgba(0, 0, 0, 0.3);
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      font-style: initial;
      font-size: 30px;
      line-height: 30px;
      cursor: pointer;
      padding: 6px 0 12px;
      border-radius: 100%;
      user-select: none;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
        color: rgba(255, 255, 255, 1);
      }
    }
    .popup-layer-btn {
      font-size: 40px;
      position: absolute;
      top: 50vh;
      transform: translateY(-50%);
      opacity: 0.4;
      color: #eeeeee;
      &:hover {
        opacity: 1;
      }
    }
    .popup-layer-btn-left {
      left: 20px;
    }
    .popup-layer-btn-right {
      right: 20px;
    }
    .popup-layer-title {
      text-align: center;
      padding: 4px 10px;
      background-color: rgba(0, 0, 0, 0.2);
      position: absolute;
      left: 0;
      right: 0;
      font-size: 14px;
      top: 0;
      border-radius: 4px;
      p {
        margin: 0;
      }
      .title-label {
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
</style>

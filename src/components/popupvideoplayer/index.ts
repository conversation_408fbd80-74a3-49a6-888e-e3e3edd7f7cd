import { createApp } from 'vue';
import type { App, AppContext } from 'vue';
import PlayerCom from './popupVideoPlayer.vue';

export default {
  install(app: App) {
    const PlayerC = createApp(PlayerCom);
    const PlayerEl = PlayerC.mount(document.createElement('div'));
    document.body.appendChild(PlayerEl.$el);
    // @ts-ignore
    app.config.globalProperties.$playvideo = PlayerEl.play;
  },
};

import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/store';
import { intersection } from 'lodash';

export default function usePermission() {
  const userStore = useUserStore();
  return {
    accessRouter(route: RouteLocationNormalized | RouteRecordRaw) {
      return (
        !route.meta?.requiresAuth ||
        !route.meta?.roles ||
        route.meta?.roles?.includes('*') ||
        intersection(route.meta?.roles || [], userStore.roles || []).length
      );
    },
    findFirstPermissionRoute(_routers: any, roles = []) {
      const cloneRouters = [..._routers];
      while (cloneRouters.length) {
        const firstElement = cloneRouters.shift();
        if (
          firstElement?.meta?.roles?.includes('*') ||
          intersection(firstElement?.meta?.roles, roles).length > 0
          // firstElement?.meta?.roles?.find((el: (number | string)[]) => {
          //  return el.includes('*') || intersection(el, roles).length > 0;
          // })
        )
          return { name: firstElement.name };
        if (firstElement?.children) {
          cloneRouters.push(...firstElement.children);
        }
      }
      return null;
    },
    // You can add any rules you want
  };
}

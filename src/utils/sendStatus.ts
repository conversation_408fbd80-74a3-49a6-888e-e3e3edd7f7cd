/* eslint-disable no-shadow */
/**
 * 通用发送状态管理工具
 * 统一管理消息发送状态的定义、转换和显示逻辑
 */

// 发送状态枚举 - 统一定义
export const enum SendStatus {
  PENDING = -1, // 待发送
  SENDING = 1, // 发送中
  SUCCESS = 3, // 发送成功
  FAILED = 5, // 发送失败
}

// 发送状态配置接口
export interface SendStatusConfig {
  value: SendStatus;
  label: string;
  color: string;
  icon: string;
  description?: string;
}

// 发送状态配置映射
export const SEND_STATUS_CONFIG: Record<SendStatus, SendStatusConfig> = {
  [SendStatus.PENDING]: {
    value: SendStatus.PENDING,
    label: '待发送',
    color: '#86909C',
    icon: 'IconClock',
    description: '消息等待发送',
  },
  [SendStatus.SENDING]: {
    value: SendStatus.SENDING,
    label: '发送中',
    color: '#165DFF',
    icon: 'IconLoading',
    description: '消息正在发送',
  },
  [SendStatus.SUCCESS]: {
    value: SendStatus.SUCCESS,
    label: '发送成功',
    color: '#00B42A',
    icon: 'IconCheck',
    description: '消息发送成功',
  },
  [SendStatus.FAILED]: {
    value: SendStatus.FAILED,
    label: '发送失败',
    color: '#F53F3F',
    icon: 'IconExclamation',
    description: '消息发送失败',
  },
};

// 发送状态工具类
export class SendStatusUtils {
  /**
   * 获取发送状态配置
   * @param status 发送状态值
   * @returns 状态配置对象
   */
  static getConfig(status: SendStatus | number): SendStatusConfig {
    const statusValue =
      typeof status === 'number' ? (status as SendStatus) : status;
    return (
      SEND_STATUS_CONFIG[statusValue] || SEND_STATUS_CONFIG[SendStatus.FAILED]
    );
  }

  /**
   * 获取状态标签
   * @param status 发送状态值
   * @returns 状态标签文本
   */
  static getLabel(status: SendStatus | number): string {
    return this.getConfig(status).label;
  }

  /**
   * 获取状态颜色
   * @param status 发送状态值
   * @returns 状态颜色值
   */
  static getColor(status: SendStatus | number): string {
    return this.getConfig(status).color;
  }

  /**
   * 获取状态图标
   * @param status 发送状态值
   * @returns 状态图标名称
   */
  static getIcon(status: SendStatus | number): string {
    return this.getConfig(status).icon;
  }

  /**
   * 获取状态描述
   * @param status 发送状态值
   * @returns 状态描述文本
   */
  static getDescription(status: SendStatus | number): string {
    return this.getConfig(status).description || '';
  }

  /**
   * 判断是否为发送中状态
   * @param status 发送状态值
   * @returns 是否为发送中
   */
  static isSending(status: SendStatus | number): boolean {
    return status === SendStatus.SENDING;
  }

  /**
   * 判断是否为发送成功状态
   * @param status 发送状态值
   * @returns 是否为发送成功
   */
  static isSuccess(status: SendStatus | number): boolean {
    return status === SendStatus.SUCCESS;
  }

  /**
   * 判断是否为发送失败状态
   * @param status 发送状态值
   * @returns 是否为发送失败
   */
  static isFailed(status: SendStatus | number): boolean {
    return status === SendStatus.FAILED;
  }

  /**
   * 判断是否为待发送状态
   * @param status 发送状态值
   * @returns 是否为待发送
   */
  static isPending(status: SendStatus | number): boolean {
    return status === SendStatus.PENDING;
  }

  /**
   * 获取所有状态配置列表（用于下拉选择等场景）
   * @returns 状态配置列表
   */
  static getAllConfigs(): SendStatusConfig[] {
    return Object.values(SEND_STATUS_CONFIG);
  }

  /**
   * 获取状态选项列表（用于 Arco Design 组件）
   * @returns 选项列表
   */
  static getOptions(): Array<{
    label: string;
    value: SendStatus;
    color?: string;
  }> {
    return this.getAllConfigs().map((config) => ({
      label: config.label,
      value: config.value,
      color: config.color,
    }));
  }

  /**
   * 转换旧版本状态值到新版本
   * @param oldStatus 旧版本状态值
   * @returns 新版本状态值
   */
  static convertLegacyStatus(oldStatus: any): SendStatus {
    // 处理字符串状态
    if (typeof oldStatus === 'string') {
      switch (oldStatus.toLowerCase()) {
        case 'sending':
          return SendStatus.SENDING;
        case 'sent':
        case 'success':
          return SendStatus.SUCCESS;
        case 'failed':
        case 'error':
          return SendStatus.FAILED;
        case 'pending':
        case 'idle':
          return SendStatus.PENDING;
        default:
          return SendStatus.FAILED;
      }
    }

    // 处理数字状态
    if (typeof oldStatus === 'number') {
      switch (oldStatus) {
        case -1:
          return SendStatus.PENDING;
        case 1:
          return SendStatus.SENDING;
        case 3:
          return SendStatus.SUCCESS;
        case 5:
          return SendStatus.FAILED;
        default:
          return SendStatus.FAILED;
      }
    }

    return SendStatus.FAILED;
  }

  /**
   * 创建新的消息状态对象
   * @param initialStatus 初始状态
   * @returns 消息状态对象
   */
  static createMessageStatus(initialStatus: SendStatus = SendStatus.PENDING) {
    return {
      status: initialStatus,
      timestamp: Date.now(),
      retryCount: 0,

      // 状态更新方法
      updateStatus(newStatus: SendStatus) {
        this.status = newStatus;
        this.timestamp = Date.now();
        if (newStatus === SendStatus.FAILED) {
          this.retryCount += 1;
        }
      },

      // 获取当前状态配置
      getConfig() {
        return SendStatusUtils.getConfig(this.status);
      },

      // 判断状态方法
      isSending() {
        return SendStatusUtils.isSending(this.status);
      },

      isSuccess() {
        return SendStatusUtils.isSuccess(this.status);
      },

      isFailed() {
        return SendStatusUtils.isFailed(this.status);
      },

      isPending() {
        return SendStatusUtils.isPending(this.status);
      },
    };
  }

  /**
   * 批量转换状态数组
   * @param statusArray 状态数组
   * @returns 转换后的状态数组
   */
  static convertStatusArray(statusArray: any[]): SendStatus[] {
    return statusArray.map((status) => this.convertLegacyStatus(status));
  }

  /**
   * 获取状态统计信息
   * @param statusArray 状态数组
   * @returns 状态统计对象
   */
  static getStatusStats(
    statusArray: (SendStatus | number)[]
  ): Record<string, number> {
    const stats = {
      pending: 0,
      sending: 0,
      success: 0,
      failed: 0,
      total: statusArray.length,
    };

    statusArray.forEach((status) => {
      if (this.isPending(status)) stats.pending += 1;
      else if (this.isSending(status)) stats.sending += 1;
      else if (this.isSuccess(status)) stats.success += 1;
      else if (this.isFailed(status)) stats.failed += 1;
    });

    return stats;
  }

  /**
   * 格式化状态显示文本（带颜色信息）
   * @param status 发送状态值
   * @param showIcon 是否显示图标
   * @returns 格式化的显示对象
   */
  static formatStatusDisplay(status: SendStatus | number, showIcon = true) {
    const config = this.getConfig(status);
    return {
      text: config.label,
      color: config.color,
      icon: showIcon ? config.icon : null,
      description: config.description,
    };
  }
}

// 用户可以直接使用 SendStatus.PENDING, SendStatus.SENDING 等

// 默认导出工具类
export default SendStatusUtils;

import { Message, Notification } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import request from '@/api/request';

// 初始化 dayjs duration 插件
dayjs.extend(duration);

interface DownloadOptions {
  taskId: string;
  pollingInterval?: number;
  onProgress?: (elapsedTime: string) => void;
  onSuccess?: (fileUrl: string) => void;
  onError?: (error: any) => void;
}

interface DownloadTaskInfo {
  file_url?: string;
  status?: string;
  progress?: number;
}

/**
 * 格式化耗时显示
 */
function formatElapsedTime(startTime: number): string {
  const elapsedMs = Date.now() - startTime;
  const timeDuration = dayjs.duration(elapsedMs);
  const seconds = timeDuration.seconds();
  const milliseconds = timeDuration.milliseconds();
  return `${seconds}.${Math.floor(milliseconds / 10)
    .toString()
    .padStart(2, '0')}`;
}

/**
 * 下载文件
 * @param url 文件URL
 * @param fileName 文件名
 */
export function downloadFile(url: string, fileName?: string): void {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName || `download_${Date.now()}.zip`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * 轮询下载任务状态
 * @param options 配置选项
 */
export async function pollDownloadTask(
  options: DownloadOptions
): Promise<void> {
  const {
    taskId,
    pollingInterval = 1000,
    onProgress,
    onSuccess,
    onError,
  } = options;

  const startTime = Date.now();
  let notificationId = `DOWNLOADING_${taskId}`;

  try {
    const checkStatus = async () => {
      try {
        const res = await request(`/api/downloadCenter/list`, { id: taskId });

        if (res.code === 0) {
          const { data } = res.data;
          const taskInfo: DownloadTaskInfo = data[0] || {};

          if (taskInfo.file_url) {
            // 下载完成
            const elapsedTime = formatElapsedTime(startTime);
            Notification.success({
              id: notificationId,
              title: '文件打包完成,正在下载...',
              content: `耗时：${elapsedTime}秒`,
              position: 'bottomRight',
              duration: 3000,
            });

            if (onSuccess) {
              onSuccess(taskInfo.file_url);
            } else {
              downloadFile(taskInfo.file_url);
            }
            return true;
          }
          // 继续轮询
          const elapsedTime = formatElapsedTime(startTime);
          Notification.warning({
            id: notificationId,
            title: '文件正在打包中...',
            content: `已执行：${elapsedTime}秒`,
            position: 'bottomRight',
            duration: 0,
          });

          if (onProgress) {
            onProgress(elapsedTime);
          }

          setTimeout(checkStatus, pollingInterval);
          return false;
        }
      } catch (error) {
        console.error('Polling error:', error);
        if (onError) {
          onError(error);
        } else {
          Message.error('获取下载状态失败');
        }
        return true;
      }
    };

    await checkStatus();
  } catch (error) {
    console.error('Download task error:', error);
    if (onError) {
      onError(error);
    } else {
      Message.error('下载任务失败');
    }
  }
}

/**
 * 创建下载任务并开始轮询
 * @param api 创建下载任务的API
 * @param params 请求参数
 * @param options 下载选项
 */
export async function createAndPollDownload(
  api: string,
  params: any,
  options: Omit<DownloadOptions, 'taskId'>
): Promise<void> {
  try {
    const res = await request(api, params);
    if (res.code === 0 && res.data?.download_center_id) {
      const taskId = res.data.download_center_id;
      await pollDownloadTask({
        taskId,
        ...options,
      });
    }
  } catch (error) {
    console.error('Create download task error:', error);
    if (options.onError) {
      options.onError(error);
    } else {
      Message.error('创建下载任务失败');
    }
  }
}

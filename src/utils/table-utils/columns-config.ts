/*
 * @Desc 全局通用列配置方法(尽量放需要自定义列的字段或者复用比较多的字段，防止列配置的唯一key重复)
 */
import { h } from 'vue';
import { TableColumnData, TableData } from '@arco-design/web-vue';
import { getPathValue } from '@/utils/util';
import { switchM } from '@/components/dict-select/dict-common';
import { isArray } from 'lodash';
import { getText } from '@/components/dict-select/dict-util';
import dayjs from 'dayjs';
import {
  moneyFormat,
  numberFormat,
  rateFormat,
  formatEmptyStr,
} from './table-util';

/**
 * @description 获取展示的数据
 * @param key {string} 数据的key
 * @param data {object} 当前行数据
 * @return {string} 展示
 */
export const getTextFromData = (
  { record, column, rowIndex }: TableColumnSlot,
  key?: any
) => {
  if (key) {
    return getPathValue(record, key);
  }
  let val = record[column.dataIndex || ''];
  return val || val === 0 ? val : '';
};
export const defaultEmptyShow = (key?: any) => {
  return (data: TableColumnSlot) => {
    return h('span', `${formatEmptyStr(getTextFromData(data, key))}`);
  };
};
export const rateFormatShow = (key?: any) => {
  return (data: TableColumnSlot) =>
    h('span', `${rateFormat(getTextFromData(data, key))}`);
};
export const numberFormatShow = (key?: any) => {
  return (data: TableColumnSlot) =>
    h('span', `${numberFormat(getTextFromData(data, key))}`);
};
export const moneyFormatShow = (key?: any) => {
  return (data: TableColumnSlot) =>
    h('span', `${moneyFormat(getTextFromData(data, key))}`);
};

export const dateFormatShow = (key?: any, format = 'YYYY-MM-DD') => {
  return (data: TableColumnSlot) =>
    h(
      'span',
      `${
        getTextFromData(data, key)
          ? dayjs(getTextFromData(data, key)).format(format)
          : '-'
      }`
    );
};

export const stringArrShow = (key?: any) => {
  return (data: TableColumnSlot) => {
    const val = getTextFromData(data, key);
    return h('span', (isArray(val) ? val.join(',') : val) || '-');
  };
};

export const getDictTxtRender = (list: any[]) => {
  return (data: TableColumnSlot) =>
    h('span', `${formatEmptyStr(getText(list, getTextFromData(data)))}`);
};

// 汇总行
export const judgeTotalColumnShow = (key: any) => {
  return (data: {
    record: TableData;
    column: TableColumnData;
    rowIndex: number;
  }) => {
    if (!data.record.advertiser_id) {
      return h('span', `汇总`);
    }
    return h('span', `${moneyFormat(getTextFromData(data, key))}`);
  };
};

/*
 * @Desc 全局通用列配置
 */
export const columnsConfig: any = {
  accept_month: {
    dataIndex: 'accept_month',
    title: '年月',
    align: 'center',
    width: 120,
  },
  material_start_put_time: {
    dataIndex: 'material_start_put_time',
    title: '素材上线时间',
    render: defaultEmptyShow('material_start_put_time'),
  },
};

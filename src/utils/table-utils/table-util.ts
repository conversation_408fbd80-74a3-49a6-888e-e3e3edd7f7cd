import numeral from 'numeral';
import { Message, Notification } from '@arco-design/web-vue';
import request from '@/api/request';
// 全局翻页配置
export const pageConfig = (params?: any) => ({
  showSizeChanger: true,
  showPageSize: true,
  current: 1,
  pageSize: 20,
  total: 0,
  pageSizeOptions: [10, 20, 50, 100],
  showTotal: true,
  size: 'small',
  showJumper: true,
  show_total_num: '',
  show_total_key: '',
  ...params,
});

// 格式化过滤空值
export const formatEmptyStr = (str: any) => {
  return str || '-';
};

export const moneyFormat = (value: any, prefix?: string, suffix?: string) => {
  if ([undefined, null].includes(value)) {
    return '-';
  }
  if (prefix) {
    return prefix + value;
  }
  if (suffix) {
    return value + suffix;
  }
  return numeral(value).format('0,0.00');
};
export const travelFormat = (value: any, prefix?: string, suffix?: string) => {
  if ([undefined, null].includes(value)) {
    return '-';
  }
  if (prefix) {
    return prefix + value;
  }
  if (suffix) {
    return value + suffix;
  }
  return numeral(value).format('0,0.0');
};
export const numberFormat = (value: any, prefix?: string, suffix?: string) => {
  if ([undefined, null].includes(value)) {
    return '-';
  }
  if (prefix) {
    return prefix + value;
  }
  if (suffix) {
    return value + suffix;
  }
  return numeral(value).format('0,0');
};

export const rateFormat = (value: any, prefix?: string, suffix?: string) => {
  if ([undefined, null].includes(value)) {
    return '-';
  }
  if (prefix) {
    return prefix + value;
  }
  if (suffix) {
    return value + suffix;
  }
  return `${numeral(value).format('0,0.00')}%`;
};

/**
 * 下载文件 -直接通过链接下载，不经文件流
 */
export async function downloadLinkFile(url: string, fileName?: string) {
  if (!fileName) {
    fileName = url.split('/').slice(-1)?.[0] || '未知文件';
  }
  const domainName = window.location.host || '';
  if ((domainName && url.includes(domainName)) || !url.includes('//')) {
    // download属性具有同源策略 只有同一域名下的文件才能下载
    const alink = document.createElement('a');

    alink.setAttribute('download', fileName);
    alink.setAttribute('href', url);
    alink.setAttribute('target', '_blank');
    document.body.appendChild(alink);
    alink.click();
    document.body.removeChild(alink);
  } else {
    const res = await fetch(url).catch((err) => err);
    if (res.status !== 200) {
      return;
    }
    const blob = await res.blob();
    const objectUrl = window.URL.createObjectURL(blob);
    const alink = document.createElement('a');
    alink.setAttribute('download', fileName);
    alink.setAttribute('href', objectUrl);
    document.body.appendChild(alink);
    alink.click();
    document.body.removeChild(alink);
  }
}

/**
 * 打开文件
 */
export async function openFile(url: string) {
  const alink = document.createElement('a');
  alink.setAttribute('href', url);
  alink.setAttribute('target', '_blank');
  document.body.appendChild(alink);
  alink.click();
  document.body.removeChild(alink);
}

// 预览文件
export function previewForOnline(url: any) {
  if (!url) {
    return;
  }
  // let xdoUrl = `http://view.xdocin.com/xdoc?_xdoc=${encodeURIComponent(url)}`;
  let textUrl = `https://api.idocv.com/view/url?url=${encodeURIComponent(url)}`;
  let otherUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(
    url
  )}`;
  let fileExtension = url.substring(url.lastIndexOf('.') + 1);
  // ["txt", "png","gif", "jpg","pdf","pptx","xlsx","docx"] office格式走微软接口,其他的格式可以使用浏览器打开
  let hasTagList = ['png', 'gif', 'jpg', 'jpeg', 'pdf', 'mp4', 'mp3'];
  let flag = hasTagList.includes(fileExtension);
  if (flag) {
    otherUrl = url;
  }
  // txt 文件直接用浏览器打开时会出现乱码情况，因此使用第三方服务
  if (fileExtension.includes(['txt'])) {
    otherUrl = textUrl;
  }
  window.open(otherUrl, '_blank');
}

// 通用校验是否有未上传完成的文件  并提示
export const checkFileIsUploaded = async (imgArr: any[]) => {
  let passFlag = true;
  imgArr.map((item: any) => {
    if (item.status && item.status === 'uploading') {
      passFlag = false;
    }
    return null;
  });
  if (!passFlag) {
    Message.warning('文件正在上传中！请稍后再试...');
  }
  return passFlag;
};

const weeks = ['一', '二', '三', '四', '五', '六', '日'];
/**
 * @description 头条投放时段转文字
 * @param schedule_time { string } 投放时段数据 00000000011111111111111
 * @return { array } [{"week":"周一", list: [["8:00", "9:00"]]}]
 */
export const getTimes = (schedule_time: string) => {
  let arr: any = [];
  let result: any = [];
  let data = schedule_time.split('');
  Array.from({ length: 7 }, (item, index) => index).forEach((item, index) => {
    arr[index] = data.slice(index * 48, (index + 1) * 48);
  });
  arr.forEach((item: any, index: number) => {
    result[index] = {
      week: `周${weeks[index]}`,
      list: [],
    };
    let times = '';
    item.forEach((val: any, i: number) => {
      if (val === '1') {
        if (!times) {
          let hour =
            Math.floor(i / 2) < 10
              ? `0${Math.floor(i / 2)}`
              : Math.floor(i / 2);
          let minute = i % 2 === 0 ? ':00' : ':30';
          times = hour + minute;
        }
        if (item[i + 1] === '0' || i === 47) {
          let hour =
            Math.floor((i + 1) / 2) < 10
              ? `0${Math.floor((i + 1) / 2)}`
              : Math.floor((i + 1) / 2);
          let minute = (i + 1) % 2 === 0 ? ':00' : ':30';
          result[index].list.push([times, hour + minute]);
          times = '';
        }
      }
    });
    if (result[index].list.length === 0) {
      result[index] = null;
    }
  });
  return result;
};

// 导出流数据
export const ExportBlobFile = async (api: any, option: any = {}) => {
  const res = await api(option);
  let fileName = `响应头无文件名-${Date.now()}`;
  try {
    fileName = decodeURIComponent(
      res.headers['content-disposition']
        .split(';')[2]
        .split("filename*=utf-8''")[1]
    );
  } catch (e) {
    console.log(e);
  }
  let blob = new Blob([res.data], {
    type: res.data.type,
  });
  // @ts-ignore
  if (window.navigator.msSaveOrOpenBlob) {
    // @ts-ignore
    navigator.msSaveBlob(blob, fileName);
  } else {
    let link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click(); // 点击下载
    document.body.removeChild(link); // 下载完成移除元素
    window.URL.revokeObjectURL(link.href);
  }
};

// 文件下载
export const downloadProgressFile = (
  url: any,
  fileName: any,
  statusObj: any,
  taskId: any
) => {
  // "https://cms-static.pengwin.com/saas_clue/getclue/collect/batch//20250530/8737481748572655720839.zip"
  // if (url.indexOf('response-content-type=application%2Fzip') > -1) {
  //   fileName = `${fileName.split('.')[0]}.zip`;
  // }
  // 从URL中提取文件名
  fileName = url.split('/').pop() || 'download.zip';
  return new Promise((resolve) => {
    Notification.warning({
      id: `DOWNLOADING_${taskId}`,
      title: `文件正在批量下载中...`,
      content: `下载进度：${(0 * 100).toFixed(2)}%`,
      position: 'bottomRight',
      duration: 0,
    });
    // 下载资源
    let xhr = new XMLHttpRequest();
    xhr.open('GET', url);
    xhr.responseType = 'blob';
    // eslint-disable-next-line func-names
    xhr.onload = function () {
      // 请求完成
      let blob = this.response;
      // console.log(xhr);
      // 创建隐藏的可下载链接
      let eleLink = document.createElement('a');
      // eleLink.target = "_blank";
      eleLink.download = fileName;
      eleLink.style.display = 'none';
      eleLink.href = URL.createObjectURL(blob);
      // 触发点击
      document.body.appendChild(eleLink);
      eleLink.click();
      // 然后移除
      document.body.removeChild(eleLink);
      resolve({
        url,
        fileName,
      });
      Message.success(`下载文件“${fileName}”成功！`);
      // 如果是最后一个文件
      if (statusObj.current === statusObj.total) {
        Notification.clear();
      }
    };
    // eslint-disable-next-line func-names
    xhr.ontimeout = function (e) {
      // 下载超时请重试
      console.log(e);
      Message.error('下载超时请重试');
      resolve({});
      if (statusObj.current === statusObj.total) {
        Notification.clear();
      }
    };
    // eslint-disable-next-line func-names
    xhr.onerror = function (e) {
      // 下载出错
      console.log(e);
      Message.error('下载出错，请联系管理员');
      resolve({});
    };
    // 进度监听
    // eslint-disable-next-line func-names
    xhr.onprogress = function (e) {
      if (e.lengthComputable) {
        Notification.warning({
          id: `DOWNLOADING_${taskId}`,
          title: `文件正在批量下载中...`,
          content: `下载进度：${((e.loaded / e.total) * 100).toFixed(2)}%`,
          position: 'bottomRight',
          duration: 0,
        });
      }
    };

    // 发送ajax请求
    xhr.send();
  });
};

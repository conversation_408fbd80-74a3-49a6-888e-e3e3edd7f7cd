import axios, { AxiosResponse } from 'axios';
import { getToken } from '@/utils/auth';
import { isArray, isNumber, isString } from 'lodash';
import { HttpResponse } from '@/api/request';
import { Message, Notification } from '@arco-design/web-vue';
import { h, nextTick, getCurrentInstance } from 'vue';
import router from '@/router';
import { useClipboard } from '@vueuse/core';
import { ToolTipFormatterParams } from '@/types/echarts';
import dayjs from 'dayjs';
// import { app } from '@/main';

export function isDef(v: any): boolean {
  return v !== undefined && v !== null;
}

/**
 * form 表单的方式发起 export 事件
 * @param publicHost API路径
 * @param data 传输数据对象
 * @returns {Promise<AxiosResponse>}
 */
export async function exportForm(
  publicHost: string,
  data: any
): Promise<HttpResponse> {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: publicHost,
      data: data || {},
      responseType: 'blob',
      headers: {
        Authorization: getToken(),
      },
    })
      .then((res) => {
        let fileName = `响应头无文件名-${Date.now()}`;
        try {
          fileName = decodeURIComponent(
            res.headers['content-disposition']
              .split(';')[2]
              .split("filename*=utf-8''")[1]
          );
        } catch (e) {
          console.log(e);
        }
        const blob = new Blob([res.data], {
          type: 'application/octet-stream',
        });
        // @ts-ignore
        if (window.navigator.msSaveOrOpenBlob) {
          // @ts-ignore
          navigator.msSaveBlob(blob, fileName);
        } else {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          window.URL.revokeObjectURL(link.href);
        }
        resolve(res.data);
      })
      .catch((e) => {
        reject(e);
        console.log('导出失败', e);
      });
  });
}

export function numToYiOrWan(value: number, length = 2) {
  // 数字转换
  const param: any = {};
  const k = 10000;
  const sizes = ['', '万', '亿', '万亿'];
  let i;
  if (value < k) {
    param.value = value;
    param.unit = '';
  } else {
    i = Math.floor(Math.log(value) / Math.log(k));
    param.value = (value / k ** i).toFixed(length);
    param.unit = sizes[i];
  }
  param.text = Number.isNaN(param.value) ? '' : param.value + param.unit;
  return param;
}

// 文件对象转base64
export const getBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

// 把base64 转换成文件对象
export function dataURLtoFile(base64Str: string, fileName: string) {
  const arr = base64Str.split(',');
  if (arr[0]) {
    // @ts-ignore
    const mime = arr[0].match(/:(.*?);/)[1]; // base64解析出来的图片类型
    const bstr = atob(arr[1]); // 对base64串进行操作，去掉url头，并转换为byte   atob为window内置方法
    let len = bstr.length;
    const ab = new ArrayBuffer(len); // 将ASCII码小于0的转换为大于0
    const u8arr = new Uint8Array(ab); //
    // eslint-disable-next-line no-plusplus
    while (len--) {
      u8arr[len] = bstr.charCodeAt(len);
    }
    // 创建新的 File 对象实例[utf-8内容，文件名称或者路径，[可选参数，type：文件中的内容mime类型]]
    return new File([u8arr], fileName, {
      type: mime,
    });
  }
  return base64Str;
}

export const createObject = (
  path: string[],
  value: any
): { [key: string]: any } => {
  let keyPath: string[] = [];
  if (isArray(path)) keyPath = [...path];
  const key = keyPath.shift();
  if (isDef(key)) {
    if (isNumber(key)) {
      const obj = new Array(key + 1);
      obj[key] = createObject(keyPath, value);
      return obj;
    }
    // @ts-ignore
    return { [key]: createObject(keyPath, value) };
  }
  return value;
};

export const setPathValue = (
  obj: { [key: string]: any },
  path: string[] | string,
  value: any
): { [key: string]: any } => {
  let keyPath: string[] = [];
  if (isArray(path)) keyPath = [...path];
  else if (isString(path)) keyPath = path.split('.');
  const key = keyPath.shift();
  if (isDef(key)) {
    // @ts-ignore
    if (obj && isDef(obj[key])) {
      // @ts-ignore
      obj[key] = setPathValue(obj[key], keyPath, value);
    } else {
      // @ts-ignore
      obj[key] = createObject(keyPath, value);
    }
  } else obj = value;
  return obj;
};

export const getPathValue = (
  obj: { [key: string]: any },
  path: string[] | string
) => {
  let keyPath: string[] = [];
  if (isArray(path)) keyPath = [...path];
  else if (isString(path)) keyPath = path.split('.');
  if (keyPath.length) {
    return keyPath.reduce(
      (currentObj, key) => currentObj && currentObj[key],
      obj || {}
    );
  }
  return '';
};

// 设置光标位置
export const setCaretPosition = (iptDom: HTMLInputElement, pos: number) => {
  nextTick(() => {
    if (iptDom.setSelectionRange) {
      iptDom.focus();
      iptDom.setSelectionRange(pos, pos);
      // @ts-ignore
    } else if (iptDom.createTextRange) {
      // @ts-ignore
      const range = iptDom.createTextRange();
      range.moveStart('character', pos);
      range.moveEnd('character', pos);
      range.collapse(true);
      range.select();
    } else {
      iptDom.selectionStart = pos;
      iptDom.selectionEnd = pos;
    }
  });
};

// 获取字符的长度, 非英文码则计算长度为2
export function getByteLen(str: string): number {
  let len = 0;
  for (let i = 0; i < str.length; i += 1) {
    const length = str.charCodeAt(i);
    if (length >= 0 && length <= 128) {
      len += 1;
    } else {
      len += 2;
    }
  }
  return len;
}

export function getNoMarkUrl(str: string): string {
  if (str) {
    const urlArr = str.split('/');
    const lastName = urlArr.pop() || '';
    const fileExtension = lastName.substring(lastName.lastIndexOf('.') + 1);
    const fileName = lastName.substring(0, lastName.lastIndexOf('.'));
    urlArr.push(`${fileName.split('').reverse().join('')}.${fileExtension}`);
    return urlArr.join('/');
  }
  return '';
}

export const handleNotification = (type: any) => {
  const id = `${Date.now()}`;
  Notification.clear();
  Notification.info({
    id,
    title: '提示',
    content: () =>
      h('div', {}, [
        '任务提交成功，点击',
        h(
          'a',
          {
            class: 'a-text',
            onClick: () => {
              let routeUrl = router.resolve({
                name: 'task-manage',
                query: {
                  type: type || 'download',
                },
              });
              window.open(routeUrl.href, '_blank');
              // router.push({
              //   name: 'task-manage',
              //   target: '_blank',
              //   query: {
              //     type: type || 'download',
              //   },
              // });
            },
          },
          '任务中心'
        ),
        '立即查看',
      ]),
    // position: 'bottomRight',
    closable: true,
    duration: 5000,
  });
};

export const requiredRule = { required: true, message: '请填写' };
export const requiredUploadRule = { required: true, message: '请上传' };
export const requiredUploadRuleArr = {
  required: true,
  message: '请上传',
  type: 'array',
};
export const requiredRuleArr = {
  required: true,
  message: '请选择',
  type: 'array',
};
export const requiredRuleArrIpt = {
  required: true,
  message: '请填写',
  type: 'array',
};

let previewImgFun: any = null;
export function setPreviewImgFun(fun: any) {
  previewImgFun = fun;
}
// 弃用
export function previewImg(list: string[], index = 0) {}

// 复制文本
export async function copy(value: any) {
  if (!value) {
    return null;
  }
  value = String(value);
  if (value.indexOf('//') === 0) {
    value = value.replace('//', '');
  }

  const copyAction = useClipboard();
  try {
    await copyAction.copy(value);
    Message.success('复制成功');
    // 成功要做的事
    console.log('复制成功!');
  } catch (e) {
    console.log(e);
  }
}

export const tooltipItemsHtmlString = (
  items: ToolTipFormatterParams[],
  isRate = false
) => {
  return items
    .map(
      (el) => `<div class="content-panel">
        <p>
          <span style="background-color: ${
            el.color
          }" class="tooltip-item-icon"></span>
          <span>${el.seriesName}</span>
        </p>
        <span class="tooltip-value">
        ${el.value}${isRate || el.seriesName?.includes('率') ? '%' : ''}
        </span>
      </div>`
    )
    .join('');
};

// 根据文件后缀判断是图片还是视频
export function isImage(url: string) {
  if (!url) {
    return false;
  }
  const fileExtension = url.substring(url.lastIndexOf('.') + 1);
  return (
    fileExtension === 'jpg' ||
    fileExtension === 'jpeg' ||
    fileExtension === 'png' ||
    fileExtension === 'webp' ||
    fileExtension === 'gif'
  );
}

export function isVideo(url: string) {
  if (!url) {
    return false;
  }
  const fileExtension = url.substring(url.lastIndexOf('.') + 1);
  return (
    fileExtension === 'mp4' ||
    fileExtension === 'avi' ||
    fileExtension === 'mov' ||
    fileExtension === 'wmv' ||
    fileExtension === 'flv' ||
    fileExtension === 'mpeg'
  );
}

export function getFileType(url: string) {
  if (!url) {
    return false;
  }
  if (isImage(url)) {
    return 'image';
  }
  if (isVideo(url)) {
    return 'video';
  }
  return 'unknown';
}

// 允许使用混合模式的平台
export const allowMixPlatform = ['Instagram'];

// 和当前天做比较，返回是否是今天，是今天返回true，否则返回false 用dayjs
export function isToday(date: string) {
  const today = dayjs();
  const targetDate = dayjs(date);
  return today.isSame(targetDate, 'day');
}

// 获取平台图标 - 使用真实的平台图标文件
export const getPlatformIcon = (platform: string) => {
  const iconMap: any = {
    小红书: '/icons/platform/小红书.png',
    抖音: '/icons/platform/抖音.png',
    微博: '/icons/platform/微博.png',
    B站: '/icons/platform/B站.png',
    快手: '/icons/platform/快手.png',
    视频号: '/icons/platform/视频号.png',
    Instagram: '/icons/platform/Instagram.png',
  };
  if (platform === 'xhs') {
    return iconMap['小红书'];
  }
  return iconMap[platform] || '/icons/platform/default.svg';
};

/**
 * 计算字符串的实际字符长度
 * @param str 要计算的字符串
 * @param includeSpaces 是否包含空格字符，默认为 true
 * @returns 字符长度（可能为小数）
 */
export function getCharacterLength(str: string, includeSpaces = true): number {
  if (!str) return 0;

  let length = 0;

  for (let i = 0; i < str.length; i += 1) {
    const char = str[i];
    const charCode = char.charCodeAt(0);

    // 空格字符处理
    if (char === ' ') {
      if (includeSpaces) {
        length += 0.5; // 空格按英文字符计算
      }
    } else if (
      (charCode >= 0x4e00 && charCode <= 0x9fff) || // 基本汉字
      (charCode >= 0x3000 && charCode <= 0x303f) || // 中文标点符号
      (charCode >= 0xff00 && charCode <= 0xffef) || // 全角字符
      (charCode >= 0x2e80 && charCode <= 0x2eff) || // CJK部首补充
      (charCode >= 0x2f00 && charCode <= 0x2fdf) || // 康熙部首
      (charCode >= 0x31c0 && charCode <= 0x31ef) || // CJK笔画
      (charCode >= 0x3400 && charCode <= 0x4dbf) || // CJK扩展A
      (charCode >= 0x20000 && charCode <= 0x2a6df) || // CJK扩展B
      (charCode >= 0x2a700 && charCode <= 0x2b73f) || // CJK扩展C
      (charCode >= 0x2b740 && charCode <= 0x2b81f) || // CJK扩展D
      (charCode >= 0x2b820 && charCode <= 0x2ceaf) // CJK扩展E
    ) {
      length += 1; // 中文字符计为 1 个字符长度
    } else {
      length += 0.5; // 英文字母、数字、英文标点符号计为 0.5 个字符长度
    }
  }

  // 向下取整
  // let result = Math.floor(length);
  // 舍去小数点
  length = Math.floor(length);
  return length;
}

// 字符串截取方法 适配getCharacterLength
export function sliceString(str: string, maxLength: number) {
  if (!str) return '';

  let length = 0;
  let result = '';

  for (let i = 0; i < str.length; i += 1) {
    const char = str[i];
    const charCode = char.charCodeAt(0);

    let charLength = 0;

    // 空格字符处理
    if (char === ' ') {
      charLength = 0.5; // 空格按英文字符计算
    } else if (
      (charCode >= 0x4e00 && charCode <= 0x9fff) || // 基本汉字
      (charCode >= 0x3000 && charCode <= 0x303f) || // 中文标点符号
      (charCode >= 0xff00 && charCode <= 0xffef) || // 全角字符
      (charCode >= 0x2e80 && charCode <= 0x2eff) || // CJK部首补充
      (charCode >= 0x2f00 && charCode <= 0x2fdf) || // 康熙部首
      (charCode >= 0x31c0 && charCode <= 0x31ef) || // CJK笔画
      (charCode >= 0x3400 && charCode <= 0x4dbf) || // CJK扩展A
      (charCode >= 0x20000 && charCode <= 0x2a6df) || // CJK扩展B
      (charCode >= 0x2a700 && charCode <= 0x2b73f) || // CJK扩展C
      (charCode >= 0x2b740 && charCode <= 0x2b81f) || // CJK扩展D
      (charCode >= 0x2b820 && charCode <= 0x2ceaf) // CJK扩展E
    ) {
      charLength = 1; // 中文字符计为 1 个字符长度
    } else {
      charLength = 0.5; // 英文字母、数字、英文标点符号计为 0.5 个字符长度
    }

    // 检查是否超出最大长度
    if (Math.floor(length + charLength) > maxLength) {
      break;
    }

    length += charLength;
    result += char;
  }

  return result;
}

/**
 * 获取字符串去除空格后的字符长度（用于兼容现有代码）
 * @param str 要计算的字符串
 * @returns 去除空格后的字符长度
 */
export function getCharacterLengthWithoutSpaces(str: string): number {
  return getCharacterLength(str, false);
}

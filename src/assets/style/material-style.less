.img-item {
  border: 1px solid transparent;
  position: relative;
  background: var(--color-bg-1);
  width: 280px;
  .img-item-cell{
    width: 135px;
    position: relative;
    &:last-child{
      margin-left: 10px;
    }
  }
  img,.swiper {
    width: 100%;
    height: 240px;
    object-fit: contain;
    background: #000;
    display: block;
  }
  :deep(.swiper .arco-carousel-arrow){
    &>div{
      background-color: rgba(var(--gray-5), 0.9);
    }

  }
  .img-check {
    padding: 10px 5px;
    display: block;
  }
  .preview-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 24px;
    z-index: 2;
    background: rgba(0, 0, 0, 0.3);
    padding: 5px;
    border-radius: 50%;
    opacity: 0.8;
    cursor: pointer;
  }
  &:hover {
    .preview-icon {
      opacity: 1;
    }
  }
  &:hover {
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
  }
}

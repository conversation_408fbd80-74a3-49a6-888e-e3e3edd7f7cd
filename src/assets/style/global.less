* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  //background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

body::before {
  width: 100%;
  height: 100vh;
  // background: url('@/assets/images/bg.image') top repeat; //工作台的背景图
  background: url('@/assets/images/layout-bg.png'); //一站式的背景图
  background-size: 100% 100%;
  opacity: 0.8;
  bottom: 0;
  content: '';
  display: block;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: -2;
}

a {
  color: inherit;
  text-decoration: none;
}

.initA {
  text-decoration: none;
  color: inherit;
}

body[arco-theme='dark']::before {
  background: var(--color-bg-1);
  opacity: 1;
}

.card-b {
  border: 1px solid var(--color-neutral-3);
}

// 滚动条设置
/*定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}

/*定义滚动条轨道
 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 9px;
}

/*定义滑块
 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 9px;
  background-color: var(--color-fill-4);
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;

  .content-panel {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }

  .tooltip-title {
    margin: 0 0 10px 0;
  }

  p {
    margin: 0;
  }

  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-value {
    padding-left: 20px;
  }

  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.general-card {
  border-radius: 4px;
  border: none;

  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }

  & > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgb(var(--blue-6));

    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

.content-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

@list: 0, 5, 10, 20;
each(@list, {
  .mr-@{value} {
    margin-right: @value * 1px !important;
  }
  .mt-@{value} {
    margin-top: @value * 1px !important;
  }
  .mb-@{value} {
    margin-bottom: @value * 1px !important;
  }
  .ml-@{value} {
    margin-left: @value * 1px !important;
  }
  .m-@{value} {
    margin: @value * 1px;
  }
  .pt-@{value} {
    padding-top: @value * 1px !important;
  }
  .pb-@{value} {
    padding-bottom: @value * 1px !important;
  }
  .pr-@{value} {
    padding-right: @value * 1px !important;
  }
  .pl-@{value} {
    padding-left: @value * 1px !important;
  }
  .p-@{value} {
    padding: @value * 1px;
  }
});

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.df {
  display: flex !important;
}

.fw-wrap {
  flex-wrap: wrap;
}

.jc-sa {
  display: flex;
  justify-content: space-around;
}

.jc-sb {
  display: flex;
  justify-content: space-between;
}

.jc-cen {
  display: flex;
  justify-content: center;
}

.ai-cen {
  display: flex;
  align-items: center;
}

.ai-end {
  display: flex;
  align-items: flex-end;
}

.ai-st {
  display: flex;
  align-items: flex-start;
}

.fd-cl {
  flex-direction: column;
}

.fd-r {
  flex-direction: row !important;
}

@list1: 50, 100, 150, 200, 300, 400;
each(@list1, {
  .w-@{value} {
    width: @value * 1px !important;
  }
}) .w100p {
  width: 100%;
}

.arco-input-group {
  background-color: var(--color-fill-2);

  .arco-checkbox {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.table-card {
  flex: 1;
  margin-top: 10px;

  .arco-card-body {
    height: 100%;
  }

  .table-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    //padding: 5px 5px;
    &.check-header {
      padding: 5px 10px;
      background: rgb(var(--primary-1));
      color: rgb(var(--color-neutral-6));
      border-radius: 5px;

      .close-btn {
        cursor: pointer;
        font-size: 16px;
        transition: all 0.2s linear;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

.arco-input-wrapper {
  padding-left: 0;

  .arco-input {
    padding-left: 12px;
  }
}

//全局导航
.arco-trigger-menu .arco-trigger-menu-has-icon .arco-trigger-menu-icon {
  display: none !important;
}

.right-control-box {
  flex: 1;
  width: 950px;
}

.arco-divider-horizontal {
  border-bottom: 1px solid var(--color-neutral-2);
  margin: 10px 0;
}

.video-view-box {
  width: 100px;
  height: 60px;
  position: relative;
  line-height: 0;
  cursor: pointer;
  overflow: hidden;
  background: #000;
  border-radius: 4px;

  .video-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 5px;
  }

  .cover-ele {
    position: relative;
    width: 100%;
    height: 100%;
    // background: #211d2f;
    border-radius: 5px;
    object-fit: contain;
    z-index: 2;
    // &.min {
    //   width: 80px;
    //   height: 80px;
    // }
    img {
      width: 100% !important;
      height: 100% !important;
    }

    .arco-image-error-icon {
      position: relative;
      height: 40px;
      width: 100%;
      margin-top: 15px;
      z-index: 2;
    }
  }

  .cover-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    object-fit: cover;
    z-index: 1;
    background: #211d2f;
    filter: blur(7px);
    transform: scale(1.5);
    opacity: 0.7;
  }

  .video-ele {
    width: 100%;
    height: 100%;
    background: #000;
    object-fit: contain;
  }

  .video-view-group {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0);
    cursor: pointer;
    z-index: 2;

    .video-view-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 24px;
      opacity: 1;
      z-index: 2;
      background: rgba(0, 0, 0, 0.3);
      padding: 5px;
      border-radius: 50%;
    }

    &:hover {
      .video-view-btn {
        opacity: 0.8;
      }
    }
  }

  .video-img-btn {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 2;
  }
}

.primary_text {
  color: rgb(var(--primary-6));
}

.error_text {
  color: rgb(var(--danger-6));
}

.success_text {
  color: rgb(var(--success-6));
}

.warning_text {
  color: rgb(var(--warning-6));
}

.text_underline {
  text-decoration: underline;
}

.cur-por {
  cursor: pointer;
}

.a-text {
  color: rgb(var(--primary-6)) !important;
  cursor: pointer;
}

// 菜单图标间距调整
.arco-menu-icon {
  margin-right: 10px !important;
}

//pop的箭头边框重置
.arco-menu-pop-trigger.arco-trigger-position-bl .arco-trigger-arrow {
  border: 0;
}

.arco-layout-sider-light {
  background: var(--color-bg-2) !important;
}

img:not([src]):not([srcset]) {
  display: none; /* 隐藏没有src属性的图片 */
}

// 修复arco的样式问题
.arco-btn-size-mini {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1 !important;
}

// 隐藏图片加载失败的图标
img {
  text-indent: -10000px;
}

//控制内容区域最小高度
.view-box {
  .arco-card-body > .df {
    min-height: calc(100vh - 161px);
  }
}

// 弹窗
.arco-modal-simple {
  .arco-modal-body {
    padding: 0;
    text-align: center;
  }
  .arco-modal-footer {
    margin-top: 20px !important;
  }
}

.arco-tabs-content {
  padding-top: 0 !important;
}

.arco-tree-node-switcher {
  margin-right: 5px !important;
}

.arco-tree-node-custom-icon {
  margin-right: 5px !important;
}

// .image-actions {
//   position: absolute;
//   left: 0;
//   bottom: 0;
//   width: 100%;
//   padding: 4px 8px;
//   background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
//   display: flex;
//   gap: 8px;
//   opacity: 1;
//   transform: translateY(100%);
//   transition: all 0.3s ease;
//   z-index: 2;

//   .arco-btn {
//     flex: 1;
//     height: 20px;
//     font-size: 10px;
//     padding: 2px 8px;
//   }
// }

.arco-badge-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: auto !important;
  height: 12px !important;
  min-width: 12px !important;
  font-size: 8px !important;
  line-height: 1 !important;
  padding: 0 3px !important;
}

.arco-tree-size-small {
  max-height: calc(100vh - 180px);
  overflow: hidden;
  overflow-y: auto;
}

.divider-line {
  border-color: var(--color-border-2) !important;
}

//  账号信息popover面板
.popover-stats {
  display: flex;
  gap: 16px;
  margin-top: 10px;

  .stat-item {
    display: flex;
    align-items: flex-start;
    padding: 6px 10px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &:hover {
      background: linear-gradient(
        135deg,
        var(--color-fill-1),
        var(--color-fill-2)
      );
      transform: translateY(-2px);
    }

    .stat-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: var(--color-bg-1);
      margin-right: 12px;
      flex-shrink: 0;

      .arco-icon {
        font-size: 16px;
        color: var(--color-primary-6);
      }

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        position: relative;

        &.online {
          background: var(--color-success-6);
          animation: pulse-dot 2s infinite;
        }

        &.offline {
          background: var(--color-text-4);
        }

        &.busy {
          background: var(--color-warning-6);
          animation: blink-dot 1.5s infinite;
        }
      }
    }

    .stat-content {
      flex: 1;
      min-width: 0;

      .stat-label {
        display: block;
        font-size: 12px;
        color: var(--color-text-3);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-value-container {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-value {
          font-size: 14px;
          font-weight: 700;
          line-height: 1;
          color: var(--color-text-1);

          &.has-unread {
            color: var(--color-primary-6);
          }

          &.status-text {
            font-size: 12px;
            font-weight: 600;

            &.online {
              color: var(--color-success-6);
            }

            &.offline {
              color: var(--color-text-4);
            }

            &.busy {
              color: var(--color-warning-6);
            }
          }
        }

        .status-time {
          font-size: 10px;
          color: var(--color-text-4);
          font-weight: 400;
        }
      }
    }

    // 未读消息项特殊样式
    &.unread-messages {
      .stat-icon {
        background: linear-gradient(
          135deg,
          rgba(var(--primary-6), 0.1),
          rgba(var(--primary-6), 0.05)
        );
      }
    }

    // 在线状态项特殊样式
    &.online-status {
      .stat-icon {
        background: linear-gradient(
          135deg,
          rgba(var(--success-6), 0.1),
          rgba(var(--success-6), 0.05)
        );
      }
    }
  }
}

// 设置面板样式
.settings-panel {
  width: 140px;

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--color-border-2);

    &:last-child {
      border-bottom: none;
    }

    .setting-label {
      font-size: 13px;
      color: var(--color-text-1);
    }
  }
}

.arco-comment:not(:first-of-type),
.arco-comment-inner-comment {
  margin-top: 10px !important;
}

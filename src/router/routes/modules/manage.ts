import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/manage',
  name: 'manage',
  redirect: '/manage/user-manage',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '管理',
    roles: [1, 15],
    requiresAuth: true,
    order: 120,
    icon: 'icon-settings',
  },
  children: [
    {
      path: 'user-manage',
      name: 'user-manage',
      component: () => import('@/views/manage/user-manage/user-manage.vue'),
      meta: {
        locale: '用户管理',
        // icon: 'icon-user',
        roles: [1],
        requiresAuth: true,
        hideInMenu: false,
      },
    },
    // 部门管理
    {
      path: 'department-manage',
      name: 'department-manage',
      component: () =>
        import('@/views/manage/department-manage/department-manage.vue'),
      meta: {
        locale: '部门管理',
        roles: [1],
        requiresAuth: true,
        hideInMenu: false,
      },
    },
    {
      path: 'product-manage',
      name: 'product-manage',
      component: () =>
        import('@/views/manage/product-manage/product-manage.vue'),
      meta: {
        locale: '产品管理',
        // icon: 'icon-archive',
        roles: [1, 15],
        requiresAuth: true,
      },
    },
    {
      path: 'customer-service',
      name: 'customer-service',
      component: () =>
        import('@/views/manage/customer-service/customer-service.vue'),
      meta: {
        locale: '电销管理',
        // icon: 'icon-customer-service',
        roles: [1, 15],
        requiresAuth: true,
      },
    },
    {
      path: 'customer-service-log',
      name: 'customer-service-log',
      component: () =>
        import('@/views/manage/customer-service/customer-service-log.vue'),
      meta: {
        locale: '电销日志',
        // icon: 'icon-customer-service',
        roles: [1, 15],
        requiresAuth: true,
        hideInMenu: true,
        activeMenu: 'customer-service',
      },
    },
    // 消息管理
    {
      path: 'system-message',
      name: 'system-message',
      component: () => import('@/views/manage/system-message/table-manage.vue'),
      meta: {
        locale: '消息管理',
        // icon: 'icon-message',
        roles: [1, 15],
        requiresAuth: true,
      },
    },
    {
      path: 'download-center',
      name: 'download-center',
      component: () => import('@/views/tools/download-center/table-manage.vue'),
      meta: {
        locale: '下载中心',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-list',
      },
    },
    {
      path: 'system-setting',
      name: 'system-setting',
      component: () =>
        import('@/views/manage/system-setting/system-setting.vue'),
      meta: {
        locale: '移动端设置',
        // icon: 'icon-settings',
        roles: [1, 15],
        requiresAuth: true,
      },
    },
    {
      path: 'service-setting',
      name: 'service-setting',
      component: () =>
        import('@/views/manage/service-setting/service-setting.vue'),
      meta: {
        locale: '系统对接',
        roles: [1, 15],
        requiresAuth: true,
      },
    },
  ],
};
export default RouterConfig;

import { DEFAULT_LAYOUT } from '@/router/routes/base';
import EmptyLayout from '@/layout/empty-layout.vue';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/account',
  name: 'account',
  redirect: '/account/devices',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '账号自热',
    roles: [1, 15],
    requiresAuth: true,
    order: 80,
    icon: 'icon-mobile',
  },
  children: [
    {
      path: 'devices',
      name: 'devices',
      component: () => import('@/views/account/devices/devices.vue'),
      meta: {
        locale: '账号管理',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-mobile',
      },
    },

    // 笔记管理
    {
      path: 'note-manage',
      name: 'note-manage',
      component: () => import('@/views/account/note-manage/index.vue'),
      meta: {
        locale: '笔记管理',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-mobile',
      },
    },
    // 评论管理
    {
      path: 'comment-manage',
      name: 'comment-manage',
      component: () => import('@/views/account/comment-manage/index.vue'),
      meta: {
        locale: '评论管理',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-mobile',
      },
    },
    {
      path: 'content-publish',
      name: 'content-publish',
      component: () =>
        import('@/views/account/content-publish/content-publish.vue'),
      meta: {
        locale: '批量发布',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-send',
      },
    },
    {
      path: 'mix-content',
      name: 'mix-content',
      component: () =>
        import('@/views/account/content-manage/mix-content/mix-content.vue'),
      meta: {
        locale: '内容库',
        roles: [1, 15],
        requiresAuth: true,
      },
    },
    // {
    //   path: 'content-manage',
    //   name: 'content-manage',
    //   component: EmptyLayout,
    //   redirect: '/account/content-manage/video-content',
    //   meta: {
    //     locale: '内容库',
    //     roles: [1, 15],
    //     requiresAuth: true,
    //     // icon: 'icon-layers',
    //   },
    //   children: [
    //     {
    //       path: 'mix-content',
    //       name: 'mix-content',
    //       component: () =>
    //         import(
    //           '@/views/account/content-manage/mix-content/mix-content.vue'
    //         ),
    //       meta: {
    //         locale: '通用',
    //         roles: ['*'],
    //         requiresAuth: true,
    //       },
    //     },
    //     {
    //       path: 'video-content',
    //       name: 'video-content',
    //       component: () =>
    //         import(
    //           '@/views/account/content-manage/video-content/video-content.vue'
    //         ),
    //       meta: {
    //         locale: '视频',
    //         roles: ['*'],
    //         requiresAuth: true,
    //       },
    //     },
    //     {
    //       path: 'image-content',
    //       name: 'image-content',
    //       component: () =>
    //         import(
    //           '@/views/account/content-manage/image-content/image-content.vue'
    //         ),
    //       meta: {
    //         locale: '图文',
    //         roles: ['*'],
    //         requiresAuth: true,
    //       },
    //     },
    //   ],
    // },
    {
      path: 'publish-task',
      name: 'publish-task',
      component: () => import('@/views/account/publish-task/index.vue'),
      meta: {
        locale: '任务列表',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-list',
      },
    },
    {
      path: 'material-manage',
      name: 'material-manage',
      component: EmptyLayout,
      redirect: '/account/material-manage/image-manage',
      meta: {
        locale: '素材库',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-storage',
      },
      children: [
        {
          path: 'video-manage',
          name: 'video-manage',
          component: () =>
            import(
              '@/views/account/material-manage/video-manage/video-manage.vue'
            ),
          meta: {
            locale: '视频素材',
            roles: ['*'],
            requiresAuth: true,
          },
        },
        {
          path: 'image-manage',
          name: 'image-manage',
          component: () =>
            import(
              '@/views/account/material-manage/image-manage/image-manage.vue'
            ),
          meta: {
            locale: '图片素材',
            roles: ['*'],
            requiresAuth: true,
          },
        },
        {
          path: 'thumb-manage',
          name: 'thumb-manage',
          component: () =>
            import(
              '@/views/account/material-manage/thumb-manage/thumb-manage.vue'
            ),
          meta: {
            locale: '封面素材',
            roles: ['*'],
            requiresAuth: true,
          },
        },
      ],
    },
  ],
};
export default RouterConfig;

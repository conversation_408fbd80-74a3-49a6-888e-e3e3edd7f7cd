import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/ai',
  name: 'ai',
  component: DEFAULT_LAYOUT,
  // redirect: '/ai/aixhs',
  meta: {
    locale: 'AI智能体',
    roles: [1, 15],
    requiresAuth: true,
    order: 90,
    // iconFont: 'icon-AItubiao-2',
    icon: 'icon-robot-add',
    showAll: true,
  },
  children: [
    // {
    //   path: 'xhs',
    //   name: 'xhs',
    //   meta: {
    //     locale: '小红书创作',
    //     suffix: '(测试)',
    //     roles: [1, 15],
    //     requiresAuth: true,
    //     link: 'https://windmill.pw2.p.i00.pw:51821/apps/get/f/xhs/auto_travel_guide_v1_remote',
    //   },
    // },
    {
      path: 'content-collect',
      name: 'content-collect',
      meta: {
        locale: '内容采集',
        roles: [1, 15],
        requiresAuth: true,
      },
      component: () => import('@/views/tools/content-collect/table-manage.vue'),
    },
  ],
};
export default RouterConfig;

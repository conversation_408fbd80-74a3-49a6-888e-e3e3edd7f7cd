import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/report',
  name: 'report',
  redirect: '/report/overview',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '数据看板',
    roles: [1, 15],
    requiresAuth: true,
    order: 10,
    icon: 'icon-dashboard',
  },
  children: [
    {
      path: 'overview',
      name: 'overview',
      component: () => import('@/views/report/overview/overview.vue'),
      meta: {
        locale: '数据看板',
        icon: 'icon-dashboard',
        roles: [1, 15],
        requiresAuth: true,
        activeMenu: 'report',
      },
    },
  ],
};
export default RouterConfig;

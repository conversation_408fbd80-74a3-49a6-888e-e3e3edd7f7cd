import type { Router, LocationQueryRaw } from 'vue-router';

import { useUserStore } from '@/store';
import { isLogin } from '@/utils/auth';
import useUser from '@/hooks/user';

export default function setupUserLoginInfoGuard(router: Router) {
  router.beforeEach(async (to, from) => {
    if (['login'].includes(to.name as string)) {
      return true;
    }
    if (isLogin()) {
      return true;
    }
    return {
      name: 'login',
      query: {
        redirect: to.name,
        ...to.query,
      } as LocationQueryRaw,
    };
  });
}

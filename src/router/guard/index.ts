import type { Router } from 'vue-router';
import { setRouteEmitter } from '@/utils/route-listener';
import checkVersion from '@/hooks/check-version';
import NProgress from 'nprogress';
import request from '@/api/request';
import setupUserLoginInfoGuard from './userLoginInfo';
import setupPermissionGuard from './permission';

function setupPageGuard(router: Router) {
  router.beforeEach(async (to, from) => {
    NProgress.start();
    // emit route change
    setRouteEmitter(to);
  });
  router.beforeResolve(async (to) => {
    try {
      checkVersion.checkVersion(false).then((res) => {
        if (res) {
          window.location.reload();
        }
      });
    } catch (e) {
      console.log('检查更新', e);
    }

    /// / 访问上报
    // let data: any = {};
    // if (!['/login', '/redirect'].includes(to.path)) {
    //  try {
    //    data.menu_one_mark =
    //      to.matched[0]?.meta?.locale || to.matched[0]?.name || '';
    //    data.menu_two_mark = to.meta.locale || to.name;
    //    request('/api/operation/report', data);
    //  } catch (e) {
    //    console.log('上报菜单', e);
    //  }
    // }
  });
  router.afterEach(async (to: any) => {
    NProgress.done();
  });
}

function uploadPath(router: Router) {
  router.afterEach(async (to: any) => {
    try {
      window.parent?.postMessage(
        JSON.stringify({
          type: 'routerChange',
          data: { path: window.location.hash },
        }),
        '*'
      );
    } catch (e) {
      console.log(e);
    }
  });
}

export default function createRouteGuard(router: Router) {
  setupPageGuard(router);
  uploadPath(router);
  setupUserLoginInfoGuard(router);
  setupPermissionGuard(router);
}

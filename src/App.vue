<template>
  <a-config-provider :locale="locale">
    <router-view />
    <global-setting />
    <a-image-preview-group
      v-model:visible="imgPreview"
      v-model:current="imgPreviewIndex"
      :src-list="previewImgs"
    />
  </a-config-provider>
</template>

<script lang="ts" setup>
  import { computed, onBeforeUnmount, ref, watch } from 'vue';
  import enUS from '@arco-design/web-vue/es/locale/lang/en-us';
  import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
  import GlobalSetting from '@/components/global-setting/index.vue';
  import useLocale from '@/hooks/locale';
  import { getToken, getUser, isLogin } from '@/utils/auth';
  import { useDataCacheStore, useUserStore } from '@/store';
  import { useRoute, useRouter } from 'vue-router';
  import useUser from '@/hooks/user';
  import { DEFAULT_ROUTE_NAME } from '@/router/constants';
  import { VITE_TOKEN_KEY } from '@/utils/env';
  import { setPreviewImgFun } from '@/utils/util';

  const imgPreview = ref(false);
  const imgPreviewIndex = ref(0);
  const previewImgs = ref([]);
  setPreviewImgFun((list: any, index = 0) => {
    previewImgs.value = list;
    imgPreviewIndex.value = index;
    imgPreview.value = true;
  });
  const dataCache = useDataCacheStore();
  dataCache.getNoRead();
  let timer = setInterval(() => {
    dataCache.getNoRead();
  }, 1000 * 5);
  onBeforeUnmount(() => {
    clearInterval(timer);
  });

  const userStore = useUserStore();
  userStore.getUserInfo();
  const router = useRouter();
  // 监听多tab登录时，无论用户相同与否，之前的tab都要重新登录
  window.addEventListener('storage', (e) => {
    if (e.key === VITE_TOKEN_KEY) {
      setTimeout(() => {
        if (isLogin()) {
          userStore.setStateInfo(getUser());
          if (router.currentRoute.value.name === 'login') {
            router.push({ name: DEFAULT_ROUTE_NAME });
          }
        } else {
          useUser().logout();
        }
      }, 1000);
    }
  });

  const route = useRoute();
  const setHtmlTitle = () => {
    if (!route || !route.path) {
      return;
    }
    try {
      let key = route?.meta?.locale || '';
      document.title = `线索系统 | ${key}`;
    } catch (e) {
      console.log(e);
    }
  };
  watch(route, (newVal, oldVal) => {
    setHtmlTitle();
  });

  const { currentLocale } = useLocale();
  const locale = computed(() => {
    switch (currentLocale.value) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
        return enUS;
      default:
        return enUS;
    }
  });
</script>

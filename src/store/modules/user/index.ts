import { defineStore } from 'pinia';
import {
  setToken,
  clearToken,
  setUser,
  clearUser,
  getUser,
  getToken,
} from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import request from '@/api/request';
import { intersection, isArray, isString, toNumber } from 'lodash';
import { UserState } from './types';
import useAppStore from '../app';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    id: undefined,
    create_user_id: undefined,
    company_id: undefined,
    user_name: undefined,
    email: undefined,
    account_name: undefined,
    phone: undefined,
    qy_wchat_id: undefined,
    feishu_user_id: undefined,
    feishu_open_id: undefined,
    role_id: undefined,
    // roles: undefined,
    roles: undefined,
    department_id: undefined,
    state: undefined,
    group_id: undefined,
    is_assistant: undefined,
    add_time: undefined,
    update_time: undefined,
    entry_time: undefined,
    leave_time: undefined,
    avatar: undefined,
    assistant_user_id: undefined,
    supplier_type: undefined,
    is_from_saas: undefined,
    host: undefined,
    name: undefined,
    service_status: undefined,
    ...getUser(),
  }),

  getters: {
    userInfo(store: any): UserState {
      return store.$state;
    },
  },

  actions: {
    hasPermission(role: number | string | number[]): boolean {
      if (this.role_id === 1) return true;
      if (isArray(role)) {
        return intersection(role, this.roles || []).length > 0;
      }
      if (isString(role)) {
        return this.roles?.includes(toNumber(role));
      }
      return this.roles?.includes(role);
    },
    switchRoles(role: number) {
      return new Promise((resolve) => {
        this.role_id = role;
        resolve(this.role_id);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
      setUser(partial);
    },
    // Set user's information
    setStateInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    async getUserInfo() {
      if (getToken()) {
        request('/api/user/getSelfInfo').then((res: any) => {
          this.setInfo({ ...res.data });
        });
      }
    },

    async login(loginForm: any) {
      try {
        const res: any = await request('/api/userLogin', loginForm);
        setToken(res.data.token);
        this.setInfo({ ...res.data });
        this.getUserInfo();
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    async loginFs(loginForm: any) {
      try {
        const res: any = await request('/api/fsLogin', loginForm);
        setToken(res.data.token);
        this.setInfo({ ...res.data });
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      clearUser();
      removeRouteListener();
      appStore.clearServerMenu();
    },
    // Logout
    async logout() {
      this.logoutCallBack();
    },
  },
});

export default useUserStore;

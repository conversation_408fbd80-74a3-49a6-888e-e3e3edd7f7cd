import { defineStore } from 'pinia';
import request from '@/api/request';
import { isLogin } from '@/utils/auth';

const useDataCacheStore = defineStore('data-cache', {
  state: () => ({
    travelLines: [] as any[], // 产品
    travelProducts: [] as any[], // 旅游产品
    travelAgencys: [] as any[], // 旅游地接
    travelCars: [] as any[], // 车型
    travelTypes: [] as any[], // 出游类型
    noReadIds: [] as number[], // 未读消息
    is_exists_new_collect_content: false, // 是否存在新的采集内容
    new_chat_message_num: 0, // 新聊天消息数量
    new_system_msg_num: 0, // 新系统消息数量
  }),
  actions: {
    getTravelSetting() {
      return request('/api/travel/getSetting', {}).then((res) => {
        this.travelCars = res.data.car_type;
        this.travelTypes = res.data.travel_type.map((item: any) => ({
          label: item,
          value: item,
        }));
        let travelLines: any[] = res.data.area;
        let travelAgencys: string[] = [];
        this.travelProducts = res.data.travel_line.map((line: string) => {
          let itemData = res.data.jingdian_setting[line];
          travelAgencys.push(itemData['地接']);
          return {
            ...itemData,
            label: line,
            area: itemData['线路'],
            value: line,
            travel_date:
              res.data.travel_line_date.find((item: any) => item.line === line)
                ?.date || [],
          };
        });
        this.travelLines = Array.from(new Set(travelLines)).map((item) => ({
          label: item.area,
          value: item.area,
          id: item.id,
        }));
        this.travelAgencys = Array.from(new Set(travelAgencys)).map((item) => ({
          label: item,
          value: item,
        }));

        return res.data;
      });
    },
    // 获取意向线索未读消息
    getNoRead() {
      if (isLogin() && window.location.hostname !== 'localhost') {
        request('/api/thread/noReadThread', {}).then((res) => {
          this.noReadIds = res.data.thread_seas_ids || [];
          this.new_chat_message_num = res.data.new_chat_message_num || 0;
          this.is_exists_new_collect_content =
            res.data.is_exists_new_collect_content || false;
          this.new_system_msg_num = res.data.new_system_msg_num || 0;
        });
      }
    },
  },
});

export default useDataCacheStore;

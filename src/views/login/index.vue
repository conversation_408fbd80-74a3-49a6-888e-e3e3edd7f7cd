<template>
  <div class="container">
    <div class="logo">
      <img
        alt="logo"
        src="@/assets/images/logo-text.png"
        style="width: 100px"
      />
      <!--<div class="logo-text">GetClue</div> -->
    </div>
    <div class="content">
      <div class="content-inner">
        <login-form />
      </div>
    </div>
    <Footer />
  </div>
</template>

<script lang="ts" setup>
  import Footer from '@/components/footer/index.vue';
  import { onBeforeMount } from 'vue';
  import { useUserStore } from '@/store';
  import { useRoute } from 'vue-router';
  import LoginFormFs from '@/views/login/components/login-form-fs.vue';
  import LoginBanner from './components/banner.vue';
  import LoginForm from './components/login-form.vue';

  const route = useRoute();
  const userStore = useUserStore();
  onBeforeMount(() => {
    userStore.logout();
  });
</script>

<style lang="less" scoped>
  .container {
    display: flex;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.9);
    .banner {
      width: 550px;
      background: linear-gradient(163.85deg, #1d2129 0%, #00308f 100%);
    }

    .content {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding-bottom: 100px;
    }

    .footer {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background: #f2f2f7;
    }
  }

  .logo {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    display: inline-flex;
    align-items: center;
    width: 100%;
    padding: 10px;
    background-color: rgba(251, 251, 253, 0.6);
    border-block-end: 1px solid rgba(251, 251, 253, 0.5);
    border-bottom: 1px solid rgba(251, 251, 253, 0.5);

    &-text {
      margin-right: 4px;
      margin-left: 4px;
      color: #000;
      font-size: 22px;
      font-weight: 700;
    }
  }
</style>

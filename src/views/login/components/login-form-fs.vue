<template>
  <div class="login-form-wrapper">
    <div class="login-logo-box">
      <img alt="logo" src="@/assets/images/logo-txt.png" style="height: 30px" />
    </div>
    <div class="login-form-error-msg">{{ errorMessage }}</div>
    <div class="login-form">
      <a-button
        type="outline"
        html-type="submit"
        long
        :loading="loading"
        @click="toLogin"
      >
        <template #icon>
          <icon-lark-color />
        </template>
        飞书登录
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onBeforeMount } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import { DEFAULT_ROUTE_NAME } from '@/router/constants';

  const router = useRouter();
  const route = useRoute();
  const { t } = useI18n();
  const errorMessage = ref('');
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const toLogin = () => {
    window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${encodeURIComponent(
      window.location.href
    )}&app_id=cli_a61c9be6f9f2900e`;
  };

  const loginAction = async () => {
    try {
      loading.value = true;
      await userStore.loginFs({ code: route.query.code });
      const { redirect, ...othersQuery } = router.currentRoute.value.query;
      router.push({
        name: (redirect as string) || DEFAULT_ROUTE_NAME,
        query: {
          ...othersQuery,
        },
      });
    } catch (err) {
      errorMessage.value = (err as Error).message;
    } finally {
      setLoading(false);
    }
  };

  onBeforeMount(() => {
    if (route.query.code) {
      loginAction();
    }
  });
</script>

<style lang="less" scoped>
  .login-form {
    &-wrapper {
      background-color: #fff;
      padding: 50px;
      width: 420px;
      border-radius: 12px;
      .login-logo-box {
        text-align: center;
      }
    }

    &-title {
      color: var(--color-text-1);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
      text-align: center;
    }

    &-sub-title {
      color: var(--color-text-3);
      font-size: 16px;
      line-height: 24px;
    }

    &-error-msg {
      height: 32px;
      color: rgb(var(--red-6));
      line-height: 32px;
    }

    &-password-actions {
      display: flex;
      justify-content: space-between;
    }

    &-register-btn {
      color: var(--color-text-3) !important;
    }
  }
  .arco-input-wrapper {
    padding-left: 12px;
  }
</style>

<template>
  <div class="login-form-wrapper">
    <div class="login-logo-box">
      <img
        alt="logo"
        src="@/assets/images/favicon-logo.png"
        style="width: 120px"
      />
      <h1 class="login-form-title">通过GetClue账户登录</h1>
    </div>
    <a-form
      ref="loginForm"
      :model="userInfo"
      class="login-form"
      layout="vertical"
      size="large"
      @submit="handleSubmit"
    >
      <a-form-item
        field="account_name"
        :rules="[{ required: true, message: $t('login.form.userName.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input v-model="userInfo.account_name" placeholder="账户" allow-clear>
        </a-input>
      </a-form-item>
      <a-form-item
        field="password"
        :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input-password
          v-model="userInfo.password"
          placeholder="密码"
          allow-clear
        >
        </a-input-password>
      </a-form-item>
      <div class="submit-box">
        <a-button type="outline" shape="circle" html-type="submit" size="large">
          <icon-arrow-rise
            :stroke-width="5"
            :spin="loading"
            :rotate="90"
            :size="20"
          />
        </a-button>
      </div>
      <div class="login-form-error-msg">{{ errorMessage }}</div>
    </a-form>
    <div class="login-form-footer">
      <div class="keep-login-box">
        <a-checkbox
          checked="rememberPassword"
          :model-value="loginConfig.rememberPassword"
          @change="setRememberPassword"
        >
          保持我的登录状态
        </a-checkbox>
      </div>
      <div><a-link @click="forgetAction"> 忘记了密码？ </a-link></div>
      <div><a-link @click="forgetAction"> 创建GetClue账户 </a-link></div>
      <img
        src="@/assets/images/platform.png"
        alt=""
        style="margin-top: 20px; width: 200px"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onBeforeMount } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import { DEFAULT_ROUTE_NAME } from '@/router/constants';
  import request from '@/api/request';

  const { VITE_ACCOUNT_KEY } = import.meta.env;
  const router = useRouter();
  const { t } = useI18n();
  const errorMessage = ref('');
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginConfig = useStorage(VITE_ACCOUNT_KEY, {
    rememberPassword: true,
    account_name: '',
    password: '',
  });
  const userInfo = reactive({
    account_name: loginConfig.value.account_name,
    password: loginConfig.value.password,
  });

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        await userStore.login(values);
        const { redirect, ...othersQuery } = router.currentRoute.value.query;
        router.push({
          name: (redirect as string) || DEFAULT_ROUTE_NAME,
          query: {
            ...othersQuery,
            super: undefined,
          },
        });
        Message.success(t('login.form.login.success'));
        const { rememberPassword } = loginConfig.value;
        const { account_name, password } = values;
        // 实际生产环境需要进行加密存储。
        loginConfig.value.account_name = rememberPassword ? account_name : '';
        loginConfig.value.password = rememberPassword ? password : '';
      } catch (err) {
        errorMessage.value = (err as Error).message;
      } finally {
        setLoading(false);
      }
    }
  };

  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };

  const forgetAction = () => {
    Message.warning('功能暂未开放，请联系销售同学');
  };

  const fsLogin = () => {
    window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${encodeURIComponent(
      window.location.href
    )}&app_id=cli_a61c9be6f9f2900e`;
  };

  const route = useRoute();
  const loginAction = async () => {
    try {
      loading.value = true;
      await userStore.loginFs({ code: route.query.code });
      const { redirect, ...othersQuery } = router.currentRoute.value.query;
      delete othersQuery.code;
      delete othersQuery.state;
      router.push({
        name: (redirect as string) || DEFAULT_ROUTE_NAME,
        query: {
          ...othersQuery,
        },
      });
    } catch (err) {
      router.replace({
        name: 'login',
        query: {
          ...router.currentRoute.value.query,
          code: undefined,
          state: undefined,
        },
      });
      errorMessage.value = (err as Error).message;
    } finally {
      setLoading(false);
    }
  };

  onBeforeMount(() => {
    if (route.query.code) {
      loginAction();
    }
  });
</script>

<style lang="less" scoped>
  .arco-form-item {
    margin-bottom: 20px;
    &.arco-form-item-error {
      margin-bottom: 0;
    }
  }
  .login-form {
    &-wrapper {
      padding: 30px 100px 20px;
      width: 660px;
      .login-logo-box {
        text-align: center;
      }
      background-color: #fff;
      box-shadow: 0 11px 34px 0 rgba(120, 120, 128, 0.16);
      border-radius: 34px;
      backdrop-filter: blur(10px);
    }
    &-title {
      font-family: system-ui, Helvetica Neue, sans-serif;
      color: rgba(0, 0, 0, 0.88);
      font-weight: 500;
      font-size: 30px;
      line-height: 36px;
      text-align: center;
      margin: 0;
      padding: 10px 0 32px;
    }
    .submit-box {
      text-align: center;
    }
    .arco-input-wrapper {
      background-color: #fff;
      border: 1px solid #cccccc;
      border-radius: 17px;
      transition: all 0.5s;
      padding: 0 20px;
    }
    .arco-input-wrapper:focus-within,
    .arco-input-wrapper.arco-input-focus {
      border-color: rgb(var(--primary-6));
    }
    .arco-btn-size-large.arco-btn-shape-circle {
      width: 50px;
      height: 50px;
      transition: all 0.5s;
      &:hover {
        transform: scale(1.2);
      }
    }

    &-error-msg {
      margin-top: 20px;
      color: rgb(var(--red-6));
      line-height: 32px;
      text-align: center;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 170px;
    }
    &-footer {
      padding-top: 50px;
      text-align: center;
      .keep-login-box {
        padding-bottom: 20px;
        :deep(.arco-checkbox-label) {
          font-size: 16px;
          color: rgba(0, 0, 0, 0.76);
        }
      }
    }
  }
  input.arco-input:-internal-autofill-selected {
    background-color: #fff !important;
  }
</style>

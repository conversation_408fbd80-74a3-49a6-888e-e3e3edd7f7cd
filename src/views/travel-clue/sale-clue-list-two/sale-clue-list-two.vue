<template>
  <div>
    <a-card size="small" class="table-card">
      <search-form
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        placeholder="请输入线索ID"
        :search-rules="searchRules"
        :base-search-rules="baseSearchRules"
        @hand-submit="handleSubmit"
      ></search-form>
    </a-card>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <div> </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_sale_clue_two' }"
            :default-columns="columnsConfig.map((item:any) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <!--<a-button
            type="primary"
            @click="addRef?.show({ source: '客服新增' })"
          >
            新增二级线索
          </a-button>-->
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParams"
        :scroll-percent="scrollPercent"
        :data-handle="dataHandle"
        :auto-request="false"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space size="mini" direction="vertical">
            <!--<a-space>
              <a-link @click="addRef?.show(record)"> 编辑线索 </a-link>
              <a-link
                v-if="record.status_two !== '作废'"
                @click="cancelRef?.show(record)"
              >
                作废
              </a-link>
            </a-space>
            <a-popconfirm
              v-if="record.status_two === '已分配'"
              position="left"
              @ok="addWechat(record)"
            >
              <template #content>
                <div>
                  <div>确定已添加微信吗？</div>
                  <div class="ai-cen mt-5">
                    <span style="white-space: nowrap">微信号：</span>
                    <a-input v-model="record.wechat" size="mini" />
                  </div>
                </div>
              </template>
              <a-link> 添加微信 </a-link>
            </a-popconfirm>
            <a-popconfirm
              v-if="record.status_two === '已添加微信'"
              position="left"
              content="确定已介绍产品及公司吗？"
              @ok="hasCommunicate(record)"
            >
              <a-link> 介绍产品及公司 </a-link>
            </a-popconfirm>
            <a-link
              v-else-if="record.status_two === '已介绍产品及公司' && false"
              @click="
                priceRef?.show(
                  {
                    thread_id: record.id,
                    source: record.platform,
                    phone: record.phone,
                    service_user_fs_id: record.two_service_user_fs_id,
                    service_user: record.two_service_user,
                    media_account: record.account_id,
                    adult_num: record.adult_num,
                    children_num: record.children_num,
                    customer_name: record.account_name,
                    order_source: '内容私域',
                  },
                  {
                    needInfo: false,
                  },
                  true
                )
              "
            >
              生成询价单和订单
            </a-link>
            <a-link
              v-else-if="record.status_two === '已询价' && false"
              @click="
                priceRef?.show(
                  { inquiry_order_no: record.relation_inquiry_order },
                  {
                    needInfo: true,
                    showConfirm: false,
                  }
                )
              "
            >
              编辑询价单
            </a-link>-->
            <a-popconfirm
              v-if="record.status_two === '已分配'"
              position="left"
              content="确定已跟进吗？"
              @ok="editStatus(record, '已跟进')"
            >
              <a-link> 已跟进 </a-link>
            </a-popconfirm>
            <a-popconfirm
              v-if="record.status_two === '已跟进'"
              position="left"
              content="确定已成交吗？"
              @ok="editStatus(record, '已成交')"
            >
              <a-link> 已成交 </a-link>
            </a-popconfirm>
            <a-link
              v-if="record.thread_seas_id"
              @click="detailRef?.show(record)"
            >
              查看沟通记录
            </a-link>
          </a-space>
        </template>
        <template #account_id="{ record }: TableColumnSlot">
          <template v-if="record.media_sec_uid">
            <a-link
              v-if="record.platform === '小红书'"
              :href="`https://www.xiaohongshu.com/user/profile/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
            <a-link
              v-else
              :href="`https://www.douyin.com/user/${record.media_sec_uid}`"
              target="_blank"
            >
              {{ record.account_id }}
            </a-link>
          </template>
          <span v-else>{{ record.account_id }}</span>
        </template>
        <template #thread_content="{ record }: TableColumnSlot">
          <a-tooltip
            v-if="record.thread_content?.length > 50"
            :content="record.thread_content.slice(0, 400)"
          >
            <span>{{ record.thread_content.slice(0, 50) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.thread_content }}</span>
        </template>
        <template #remark="{ record }: TableColumnSlot">
          <a-popover v-model:popup-visible="record.showBtn" trigger="click">
            <template #content>
              <a-input-group>
                <a-textarea
                  v-model="record.remark_new"
                  style="width: 400px"
                  :auto-size="{ minRows: 2, maxRows: 6 }"
                  @focus="record.showBtn = true"
                  @blur="hideEditBtn(record)"
                />
                <a-button
                  v-if="record.showBtn"
                  type="primary"
                  @click="editRemark(record)"
                >
                  <icon-check />
                </a-button>
              </a-input-group>
            </template>
            <span style="white-space: pre-line">
              {{ record.remark || '-' }}
              <icon-edit class="a-text" />
            </span>
          </a-popover>
        </template>
        <template #status_two="{ record }: TableColumnSlot">
          <a-space direction="vertical" style="white-space: pre-line">
            <a-tag :color="getColor(clueStatusM, record.status_two)">
              {{ getText(clueStatusM, record.status_two) }}
            </a-tag>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <travel-price-edit ref="priceRef" @save="theTable?.fetchData()" />
    <sale-clue-two-add
      ref="addRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-two-add>
    <sale-clue-cancellation
      ref="cancelRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-cancellation>
    <sale-clue-detail ref="detailRef" no-chat></sale-clue-detail>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed, markRaw } from 'vue';
  import request from '@/api/request';
  import { TableColumnSlot } from '@/global';
  import { clueSuorceM, clueStatusM } from '@/components/dict-select/dict-clue';
  import { copy } from '@/utils/util';
  import { useRoute } from 'vue-router';
  import { useDataCacheStore } from '@/store';
  import requestSelect from '@/components/select/request-select.vue';
  import SaleClueTwoAdd from '@/views/travel-clue/sale-clue-list-two/sale-clue-two-add.vue';
  import SaleClueCancellation from '@/views/travel-clue/sale-clue-list/sale-clue-cancellation.vue';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import SaleClueDetail from '@/views/travel-clue/sale-clue-list/sale-clue-detail.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import TravelPriceEdit from './components/travel-price-edit.vue';

  const detailRef = ref();
  const generateFormModel = () => {
    return {
      id: null as string | null,
      thread_seas_id: null,
      phone_or_wechat: null,
      two_service_user_fs_id: null,
      status_two: null,
      status: '流转二级线索池',
      area: null,
      from_setting: null,
      source: null,
      time_distribute_two: null,
      service_user_fs_id: null,
      cancellation_time: null,
      remark: null,
      user_director_id: [],
      user_laterstage_id: [],
      user_operate_id: [],
      time_relation_inquiry: null,
      time_add_wechat: null,
      time_introduce_product: null,
      add_user_id: [],
      tab_type: 0,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const priceRef = ref();
  const addRef = ref();
  const cancelRef = ref();
  const formModel = reactive(generateFormModel());
  const route = useRoute();
  formModel.id = route.query.id as string;
  const dataCacheStore = useDataCacheStore();

  const baseSearchRules: any = ref([
    {
      field: 'id',
      label: '线索ID',
      value: null,
      symbol: '是',
    },
  ]);
  const searchRules = computed(() => [
    {
      field: 'area',
      label: '产品',
      value: null,
      component_name: markRaw(RequestSelect),
      alwaysShow: true,
      attr: {
        api: 'product',
      },
    },
    {
      field: 'platform',
      label: '来源媒体',
      value: null,
      component_name: markRaw(PlatformSelect),
      alwaysShow: true,
    },
    {
      title: '来源账号',
      dataIndex: 'source_account',
    },
    {
      field: 'from_setting',
      label: '来源详情',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '来源详情',
        allowClear: true,
      },
    },
    {
      field: 'phone_or_wechat',
      label: '手机/微信',
      value: null,
      component_name: 'a-input',
      attr: {
        placeholder: '手机/微信',
        allowClear: true,
      },
    },
    {
      field: 'two_service_user_fs_id',
      label: '电销',
      value: null,
      component_name: markRaw(requestSelect),
      alwaysShow: true,
      attr: {
        requestUrl: '/api/travel/allServiceUser',
        labelKey: 'user_name',
        valueKey: 'id',
      },
    },
    {
      field: 'status_two',
      label: '状态',
      value: null,
      alwaysShow: true,
      component_name: 'dict-select',
      attr: {
        dataList: clueStatusM,
      },
    },
    {
      field: 'time_into_two',
      label: '入库时间',
      value: null,
      component_name: 'a-range-picker',
    },
    {
      field: 'time_distribute_two',
      label: '分配时间',
      value: null,
      component_name: 'a-range-picker',
    },
    // {
    //   field: 'time_relation_inquiry',
    //   label: '询价并转单时间',
    //   value: null,
    //   component_name: 'a-range-picker',
    // },
    {
      field: 'source',
      label: '来源类型',
      value: null,
      component_name: 'dict-select',
      attr: {
        dataList: clueSuorceM,
      },
    },
    {
      field: 'remark',
      label: '备注',
      value: null,
      component_name: 'a-input',
      attr: {
        allowClear: true,
      },
    },
    {
      field: 'add_user_id',
      label: '添加人',
      value: null,
      component_name: markRaw(requestSelect),
      alwaysShow: true,
      attr: {
        requestUrl: '/api/user/userList',
        labelKey: 'user_name',
        sendParams: {
          pageSize: 999999,
        },
      },
    },
  ]);

  function dataHandle(list: any[]) {
    return list.map((item) => ({
      ...item,
      remark_new: item.remark,
    }));
  }
  const getList = async (data: any) => {
    return request('/api/thread/salesThreadList', {
      ...data,
    });
  };

  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        {
          title: '用户账号ID',
          dataIndex: 'account_id',
          width: 140,
        },
        {
          title: '用户账号昵称',
          dataIndex: 'account_name',
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          width: 130,
        },
        {
          title: '微信',
          dataIndex: 'wechat',
        },
        {
          title: '来源媒体',
          dataIndex: 'platform',
        },
        {
          title: '产品',
          dataIndex: 'area',
        },
        {
          title: '来源类型',
          dataIndex: 'source',
          width: 140,
        },
        {
          title: '来源详情',
          dataIndex: 'from_setting',
        },
        {
          title: '来源内容',
          dataIndex: 'thread_content',
          width: 300,
        },
        {
          title: '客服分配规则',
          dataIndex: 'distribute_rule',
          width: 140,
        },
        {
          title: '分配客服',
          dataIndex: 'two_service_user',
        },
        {
          title: '分配时间',
          dataIndex: 'time_distribute_two',
        },
        {
          title: '入库时间',
          dataIndex: 'time_into_two',
        },
        {
          title: '状态',
          dataIndex: 'status_two',
          width: 200,
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 300,
        },
        // {
        //  title: '添加人',
        //  dataIndex: 'add_user_name',
        // },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      // @ts-ignore
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(cloneDeep(allFieldsConfig[0].dataList));
  const columns = computed(() => [
    {
      title: '线索ID',
      dataIndex: 'id',
      width: 100,
      fixed: 'left',
    },
    ...columnsConfig.value,
    {
      title: '操作',
      dataIndex: 'action',
      width: 160,
      fixed: 'right',
    },
  ]);
  const scrollPercent = computed(() => ({
    maxHeight: '67vh',
    x: columns.value.length * 140,
  }));
  // table渲染完成回调
  const changeHandler = (tableData: any) => {
    loading.value = false;
  };

  const tableParams = computed(() => ({
    ...formModel,
  }));

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    delete resData.tab_type;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  function hideEditBtn(record: any) {
    setTimeout(() => {
      record.showBtn = false;
      record.remark_new = record.remark;
    }, 300);
  }
  function editRemark(record: any) {
    record.showBtn = false;
    record.remark = record.remark_new;
    request('/api/thread/addSales', {
      ...record,
      id: record.id,
      remark: record.remark_new,
    });
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }

  function addWechat(record: any) {
    if (!record.loading) {
      record.loading = true;
      // copy(record.wechat || record.phone);
      request('/api/thread/ensureAddWechat', {
        id: record.id,
        wechat: record.wechat,
      }).then(() => {
        theTable.value?.fetchData();
      });
    }
  }
  function hasCommunicate(record: any) {
    if (!record.loading) {
      record.loading = true;
      copy(record.wechat || record.phone);
      request('/api/thread/hadIntroduceProduct', {
        id: record.id,
      }).then(() => {
        theTable.value?.fetchData();
      });
    }
  }
  function editStatus(record: any, status: string) {
    if (!record.loading) {
      record.loading = true;
      request('/api/thread/setSaleStatus', {
        id: record.id,
        status_two: status,
      }).then(() => {
        theTable.value?.fetchData();
      });
    }
  }
</script>

<style scoped lang="less"></style>

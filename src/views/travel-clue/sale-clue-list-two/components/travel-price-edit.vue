<template>
  <d-modal
    v-model:visible="visible"
    width="860px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="modalTitle"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <template v-if="!pageConfig.createOrder">
        <div v-if="isBothCreateFlag" class="form-title">询价单信息</div>
        <a-divider>基础信息</a-divider>
        <a-row :gutter="[10, 0]">
          <a-col :span="12">
            <a-form-item label="客户昵称">
              <a-input v-model="formModel.customer_name" placeholder="请输入" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出游类型">
              <dict-select
                v-model="formModel.travel_type"
                :data-list="dataCacheStore.travelTypes"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="产品">
              <dict-select
                v-model="formModel.area"
                :data-list="dataCacheStore.travelLines"
                :allow-clear="false"
                @change="
                  formModel.travel_line = '';
                  formModel.local_travel_agency = '';
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="地接社">
              <dict-select
                v-model="formModel.local_travel_agency"
                :data-list="dataCacheStore.travelAgencys"
                :allow-clear="false"
                :data-scope="
                  dataCacheStore.travelProducts
                    .filter((item) => item['线路'] === formModel.area)
                    .map((item) => item['地接'])
                "
                @change="formModel.travel_line = ''"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="产品">
              <dict-select
                v-model="formModel.travel_line"
                :data-list="
                  dataCacheStore.travelProducts.filter(
                    (item) =>
                      item['线路'] === formModel.area &&
                      item['地接'] === formModel.local_travel_agency
                  )
                "
                :allow-clear="false"
                @change="
                  nextTick(() => {
                    formModel.travel_date =
                      curLineConfig.travel_date?.[0]?.value;
                    travelDateChange(
                      curLineConfig.travel_date?.[0]?.value,
                      curLineConfig.travel_date?.[0]
                    );
                  })
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="计划出游时间">
              <dict-select
                v-model="formModel.travel_date"
                :data-list="curLineConfig?.travel_date || []"
                label-key="name"
                :allow-clear="false"
                @change="travelDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="成人人数">
              <a-input-number
                v-model="formModel.adult_num"
                :min="1"
                placeholder="请输入"
                :precision="0"
                @change="
                  formModel.standard_room = formModel.adult_num;
                  adultNumChange();
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="儿童人数">
              <a-input-number
                v-model="formModel.children_num"
                placeholder="请输入"
                :min="0"
                :precision="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              :label="
                formModel.area === '哈尔滨'
                  ? '其中1.2m以内儿童人数'
                  : '其中三岁以下儿童人数'
              "
              label-col-flex="86px"
            >
              <a-input-number
                v-model="formModel.under_3_children_num"
                :min="0"
                :max="formModel.children_num || 1"
                :precision="0"
                @change="(value) => setNumberVal(value, 'under_3_children_num')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="床位数">
              <a-input-number
                v-model="formModel.standard_room"
                placeholder="请输入"
                :precision="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="车型">
              <dict-select
                v-model="formModel.car_type"
                :data-list="dataCacheStore.travelCars"
                :data-scope="[formModel.car_type]"
                label-key="car_type"
                value-key="car_type"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否接受拼车">
              <a-radio-group v-model="formModel.is_accept_carpooling">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="2">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="门市价">
              <a-input-number
                v-model="formModel.total_price"
                placeholder="请输入"
                disabled
                :precision="2"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="其他成本金额">
              <a-input-number
                v-model="formModel.other_cost_amount"
                placeholder="请输入"
              ></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="其他成本项说明">
              <a-input
                v-model="formModel.other_cost_remark"
                placeholder="请输入"
                allow-clear
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="总团款">
              <a-input-number
                v-model="formModel.total_cut_price"
                placeholder="请输入"
                :precision="2"
              />
            </a-form-item>
          </a-col>
          <template v-if="formModel.adult_num">
            <a-col :span="12">
              <a-form-item label="结算价">
                <a-input-number
                  v-if="isCustomLine"
                  v-model="formModel.total_final_price"
                  placeholder="请输入"
                  :precision="2"
                />
                <span v-else>
                  {{ formModel.total_final_price || '-' }}
                </span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="总团款公式">
                <span v-if="formModel.total_price_detail === '直播间套餐'">
                  {{ formModel.total_price_detail || '-' }}
                </span>
                <span v-else-if="formModel.total_price_detail">
                  {{ formModel.total_price_detail }}={{
                    total_cut_price_str_result
                  }}
                </span>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="结算价公式">
                <a-input
                  v-if="isCustomLine"
                  v-model="formModel.total_final_price_detail"
                  style="width: 100%"
                />
                <template
                  v-else-if="formModel.total_price_detail === '直播间套餐'"
                >
                  <span>
                    {{ formModel.total_final_price_detail }}={{
                      formModel.total_final_price
                    }}
                  </span>
                </template>
                <span v-else>
                  {{ formModel.total_final_price_detail }}={{
                    formModel.total_final_price
                  }}
                </span>
              </a-form-item>
            </a-col>
          </template>
          <a-col :span="24">
            <a-form-item label="总团款说明">
              <a-input
                v-model="formModel.total_cut_price_remark"
                placeholder="请输入"
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider>补录信息</a-divider>
        <a-row :gutter="[10, 0]">
          <a-col :span="12">
            <a-form-item label="来源媒体">
              <platform-select
                v-model="formModel.source"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="媒体账号">
              <a-input
                v-model="formModel.media_account"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号">
              <a-input
                v-model="formModel.phone"
                allow-clear
                placeholder="请输入"
                @blur="
                  formModel.order_info.phone =
                    formModel.order_info.phone || formModel.phone
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态">
              <dict-select
                v-model="formModel.status"
                :data-list="priceStatusListM"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="客服">
              <request-select
                v-model="formModel.service_user_fs_id"
                request-url="/api/travel/allServiceUser"
                label-key="user_name"
                value-key="fs_user_id"
                @change="
                  (val, item) => (formModel.service_user = item.user_name)
                "
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="出行日期">
              <a-date-picker
                v-model="formModel.real_travel_start_date"
                :disabled-date="
                  (current) => dayjs(current).isBefore(dayjs(), 'day')
                "
                class="w100p"
                allow-clear
                @change="changeTravelStartDate"
              ></a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="返程日期">
              <a-date-picker
                v-model="formModel.real_travel_end_date"
                class="w100p"
                allow-clear
              ></a-date-picker>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="备注">
              <a-textarea
                v-model="formModel.remark"
                placeholder="请输入"
                :auto-size="{ minRows: 3, maxRows: 10 }"
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <!-- 订单信息 -->
      <template v-if="isBothCreateFlag">
        <!-- 订单信息 -->
        <div class="form-title">订单信息</div>
        <a-divider>基础信息</a-divider>
        <a-row :gutter="[10, 0]">
          <a-col :span="12">
            <a-form-item
              label="来源媒体"
              field="order_info.source"
              :rules="{ required: true, message: '请输入', trigger: 'blur' }"
            >
              <platform-select
                v-model="formModel.order_info.source"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="订单来源"
              field="order_info.order_source"
              :rules="{ required: true, message: '请选择', trigger: 'blur' }"
            >
              <request-select
                v-model="formModel.order_info.order_source"
                request-url="/api/report/liveRoomList"
                :get-data-list="
                  (data) => [
                    { live_room_name: '内容私域' },
                    { live_room_name: '直播间私域' },
                    ...data,
                  ]
                "
                label-key="live_room_name"
                value-key="live_room_name"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="付款方式"
              field="order_info.payment_type"
              :rules="{ required: true, message: '请选择', trigger: 'blur' }"
            >
              <dict-select
                v-model="formModel.order_info.payment_type"
                :data-list="payTypeM"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="客服"
              field="order_info.service_user_fs_id"
              :rules="{ required: true, message: '请输入', trigger: 'blur' }"
            >
              <request-select
                v-model="formModel.order_info.service_user_fs_id"
                request-url="/api/travel/allServiceUser"
                label-key="user_name"
                value-key="fs_user_id"
                @change="
                  (val, item) =>
                    (formModel.order_info.service_user = item.user_name)
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号">
              <a-input
                v-model="formModel.order_info.phone"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider>订单信息</a-divider>
        <a-row
          v-for="(item, index) in formModel.order_info.media_order_info"
          :key="index"
          class="single-order-item"
          :gutter="[10, 0]"
        >
          <div class="form-title">
            <a-avatar
              :size="24"
              :style="{
                backgroundColor: '#14a9f8',
              }"
            >
              {{ index + 1 }}
            </a-avatar>
          </div>
          <div
            v-if="!formModel.transfer_offline_order && !pageConfig.createOrder"
            class="form-button-add"
          >
            <a-space>
              <a-button type="outline" size="mini" @click="addOrderFn(index)">
                新增
              </a-button>
              <a-button
                v-if="formModel.order_info.media_order_info.length > 1"
                type="outline"
                size="mini"
                @click="deleteOrderFn(index)"
              >
                删除
              </a-button>
            </a-space>
          </div>
          <a-col :span="12">
            <a-form-item
              label="媒体订单号"
              :field="`order_info.media_order_info.${index}.media_order_no`"
              :rules="{
                //required: formModel.order_info.source !== '线下',
                message: '请输入',
                trigger: 'blur',
              }"
            >
              <a-input
                v-model="item.media_order_no"
                placeholder="请输入"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="订金"
              :field="`order_info.media_order_info.${index}.advance_price`"
              :rules="{ required: true, message: '请输入', trigger: 'blur' }"
            >
              <a-input-number
                v-model="item.advance_price"
                placeholder="请输入"
                :precision="2"
                :min="0"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="订单创建时间"
              :field="`order_info.media_order_info.${index}.order_create_time`"
              :rules="{ required: true, message: '请选择', trigger: 'blur' }"
            >
              <a-date-picker
                v-model="item.order_create_time"
                placeholder="请选择"
                show-time
                class="w100p"
                allow-clear
              ></a-date-picker>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button
            v-if="showConfirmBtn"
            type="outline"
            @click="
              formModel.is_confirm_order = true;
              handleBeforeOk();
            "
          >
            订单已确认
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, nextTick, ref, watch } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message, Modal } from '@arco-design/web-vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import {
    payTypeM,
    priceStatusListM,
  } from '@/components/dict-select/dict-travel';
  import dayjs from 'dayjs';
  import { isUndefined } from 'lodash';
  import { useDataCacheStore } from '@/store';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';

  const defaultForm = () => ({
    customer_name: '', // #客户名称
    travel_type: '亲子', // #旅游需求
    travel_date: '', // #出游日期
    travel_date_show: '', // #出游日期
    travel_line: '', // 产品
    area: '', // 产品
    local_travel_agency: '', // 地接社
    adult_num: null, // #成人个数
    standard_room: null as null | number, // 床位数量
    children_num: null, // 儿童
    under_3_children_num: 0, // 其中三岁以下儿童
    is_accept_carpooling: 2 as any, // 是否接受拼车 1:接受 2:不接受
    total_price: null, // 门市价
    total_cut_price: null, // 总团款价格
    total_final_price: null, // 结算价
    car_type: null,
    setting: null,
    other_cost_amount: null,
    other_cost_remark: null,
    real_travel_start_date: null,
    real_travel_end_date: null as null | string,
    remark: null,
    total_cut_price_remark: null,

    id: null,
    status: null,
    source: null,
    media_account: null,
    phone: null as null | string,
    service_user_fs_id: null,
    service_user: null,
    total_price_detail: null,
    total_final_price_detail: null,

    final_order_no: null, // 订单ID
    is_confirm_order: undefined as undefined | boolean, // 是否确认订单 showConfirmBtn下有效

    transfer_offline_order: null, // 转线下的订单编号
    // 订单信息
    order_info: {
      order_source: null, // 订单来源
      payment_type: '线下', // 付款方式
      source: '',
      service_user: '',
      service_user_fs_id: '',
      phone: '' as null | string,
      media_order_info: [
        {
          media_order_no: '',
          advance_price: null as number | null,
          order_create_time: '',
        },
      ],
    },
    thread_id: null, // 二级线索ID
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const showConfirmBtn = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  // 询价单和订单同时创建的标识
  const isBothCreateFlag = ref(false);
  let pageConfig = ref<any>({});
  const dataCacheStore = useDataCacheStore();

  const curLineConfig = computed<any>(
    () =>
      dataCacheStore.travelProducts.find(
        (item) => item.value === formModel.value.travel_line
      ) || {}
  );

  const total_cut_price_str_result = computed(() => {
    try {
      // eslint-disable-next-line
      return eval(formModel.value.total_price_detail || '');
    } catch (e) {
      return '';
    }
  });

  const modalTitle = computed(() => {
    if (pageConfig.value.createOrder) {
      return `创建订单`;
    }
    if (formModel.value.transfer_offline_order) {
      return `转线下（${formModel.value.transfer_offline_order}）`;
    }
    return `${formModel.value.id ? '编辑' : '新建'}询价单${
      isBothCreateFlag.value ? '和订单' : ''
    }信息`;
  });

  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  function adultNumChange() {
    dataCacheStore.travelCars.some((item: any) => {
      if (
        parseInt(item.car_people_num, 10) >=
        (formModel.value.adult_num || 0) + (formModel.value.children_num || 0)
      ) {
        formModel.value.car_type = item.car_type;
        return true;
      }
      return false;
    });
  }

  // 不要继续追加参数个数了 把参数都加到config里面做判断用
  const show = async (dinfo: any, config: any, transIsBothCreateFlag: any) => {
    // 是否同时创建订单询价单
    isBothCreateFlag.value = !!transIsBothCreateFlag;
    // extraOrderInfo 目前用来转线下 是订单信息
    // showConfirm 是否展示确认订单按钮
    // needInfo 是否需要根据询价单号拉取询价详情
    // createOrder 创建跟询价单关联的订单
    const { needInfo, showConfirm, extraOrderInfo, createOrder } = config;
    pageConfig.value = config;
    let initForm = defaultForm();
    formModel.value = initForm;
    loading.value = true;
    showConfirmBtn.value = showConfirm || false;
    if (dinfo) {
      let { final_order_no } = dinfo;
      if (needInfo && dinfo.inquiry_order_no) {
        dinfo = (
          await request('/api/travel/orderInfo', {
            order_no: dinfo.inquiry_order_no,
          })
        ).data;
      }
      Object.keys(initForm).forEach((key) => {
        if (
          dinfo[key] &&
          [
            'total_price',
            'total_cut_price',
            'total_final_price',
            'other_cost_amount',
          ].includes(key)
        ) {
          // @ts-ignore
          formModel.value[key] = parseFloat(dinfo[key]);
        } else {
          // @ts-ignore
          formModel.value[key] = isUndefined(dinfo[key])
            ? // @ts-ignore
              initForm[key]
            : dinfo[key];
        }
      });
      formModel.value.final_order_no = final_order_no;

      if (
        formModel.value.is_accept_carpooling === '接受' ||
        formModel.value.is_accept_carpooling === 1
      ) {
        formModel.value.is_accept_carpooling = 1;
      } else {
        formModel.value.is_accept_carpooling = 2;
      }
      formModel.value.total_price_detail = dinfo.total_price_detail;
      formModel.value.total_final_price_detail = dinfo.total_final_price_detail;
      formModel.value.under_3_children_num = dinfo.under_3_children_num || 0;

      // 转线下
      if (extraOrderInfo) {
        formModel.value.transfer_offline_order = extraOrderInfo.order_no;
        Object.keys(initForm.order_info).forEach((key) => {
          if (extraOrderInfo[key] && [].includes(key)) {
            // @ts-ignore
            formModel.value.order_info[key] = parseFloat(extraOrderInfo[key]);
          } else {
            // @ts-ignore
            formModel.value.order_info[key] = isUndefined(extraOrderInfo[key])
              ? // @ts-ignore
                initForm.order_info[key]
              : extraOrderInfo[key];
          }
        });
        Object.keys(initForm.order_info.media_order_info[0]).forEach((key) => {
          if (extraOrderInfo[key] && ['advance_price'].includes(key)) {
            // @ts-ignore
            formModel.value.order_info.media_order_info[0][key] = parseFloat(
              extraOrderInfo[key]
            );
          } else {
            // @ts-ignore
            formModel.value.order_info.media_order_info[0][key] = isUndefined(
              extraOrderInfo[key]
            )
              ? // @ts-ignore
                initForm.order_info.media_order_info[0][key]
              : extraOrderInfo[key];
          }
        });
      }

      if (createOrder || isBothCreateFlag.value) {
        formModel.value.order_info.source = dinfo.source;
        formModel.value.order_info.service_user = dinfo.service_user;
        formModel.value.order_info.service_user_fs_id =
          dinfo.service_user_fs_id;
        formModel.value.order_info.phone = dinfo.phone;
        formModel.value.order_info.order_source = dinfo.order_source;
        formModel.value.order_info.media_order_info[0].advance_price = 0;
        formModel.value.order_info.media_order_info[0].order_create_time =
          dayjs().format('YYYY-MM-DD HH:mm:ss');
      }
    }
    nextTick(() => {
      loading.value = false;
      adultNumChange();
      visible.value = true;
    });
  };

  function setNumberVal(val: any, key: string) {
    if (isUndefined(val)) {
      // @ts-ignore
      formModel.value[key as keyof typeof formModel.value] = null;
    }
  }

  const isCustomLine = computed(() =>
    formModel.value.travel_line?.includes('路线7')
  );

  let cancelToken: any;
  // 计算价格
  const calculatePrice = () => {
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    request(
      '/api/travel/orderCalculate',
      {
        hideMsgTips: true,
        car_people_num:
          dataCacheStore.travelCars.find(
            (item: any) => item.car_type === formModel.value.car_type
            // @ts-ignore
          )?.car_people_num || 0,
        ...formModel.value,
      },
      cancelToken.signal
    ).then((res) => {
      if (isCustomLine.value) {
        delete res.data.total_final_price;
        delete res.data.total_final_price_detail;
      }
      Object.assign(formModel.value, res.data);
    });
  };

  watch(
    () => {
      return [
        formModel.value.travel_date, // #出游日期
        formModel.value.travel_line, // #路由线路
        formModel.value.adult_num, // #成人个数
        formModel.value.standard_room, // 床位数量
        formModel.value.children_num, // 儿童
        formModel.value.under_3_children_num, // 三岁以下儿童
        formModel.value.is_accept_carpooling, // 是否接受拼车 1:接受 2:不接受
        formModel.value.car_type,
        formModel.value.other_cost_amount, // 其他成本
      ];
    },
    (v, oldValue) => {
      if (
        !loading.value &&
        visible.value &&
        formModel.value.travel_line &&
        formModel.value.car_type
      ) {
        nextTick(() => {
          calculatePrice();
        });
      }
    }
  );

  function travelDateChange(val: string, item: any) {
    formModel.value.travel_date_show = item?.name || '';
  }

  const changeTravelStartDate = (val: any) => {
    if (val && formModel.value.travel_line) {
      let dayNightNumber = curLineConfig.value['几晚'] || 0;
      if (dayNightNumber) {
        formModel.value.real_travel_end_date = dayjs(val)
          .add(dayNightNumber, 'day')
          .format('YYYY-MM-DD');
      }
    }
  };
  // 新增订单
  const addOrderFn = (index: number) => {
    formModel.value.order_info.media_order_info.splice(index + 1, 0, {
      media_order_no: '',
      advance_price: null,
      order_create_time: '',
    });
  };

  // 删除订单
  const deleteOrderFn = (index: number) => {
    formModel.value.order_info.media_order_info.splice(index, 1);
  };

  const handleCancel = () => {
    visible.value = false;
    clearValidate();
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      if (
        formModel.value.real_travel_end_date &&
        formModel.value.real_travel_start_date
      ) {
        if (
          dayjs(formModel.value.real_travel_start_date).isAfter(
            dayjs(formModel.value.real_travel_end_date)
          )
        ) {
          Message.error('出行日期不能晚于返程日期');
          return;
        }
      }
      loading.value = true;
      let APIUrl = '/api/travel/orderSave';

      let sendParams: any = { ...formModel.value };

      if (isBothCreateFlag.value) {
        APIUrl = '/api/travel/addOrderAndInquiryOrder';
        if (pageConfig.value.createOrder) {
          sendParams = {
            inquiry_order_info: {
              id: formModel.value.id,
            },
            order_info: formModel.value.order_info,
          };
        } else {
          sendParams = {
            inquiry_order_info: formModel.value,
            order_info: formModel.value.order_info,
          };
        }
      }
      if (formModel.value.transfer_offline_order) {
        APIUrl = '/api/travel/transferOfflineOrder';
        sendParams = {
          inquiry_order_info: formModel.value,
          transfer_offline_order: formModel.value.transfer_offline_order,
          order_info: {
            ...formModel.value.order_info,
            ...formModel.value.order_info.media_order_info[0],
          },
        };
      }

      request(APIUrl, sendParams)
        .then((resData) => {
          if (
            isBothCreateFlag.value &&
            sendParams.inquiry_order_info.thread_id
          ) {
            Modal.success({
              title: '保存成功',
              content: `询价单号：${resData.data.inquiry_order_no}，订单编号：${resData.data.order_no}。`,
            });
          } else {
            Message.success('保存成功');
          }
          emit('save');
          handleCancel();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .arco-modal-body {
    padding: 0 20px;
  }

  .arco-divider-horizontal.arco-divider-with-text {
    margin: 10px 0 20px;
  }

  .form-title {
    font-size: 16px;
    font-weight: bold;
  }

  .single-order-item {
    position: relative;
    padding: 40px 10px 10px;
    border: 1px solid var(--color-border-1);
    margin-bottom: 2%;
    border-radius: 4px;
    .form-title {
      position: absolute;
      top: 10px;
    }
    .form-button-add {
      position: absolute;
      right: 16px;
      top: 10px;
    }
  }
</style>

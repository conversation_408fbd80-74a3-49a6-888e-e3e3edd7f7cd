<template>
  <div class="emoji-text">
    <template v-for="(part, index) in parsedContent" :key="index">
      <img
        v-if="isEmoji(part)"
        class="msg-emoji"
        :src="`/${config.filePath}/${part}.png`"
        :alt="part"
        :title="part"
        @click="emits('select', part)"
      />
      <div v-else-if="isImageLink(part)" class="image-container">
        <a-image
          v-if="!failedImages.has(part)"
          class="msg-image"
          :src="cleanImageUrl(part)"
          :alt="'图片'"
          :height="100"
          @click="emits('imageClick', part)"
          @error="handleImageError"
          @load="handleImageLoad"
        />
        <div v-else class="image-error">
          <span class="error-text">图片加载失败</span>
          <button class="retry-btn" @click="retryImage(part)">重试</button>
        </div>
      </div>
      <span v-else>{{ part }}</span>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';

  const props = defineProps({
    content: {
      type: String,
      default: '',
    },
    platform: {
      type: String,
      default: '小红书',
    },
  });

  // 用于存储加载失败的图片链接
  const failedImages = ref<Set<string>>(new Set());

  const douyin = [
    '[微笑]',
    '[色]',
    '[发呆]',
    '[酷拽]',
    '[抠鼻]',
    '[流泪]',
    '[捂脸]',
    '[发怒]',
    '[呲牙]',
    '[尬笑]',
    '[害羞]',
    '[调皮]',
    '[舔屏]',
    '[看]',
    '[爱心]',
    '[比心]',
    '[赞]',
    '[鼓掌]',
    '[感谢]',
    '[抱抱你]',
    '[玫瑰]',
    '[灵机一动]',
    '[耶]',
    '[打脸]',
    '[大笑]',
    '[机智]',
    '[送心]',
    '[666]',
    '[闭嘴]',
    '[来看我]',
    '[一起加油]',
    '[哈欠]',
    '[震惊]',
    '[晕]',
    '[衰]',
    '[困]',
    '[疑问]',
    '[泣不成声]',
    '[小鼓掌]',
    '[大金牙]',
    '[偷笑]',
    '[石化]',
    '[思考]',
    '[吐血]',
    '[可怜]',
    '[嘘]',
    '[撇嘴]',
    '[笑哭]',
    '[奸笑]',
    '[得意]',
    '[憨笑]',
    '[坏笑]',
    '[抓狂]',
    '[泪奔]',
    '[钱]',
    '[恐惧]',
    '[愉快]',
    '[快哭了]',
    '[翻白眼]',
    '[互粉]',
    '[我想静静]',
    '[委屈]',
    '[鄙视]',
    '[飞吻]',
    '[再见]',
    '[紫薇别走]',
    '[听歌]',
    '[求抱抱]',
    '[绝望的凝视]',
    '[不失礼貌的微笑]',
    '[不看]',
    '[裂开]',
    '[干饭人]',
    '[吐舌]',
    '[呆无辜]',
    '[白眼]',
    '[熊吉]',
    '[猪头]',
    '[冷漠]',
    '[微笑袋鼠]',
    '[凝视]',
    '[暗中观察]',
    '[二哈]',
    '[菜狗]',
    '[黑脸]',
    '[展开说说]',
    '[蜜蜂狗]',
    '[摸头]',
    '[皱眉]',
    '[擦汗]',
    '[红脸]',
    '[做鬼脸]',
    '[强]',
    '[如花]',
    '[吐]',
    '[惊喜]',
    '[敲打]',
    '[奋斗]',
    '[吐彩虹]',
    '[大哭]',
    '[嘿哈]',
    '[加好友]',
    '[惊恐]',
    '[惊讶]',
    '[囧]',
    '[难过]',
    '[斜眼]',
    '[阴险]',
    '[悠闲]',
    '[咒骂]',
    '[吃瓜群众]',
    '[绿帽子]',
    '[真的会谢]',
    '[达咩]',
    '[敢怒不敢言]',
    '[投降]',
    '[求求了]',
    '[眼含热泪]',
    '[叹气]',
    '[好开心]',
    '[不是吧]',
    '[动动脑子]',
    '[表面微笑]',
    '[表面呲牙]',
    '[鞠躬]',
    '[躺平]',
    '[九转大肠]',
    '[敲木鱼]',
    '[不你不想]',
    '[一头乱麻]',
    '[kisskiss]',
    '[你不大行]',
    '[噢买尬]',
    '[宕机]',
    '[还得是我]',
    '[6]',
    '[脸疼]',
    '[他急了]',
    '[苦涩]',
    '[逞强落泪]',
    '[强壮]',
    '[碰拳]',
    '[OK]',
    '[击掌]',
    '[左上]',
    '[握手]',
    '[抱拳]',
    '[勾引]',
    '[拳头]',
    '[弱]',
    '[胜利]',
    '[右边]',
    '[左边]',
    '[亚运鼓掌]',
    '[金牌]',
    '[手花]',
    '[嘴唇]',
    '[心碎]',
    '[凋谢]',
    '[啤酒]',
    '[咖啡]',
    '[蛋糕]',
    '[礼物]',
    '[撒花]',
    '[加一]',
    '[减一]',
    '[okk]',
    '[V5]',
    '[绝]',
    '[给力]',
    '[红包]',
    '[屎]',
    '[发]',
    '[我太难了]',
    '[18禁]',
    '[炸弹]',
    '[去污粉]',
    '[西瓜]',
    '[加鸡腿]',
    '[我酸了]',
    '[握爪]',
    '[太阳]',
    '[月亮]',
    '[给跪了]',
    '[蕉绿]',
    '[扎心]',
    '[胡瓜]',
    '[yyds]',
    '[emo]',
    '[开心兔]',
    '[招财兔]',
    '[年兽兔]',
    '[打call]',
    '[栓Q]',
    '[无语]',
    '[雪人]',
    '[雪花]',
    '[圣诞树]',
    '[平安果]',
    '[圣诞帽]',
    '[气球]',
    '[干杯]',
    '[烟花]',
    '[福]',
    '[candy]',
    '[糖葫芦]',
    '[虎头]',
    '[饺子]',
    '[鞭炮]',
    '[元宝]',
    '[灯笼]',
    '[锦鲤]',
    '[巧克力]',
    '[汤圆]',
    '[情书]',
    '[iloveyou]',
    '[戒指]',
    '[小黄鸭]',
    '[棒棒糖]',
    '[纸飞机]',
    '[必胜]',
    '[粽子]',
  ];

  const xiaohongshu = [
    '[微笑R]',
    '[害羞R]',
    '[失望R]',
    '[汗颜R]',
    '[哇R]',
    '[喝奶茶R]',
    '[自拍R]',
    '[偷笑R]',
    '[飞吻R]',
    '[石化R]',
    '[笑哭R]',
    '[赞R]',
    '[蹲后续H]',
    '[暗中观察R]',
    '[买爆R]',
    '[大笑R]',
    '[色色R]',
    '[生气R]',
    '[哭惹R]',
    '[萌萌哒R]',
    '[斜眼R]',
    '[可怜R]',
    '[鄙视R]',
    '[皱眉R]',
    '[抓狂R]',
    '[捂脸R]',
    '[派对R]',
    '[吧唧R]',
    '[惊恐R]',
    '[抠鼻R]',
    '[再见R]',
    '[叹气R]',
    '[睡觉R]',
    '[得意R]',
    '[吃瓜R]',
    '[扶墙R]',
    '[黑薯问号R]',
    '[黄金薯R]',
    '[吐舌头H]',
    '[扯脸H]',
    '[doge]',
    '[天幕R]',
    '[卡式炉R]',
    '[折叠椅R]',
    '[营地车R]',
    '[露营灯R]',
    '[露营R]',
    '[渔夫帽R]',
    '[登山鞋R]',
    '[背包R]',
    '[马甲R]',
    '[骑行服R]',
    '[手套R]',
    '[头盔R]',
    '[风镜R]',
    '[公路车R]',
    '[折叠车R]',
    '[飞盘R]',
    '[冲浪板R]',
    '[双翘滑板R]',
    '[陆冲板R]',
    '[长板R]',
    '[种草R]',
    '[拔草R]',
    '[点赞R]',
    '[向右R]',
    '[合十R]',
    '[okR]',
    '[加油R]',
    '[握手R]',
    '[鼓掌R]',
    '[弱R]',
    '[耶R]',
    '[抱拳R]',
    '[勾引R]',
    '[拳头R]',
    '[拥抱R]',
    '[举手R]',
    '[猪头R]',
    '[老虎R]',
    '[集美R]',
    '[仙女R]',
    '[红书R]',
    '[开箱R]',
    '[探店R]',
    '[ootdR]',
    '[同款R]',
    '[打卡R]',
    '[飞机R]',
    '[拍立得R]',
    '[薯券R]',
    '[优惠券R]',
    '[购物车R]',
    '[kissR]',
    '[礼物R]',
    '[生日蛋糕R]',
    '[私信R]',
    '[请文明R]',
    '[请友好R]',
    '[氛围感R]',
    '[清单R]',
    '[电影R]',
    '[学生党R]',
    '[彩虹R]',
    '[爆炸R]',
    '[炸弹R]',
    '[火R]',
    '[啤酒R]',
    '[咖啡R]',
    '[钱袋R]',
    '[流汗R]',
    '[发R]',
    '[红包R]',
    '[福R]',
    '[鞭炮R]',
    '[庆祝R]',
    '[烟花R]',
    '[气球R]',
    '[看R]',
    '[新月R]',
    '[满月R]',
    '[大便R]',
    '[太阳R]',
    '[晚安R]',
    '[星R]',
    '[玫瑰R]',
    '[凋谢R]',
    '[郁金香R]',
    '[樱花R]',
    '[海豚R]',
    '[放大镜R]',
    '[刀R]',
    '[辣椒R]',
    '[黄瓜R]',
    '[葡萄R]',
    '[草莓R]',
    '[桃子R]',
    '[红薯R]',
    '[栗子R]',
    '[红色心形R]',
    '[黄色心形R]',
    '[绿色心形R]',
    '[蓝色心形R]',
    '[紫色心形R]',
    '[爱心R]',
    '[两颗心R]',
    '[浅肤色R]',
    '[中浅肤色R]',
    '[中等肤色R]',
    '[中深肤色R]',
    '[有R]',
    '[可R]',
    '[蹲R]',
    '[零R]',
    '[一R]',
    '[二R]',
    '[三R]',
    '[四R]',
    '[五R]',
    '[六R]',
    '[七R]',
    '[八R]',
    '[九R]',
    '[加一R]',
    '[满R]',
    '[禁R]',
  ];

  const detectPlatform = (content: string) => {
    // 如果平台已经指定，则直接返回
    if (props.platform) {
      return props.platform;
    }

    const xiaohongshuPattern = /\[.*?R\]/;
    const douyinPattern = /\[.*?\]/;

    // 如果包含小红书特有的表情格式（以R结尾），则判定为小红书
    if (xiaohongshuPattern.test(content)) {
      return '小红书';
    }
    // 如果包含抖音表情格式，则判定为抖音
    if (douyinPattern.test(content)) {
      return '抖音';
    }
    // 默认返回抖音
    return '抖音';
  };

  // 检测是否为图片链接
  const isImageLink = (text: string) => {
    // 过滤掉类似 (¦3[▓▓] 这种非标准图片链接
    if (/[^\s[\](){}]+\[.*?\]/.test(text)) return false;
    // 检查是否已经加载失败
    if (failedImages.value.has(text)) {
      return false;
    }

    // 检测常见的图片链接格式
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|bmp|svg)(\?.*)?$/i;
    const imageDomains =
      /(xhscdn\.com|douyin\.com|tiktok\.com|pengwin\.com|weibo\.com|qq\.com|sinaimg\.cn|sns-webpic-qc\.xhscdn\.com)/i;

    // 检查是否为有效的URL
    try {
      const url = new URL(text);
      // 检查域名或文件扩展名
      return (
        imageExtensions.test(url.pathname) || imageDomains.test(url.hostname)
      );
    } catch {
      // 如果不是有效URL，检查是否包含图片特征
      return imageExtensions.test(text) || imageDomains.test(text);
    }
  };

  // 处理图片加载错误
  const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    failedImages.value.add(img.src);
  };

  // 处理图片加载成功
  const handleImageLoad = (event: Event) => {
    const img = event.target as HTMLImageElement;
    // 图片加载成功，从失败列表中移除
    failedImages.value.delete(img.src);
  };

  // 重试加载图片
  const retryImage = (imageUrl: string) => {
    failedImages.value.delete(imageUrl);
  };

  // 清理链接文本，移除@前缀
  const cleanImageUrl = (text: string) => {
    if (text.startsWith('@')) {
      return text.substring(1);
    }
    return text;
  };

  // 分割文本和链接
  const splitTextAndLinks = (text: string) => {
    const result = [];
    const urlRegex = /(@?https?:\/\/[^\s]+)/g;
    let lastIndex = 0;
    let match = urlRegex.exec(text);

    while (match !== null) {
      // 添加链接前的文本
      if (match.index > lastIndex) {
        const beforeText = text.slice(lastIndex, match.index);
        if (beforeText) {
          result.push(beforeText);
        }
      }
      // 添加链接
      result.push(match[0]);
      lastIndex = match.index + match[0].length;
      match = urlRegex.exec(text);
    }

    // 添加剩余的文本
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      if (remainingText) {
        result.push(remainingText);
      }
    }

    return result;
  };

  // 解析文本中的表情符号
  const parseTextWithEmojis = (text: string) => {
    const emojiRegex = /\[.*?\]/g;
    const parts = text.split(emojiRegex);
    const matches = text.match(emojiRegex) || [];

    const result = [];
    for (let i = 0; i < parts.length; i += 1) {
      if (parts[i]) {
        result.push(parts[i]);
      }
      if (i < matches.length) {
        result.push(matches[i]);
      }
    }
    return result;
  };

  const parsedContent = computed(() => {
    // 先处理图片链接，将其从文本中分离
    const imageUrlRegex =
      /\[(https?:\/\/[^\]]+\.(jpg|jpeg|png|gif|webp|bmp|svg)(\?[^\]]*)?)\]/gi;
    const parts = [];
    let lastIndex = 0;
    let match;

    // 重置正则表达式的lastIndex
    imageUrlRegex.lastIndex = 0;

    match = imageUrlRegex.exec(props.content);
    while (match !== null) {
      // 添加图片链接前的文本
      if (match.index > lastIndex) {
        const beforeText = props.content.slice(lastIndex, match.index).trim();
        if (beforeText) {
          // 进一步处理表情符号
          const textParts = parseTextWithEmojis(beforeText);
          parts.push(...textParts);
        }
      }

      // 添加图片链接
      parts.push(match[1]); // 提取URL（不包含方括号）
      lastIndex = match.index + match[0].length;
      match = imageUrlRegex.exec(props.content);
    }

    // 添加剩余的文本
    if (lastIndex < props.content.length) {
      const remainingText = props.content.slice(lastIndex).trim();
      if (remainingText) {
        const textParts = parseTextWithEmojis(remainingText);
        parts.push(...textParts);
      }
    }

    return parts;
  });

  const emits = defineEmits(['select', 'imageClick']);
  const config = computed(() => {
    const platform = detectPlatform(props.content);
    switch (platform) {
      case '小红书':
        return {
          filePath: 'xiaohongshu-emoji',
          emojis: xiaohongshu,
        };
      case '抖音':
      default:
        return {
          filePath: 'douyin-emoji',
          emojis: douyin,
        };
    }
  });

  // 修正 isEmoji 的定义顺序，确保 config 已定义
  const isEmoji = (text: string) => {
    // 只将以 [ 开头且以 ] 结尾且内容在 emoji 列表中的内容识别为 emoji
    if (!text.startsWith('[') || !text.endsWith(']')) return false;
    return config.value.emojis.includes(text);
  };
</script>

<style scoped lang="less">
  .emoji-text {
    display: inline-block;
    line-height: 1.5;
  }

  .msg-emoji {
    display: inline-block;
    vertical-align: sub;
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin: 0px 2px;
  }

  .msg-image {
    display: inline-block;
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
    cursor: pointer;
    margin: 2px;
    object-fit: cover;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.01);
    }
  }

  .image-container {
    display: block;
    margin: 4px 0;
  }

  .image-error {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 80px;
    border: 1px dashed #ccc;
    border-radius: 4px;
    background-color: #f5f5f5;
    margin: 2px;
    font-size: 12px;
    color: #666;
  }

  .error-text {
    margin-bottom: 4px;
  }

  .retry-btn {
    padding: 8px 16px;
    font-size: 11px;
    color: #fff;
    background-color: #1890ff;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #40a9ff;
    }
  }
</style>

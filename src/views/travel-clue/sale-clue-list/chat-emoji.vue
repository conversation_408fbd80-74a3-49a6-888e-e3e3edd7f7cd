<template>
  <a-popover
    v-model:popup-visible="visible"
    trigger="click"
    position="top"
    popup-container="document.body"
  >
    <template #content>
      <div class="emoji-box">
        <img
          v-for="item in config.emojis"
          :key="item"
          class="msg-emoji"
          :src="`/${config.filePath}/${item}.png`"
          :alt="item"
          :title="item"
          @click="handleSelect(item)"
        />
      </div>
    </template>
    <slot></slot>
  </a-popover>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';

  const props = defineProps({
    platform: {
      type: String,
      default: '小红书',
    },
  });
  const douyin = [
    '[微笑]',
    '[色]',
    '[发呆]',
    '[酷拽]',
    '[抠鼻]',
    '[流泪]',
    '[捂脸]',
    '[发怒]',
    '[呲牙]',
    '[尬笑]',
    '[害羞]',
    '[调皮]',
    '[舔屏]',
    '[看]',
    '[爱心]',
    '[比心]',
    '[赞]',
    '[鼓掌]',
    '[感谢]',
    '[抱抱你]',
    '[玫瑰]',
    '[灵机一动]',
    '[耶]',
    '[打脸]',
    '[大笑]',
    '[机智]',
    '[送心]',
    '[666]',
    '[闭嘴]',
    '[来看我]',
    '[一起加油]',
    '[哈欠]',
    '[震惊]',
    '[晕]',
    '[衰]',
    '[困]',
    '[疑问]',
    '[泣不成声]',
    '[小鼓掌]',
    '[大金牙]',
    '[偷笑]',
    '[石化]',
    '[思考]',
    '[吐血]',
    '[可怜]',
    '[嘘]',
    '[撇嘴]',
    '[笑哭]',
    '[奸笑]',
    '[得意]',
    '[憨笑]',
    '[坏笑]',
    '[抓狂]',
    '[泪奔]',
    '[钱]',
    '[恐惧]',
    '[愉快]',
    '[快哭了]',
    '[翻白眼]',
    '[互粉]',
    '[我想静静]',
    '[委屈]',
    '[鄙视]',
    '[飞吻]',
    '[再见]',
    '[紫薇别走]',
    '[听歌]',
    '[求抱抱]',
    '[绝望的凝视]',
    '[不失礼貌的微笑]',
    '[不看]',
    '[裂开]',
    '[干饭人]',
    '[吐舌]',
    '[呆无辜]',
    '[白眼]',
    '[熊吉]',
    '[猪头]',
    '[冷漠]',
    '[微笑袋鼠]',
    '[凝视]',
    '[暗中观察]',
    '[二哈]',
    '[菜狗]',
    '[黑脸]',
    '[展开说说]',
    '[蜜蜂狗]',
    '[摸头]',
    '[皱眉]',
    '[擦汗]',
    '[红脸]',
    '[做鬼脸]',
    '[强]',
    '[如花]',
    '[吐]',
    '[惊喜]',
    '[敲打]',
    '[奋斗]',
    '[吐彩虹]',
    '[大哭]',
    '[嘿哈]',
    '[加好友]',
    '[惊恐]',
    '[惊讶]',
    '[囧]',
    '[难过]',
    '[斜眼]',
    '[阴险]',
    '[悠闲]',
    '[咒骂]',
    '[吃瓜群众]',
    '[绿帽子]',
    '[真的会谢]',
    '[达咩]',
    '[敢怒不敢言]',
    '[投降]',
    '[求求了]',
    '[眼含热泪]',
    '[叹气]',
    '[好开心]',
    '[不是吧]',
    '[动动脑子]',
    '[表面微笑]',
    '[表面呲牙]',
    '[鞠躬]',
    '[躺平]',
    '[九转大肠]',
    '[敲木鱼]',
    '[不你不想]',
    '[一头乱麻]',
    '[kisskiss]',
    '[你不大行]',
    '[噢买尬]',
    '[宕机]',
    '[还得是我]',
    '[6]',
    '[脸疼]',
    '[他急了]',
    '[苦涩]',
    '[逞强落泪]',
    '[强壮]',
    '[碰拳]',
    '[OK]',
    '[击掌]',
    '[左上]',
    '[握手]',
    '[抱拳]',
    '[勾引]',
    '[拳头]',
    '[弱]',
    '[胜利]',
    '[右边]',
    '[左边]',
    '[亚运鼓掌]',
    '[金牌]',
    '[手花]',
    '[嘴唇]',
    '[心碎]',
    '[凋谢]',
    '[啤酒]',
    '[咖啡]',
    '[蛋糕]',
    '[礼物]',
    '[撒花]',
    '[加一]',
    '[减一]',
    '[okk]',
    '[V5]',
    '[绝]',
    '[给力]',
    '[红包]',
    '[屎]',
    '[发]',
    '[我太难了]',
    '[18禁]',
    '[炸弹]',
    '[去污粉]',
    '[西瓜]',
    '[加鸡腿]',
    '[我酸了]',
    '[握爪]',
    '[太阳]',
    '[月亮]',
    '[给跪了]',
    '[蕉绿]',
    '[扎心]',
    '[胡瓜]',
    '[yyds]',
    '[emo]',
    '[开心兔]',
    '[招财兔]',
    '[年兽兔]',
    '[打call]',
    '[栓Q]',
    '[无语]',
    '[雪人]',
    '[雪花]',
    '[圣诞树]',
    '[平安果]',
    '[圣诞帽]',
    '[气球]',
    '[干杯]',
    '[烟花]',
    '[福]',
    '[candy]',
    '[糖葫芦]',
    '[虎头]',
    '[饺子]',
    '[鞭炮]',
    '[元宝]',
    '[灯笼]',
    '[锦鲤]',
    '[巧克力]',
    '[汤圆]',
    '[情书]',
    '[iloveyou]',
    '[戒指]',
    '[小黄鸭]',
    '[棒棒糖]',
    '[纸飞机]',
    '[必胜]',
    '[粽子]',
  ];

  const xiaohongshu = [
    '[微笑R]',
    '[害羞R]',
    '[失望R]',
    '[汗颜R]',
    '[哇R]',
    '[喝奶茶R]',
    '[自拍R]',
    '[偷笑R]',
    '[飞吻R]',
    '[石化R]',
    '[笑哭R]',
    '[赞R]',
    '[蹲后续H]',
    '[暗中观察R]',
    '[买爆R]',
    '[大笑R]',
    '[色色R]',
    '[生气R]',
    '[哭惹R]',
    '[萌萌哒R]',
    '[斜眼R]',
    '[可怜R]',
    '[鄙视R]',
    '[皱眉R]',
    '[抓狂R]',
    '[捂脸R]',
    '[派对R]',
    '[吧唧R]',
    '[惊恐R]',
    '[抠鼻R]',
    '[再见R]',
    '[叹气R]',
    '[睡觉R]',
    '[得意R]',
    '[吃瓜R]',
    '[扶墙R]',
    '[黑薯问号R]',
    '[黄金薯R]',
    '[吐舌头H]',
    '[扯脸H]',
    '[doge]',
    '[天幕R]',
    '[卡式炉R]',
    '[折叠椅R]',
    '[营地车R]',
    '[露营灯R]',
    '[露营R]',
    '[渔夫帽R]',
    '[登山鞋R]',
    '[背包R]',
    '[马甲R]',
    '[骑行服R]',
    '[手套R]',
    '[头盔R]',
    '[风镜R]',
    '[公路车R]',
    '[折叠车R]',
    '[飞盘R]',
    '[冲浪板R]',
    '[双翘滑板R]',
    '[陆冲板R]',
    '[长板R]',
    '[种草R]',
    '[拔草R]',
    '[点赞R]',
    '[向右R]',
    '[合十R]',
    '[okR]',
    '[加油R]',
    '[握手R]',
    '[鼓掌R]',
    '[弱R]',
    '[耶R]',
    '[抱拳R]',
    '[勾引R]',
    '[拳头R]',
    '[拥抱R]',
    '[举手R]',
    '[猪头R]',
    '[老虎R]',
    '[集美R]',
    '[仙女R]',
    '[红书R]',
    '[开箱R]',
    '[探店R]',
    '[ootdR]',
    '[同款R]',
    '[打卡R]',
    '[飞机R]',
    '[拍立得R]',
    '[薯券R]',
    '[优惠券R]',
    '[购物车R]',
    '[kissR]',
    '[礼物R]',
    '[生日蛋糕R]',
    '[私信R]',
    '[请文明R]',
    '[请友好R]',
    '[氛围感R]',
    '[清单R]',
    '[电影R]',
    '[学生党R]',
    '[彩虹R]',
    '[爆炸R]',
    '[炸弹R]',
    '[火R]',
    '[啤酒R]',
    '[咖啡R]',
    '[钱袋R]',
    '[流汗R]',
    '[发R]',
    '[红包R]',
    '[福R]',
    '[鞭炮R]',
    '[庆祝R]',
    '[烟花R]',
    '[气球R]',
    '[看R]',
    '[新月R]',
    '[满月R]',
    '[大便R]',
    '[太阳R]',
    '[晚安R]',
    '[星R]',
    '[玫瑰R]',
    '[凋谢R]',
    '[郁金香R]',
    '[樱花R]',
    '[海豚R]',
    '[放大镜R]',
    '[刀R]',
    '[辣椒R]',
    '[黄瓜R]',
    '[葡萄R]',
    '[草莓R]',
    '[桃子R]',
    '[红薯R]',
    '[栗子R]',
    '[红色心形R]',
    '[黄色心形R]',
    '[绿色心形R]',
    '[蓝色心形R]',
    '[紫色心形R]',
    '[爱心R]',
    '[两颗心R]',
    '[浅肤色R]',
    '[中浅肤色R]',
    '[中等肤色R]',
    '[中深肤色R]',
    '[有R]',
    '[可R]',
    '[蹲R]',
    '[零R]',
    '[一R]',
    '[二R]',
    '[三R]',
    '[四R]',
    '[五R]',
    '[六R]',
    '[七R]',
    '[八R]',
    '[九R]',
    '[加一R]',
    '[满R]',
    '[禁R]',
  ];
  const emits = defineEmits(['select']);
  const config = computed(() => {
    switch (props.platform) {
      case '小红书':
        return {
          filePath: 'xiaohongshu-emoji',
          emojis: xiaohongshu,
        };
      case '抖音':
      default:
        return {
          filePath: 'douyin-emoji',
          emojis: douyin,
        };
    }
  });
  const visible = ref(false);
  function handleSelect(item: string) {
    emits('select', item);
    visible.value = false;
  }
</script>

<style scoped lang="less">
  .emoji-box {
    width: 512px;
    .msg-emoji {
      width: 24px;
      height: 24px;
      cursor: pointer;
      margin: 0px 4px;
    }
  }
</style>

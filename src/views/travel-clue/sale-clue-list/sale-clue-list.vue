<template>
  <div>
    <div class="table-card">
      <search-form-tile
        ref="searchFormRef"
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit"
      >
        <template #formItemGroup>
          <a-form-item label="线索ID">
            <a-input-search
              v-model="formModel.search"
              placeholder="请输入线索ID或客户名称"
              allow-clear
              class="w-300"
              @search="handleSubmit()"
              @keydown.enter="handleSubmit()"
            />
            <a-space>
              <a-button class="ml-10" type="primary" @click="handleSubmit()">
                <template #icon>
                  <icon-search />
                </template>
                搜索
              </a-button>
              <!-- 展开 -->
              <a-button type="text" @click="expandFn()">
                <template #icon>
                  <icon-filter />
                </template>
                <span v-if="expandFlag"> 收起 </span>
                <span v-else> 展开 </span>
              </a-button>
            </a-space>
          </a-form-item>
          <!-- 线索阶段 -->
          <a-form-item label="线索阶段">
            <dict-radio
              v-model="formModel.clue_stage"
              :data-list="clueStatusThreeM"
              show-all
              @change="handleSubmit()"
            />
          </a-form-item>
          <!-- 建联方式 -->
          <a-form-item label="建联方式">
            <dict-radio
              v-model="formModel.contact_way_type"
              :data-list="contactWayTypeM"
              show-all
              @change="handleSubmit()"
            />
          </a-form-item>
          <template v-if="expandFlag">
            <a-form-item label="产品">
              <request-radio
                v-model="formModel.thread_setting_id"
                api="product"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item label="线索来源媒体">
              <dict-radio
                v-model="formModel.platform"
                :data-list="contactWayListM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item v-if="isCollectSwitchOpen" label="线索来源">
              <dict-radio
                v-model="formModel.source"
                :data-list="clueSourceTypeM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item label="跟进状态">
              <dict-radio
                v-model="formModel.status"
                :data-list="clueStatusM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item label="留资状态">
              <dict-radio
                v-model="formModel.status_two"
                :data-list="clueStatusTwoM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
          </template>
        </template>
      </search-form-tile>
    </div>
    <a-card
      size="small"
      class="table-card"
      :body-style="{ minHeight: 'calc(100vh - 165px)' }"
    >
      <div class="table-card-header">
        <div> </div>
        <a-space>
          <columns-select
            :the-field-map="allFieldsConfig"
            :need-format-column="false"
            :send-params="{ type: 'travel_sale_clue' }"
            :default-columns="columnsConfig.map((item:any) => item.dataIndex)"
            @select-columns-ok="setColumns"
          ></columns-select>
          <!-- <a-button
            type="primary"
            @click="addRef?.show({ source: '接粉专员新增' })"
          >
            新增二级线索
          </a-button> -->
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :scroll-percent="scrollPercent"
        :send-params="tableParams"
        :auto-request="false"
      >
        <template #clue_stage="{ record }: TableColumnSlot">
          <status-badge
            :list="clueStatusThreeM"
            :value="record.clue_stage"
          ></status-badge>
        </template>
        <template #contact_way_type="{ record }: TableColumnSlot">
          <status-badge
            :list="contactWayTypeM"
            :value="record.contact_way_type"
          ></status-badge>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space size="mini" direction="vertical">
            <a-badge
              v-if="record.account_id"
              :count="
                dataCache.noReadIds.includes(record.thread_seas_id) ? 1 : 0
              "
              dot
            >
              <a-link @click="gotoChat(record)"> 查看沟通记录 </a-link>
            </a-badge>
            <a-popconfirm
              v-if="record.status === '已分配'"
              position="left"
              content="确定已跟进吗？"
              @ok="editStatus(record, '已跟进')"
            >
              <a-link> 已跟进 </a-link>
            </a-popconfirm>
            <a-popconfirm
              v-if="record.status === '已跟进'"
              position="left"
              content="确定已成交吗？"
              @ok="editStatus(record, '已成交')"
            >
              <a-link> 已成交 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
        <template #account_name="{ record }: TableColumnSlot">
          <template v-if="record.media_sec_uid">
            <a-link
              v-if="record.platform === '小红书'"
              :href="`https://www.xiaohongshu.com/user/profile/${record.media_sec_uid}`"
              target="_blank"
            >
              <icon-link class="mr-5" />
              {{ record.account_name }}
            </a-link>
            <a-link
              v-else
              :href="`https://www.douyin.com/user/${record.media_sec_uid}`"
              target="_blank"
            >
              <icon-link class="mr-5" />
              {{ record.account_name }}
            </a-link>
          </template>
          <span v-else>{{ record.account_name || '-' }}</span>
        </template>
        <template #source_account="{ record }: TableColumnSlot">
          <template v-if="record.from_user_id">
            <a-link
              v-if="record.platform === '小红书'"
              :href="`https://www.xiaohongshu.com/user/profile/${record.from_user_id}`"
              target="_blank"
            >
              <icon-link class="mr-5" />
              <span>{{ record.source_account }}</span>
            </a-link>
            <a-link
              v-else
              :href="`https://www.douyin.com/user/${record.from_user_id}`"
              target="_blank"
            >
              <icon-link class="mr-5" />
              {{ record.source_account }}
            </a-link>
          </template>
          <span v-else>{{ record.source_account || '-' }}</span>
        </template>
        <template #remark="{ record }: TableColumnSlot">
          <span style="white-space: pre-line">{{ record.remark }}</span>
        </template>
        <template #platform="{ record }: TableColumnSlot">
          <a-space align="center">
            <img
              width="20"
              :src="`/icons/platform/${record.platform}.png`"
              :alt="record.platform"
            />
            <span>{{ record.platform }}</span>
          </a-space>
        </template>
        <template #thread_content="{ record }: TableColumnSlot">
          <a-tooltip
            v-if="record.thread_content?.length > 50"
            :content="record.thread_content.slice(0, 400)"
          >
            <span>{{ record.thread_content.slice(0, 50) }}...</span>
          </a-tooltip>
          <span v-else>{{ record.thread_content }}</span>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <status-badge
            :list="clueStatusM"
            :value="record.status"
          ></status-badge>
        </template>
        <template #status_two="{ record }: TableColumnSlot">
          <status-badge
            :list="clueStatusTwoM"
            :value="record.status_two"
          ></status-badge>
        </template>
        <template #from_user_id="{ record }: TableColumnSlot">
          <a-tooltip :content="record.from_user_id">
            <div class="text-overflow">
              {{ record.from_user_id }}
            </div>
          </a-tooltip>
        </template>
      </base-table>
    </a-card>
    <sale-clue-detail ref="detailRef"></sale-clue-detail>
    <sale-clue-next
      ref="nextRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-next>
    <sale-clue-two-add
      ref="addRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-two-add>
    <sale-clue-cancellation
      ref="cancelRef"
      @refresh="theTable?.fetchData()"
    ></sale-clue-cancellation>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive, computed, onBeforeUnmount, markRaw } from 'vue';
  import request from '@/api/request';
  import { TableColumnSlot } from '@/global';
  import {
    clueSourceTypeM,
    clueStatusTwoM,
    clueStatusM,
    clueStatusThreeM,
    contactWayTypeM,
  } from '@/components/dict-select/dict-clue';
  import SaleClueDetail from '@/views/travel-clue/sale-clue-list/sale-clue-detail.vue';
  import SaleClueNext from '@/views/travel-clue/sale-clue-list/sale-clue-next.vue';
  import { useDataCacheStore, useAppStore, useUserStore } from '@/store';
  import SaleClueTwoAdd from '@/views/travel-clue/sale-clue-list-two/sale-clue-two-add.vue';
  // import requestSelect from '@/components/select/request-select.vue';
  import SaleClueCancellation from '@/views/travel-clue/sale-clue-list/sale-clue-cancellation.vue';
  import ColumnsSelect from '@/components/columns-select/columns-select.vue';
  import { cloneDeep } from 'lodash';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import { useRoute, useRouter } from 'vue-router';
  import RequestSelect from '@/components/select/request-select.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import SearchFormTile from '@/components/search-form-tile/search-form-tile.vue';
  import RequestRadio from '@/components/select/request-radio.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { getColor, getText } from '../../../components/dict-select/dict-util';

  const userStore = useUserStore();

  const emit = defineEmits(['goChat']);

  // 判断collect_switch是否open
  const isCollectSwitchOpen = computed(() => {
    return userStore.collect_switch === 'open';
  });

  const generateFormModel = () => {
    return {
      id: null as string | null,
      thread_seas_id: null,
      distribute_rule: null,
      status: null,
      status_two: null,
      source: null,
      platform: null,
      thread_setting_id: '',
      from_setting: null,
      time_distribute: null,
      service_user_fs_id: null,
      time_first_response: null,
      cancellation_time: null,
      clue_stage: null,
      contact_way_type: null,
      search: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const detailRef = ref();
  const nextRef = ref();
  const cancelRef = ref();
  const addRef = ref();
  const formModel = reactive(generateFormModel());
  const dataCacheStore = useDataCacheStore();

  const route = useRoute();
  formModel.id = (route.query.id as string) || null;

  const getList = async (data: any) => {
    return request('/api/thread/salesThreadList', {
      ...data,
    });
  };
  const allFieldsConfig = [
    {
      title: '全部字段',
      dataIndex: '全部字段',
      keys: [] as string[],
      dataList: [
        // 线索用户ID
        {
          title: '线索用户ID',
          dataIndex: 'account_id',
        },
        {
          title: '线索用户昵称',
          dataIndex: 'account_name',
        },
        {
          title: '产品',
          dataIndex: 'area',
        },
        {
          title: '线索来源媒体',
          dataIndex: 'platform',
          align: 'center',
        },
        {
          title: '线索来源类型',
          dataIndex: 'source',
        },
        // 线索来源内容账号
        {
          title: '线索来源内容账号',
          dataIndex: 'from_setting',
        },
        // 线索来源内容账号ID
        {
          title: '线索来源内容账号ID',
          dataIndex: 'from_account_id',
        },
        {
          title: '关键词',
          dataIndex: 'keyword',
        },
        {
          title: '评论内容',
          dataIndex: 'thread_content',
          width: 300,
        },
        {
          title: '评论时间',
          dataIndex: 'comment_time',
          align: 'center',
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          align: 'center',
        },
        {
          title: '微信',
          dataIndex: 'wechat',
        },
        {
          title: '线索来源内容链接',
          dataIndex: 'source_link',
        },
        // {
        //   title: '私信内容',
        //   dataIndex: 'send_msg_content',
        // },
        {
          title: '陌拜账号',
          dataIndex: 'first_send_account',
        },
        {
          title: '留资时间',
          dataIndex: 'time_into_two',
          defaultHide: true,
          align: 'center',
        },
        {
          title: '首次响应时间',
          dataIndex: 'time_first_response',
          defaultHide: true,
          align: 'center',
        },
        {
          title: '电销',
          dataIndex: 'two_service_user',
          align: 'center',
        },
        // {
        //   title: '账号昵称',
        //   dataIndex: 'source_account',
        // },
        {
          title: '成交时间',
          dataIndex: 'time_deal',
          defaultHide: true,
          align: 'center',
        },

        // 建联方式
        {
          title: '建联方式',
          dataIndex: 'contact_way_type',
          slotName: 'contact_way_type',
          align: 'center',
        },
        {
          title: '跟进状态',
          dataIndex: 'status',
          align: 'center',
        },

        // {
        //   title: '留资状态',
        //   dataIndex: 'status_two',
        //   align: 'center',
        //   fixed: 'right',
        //   width: 90,
        // },
      ],
    },
  ];
  allFieldsConfig.forEach((item) => {
    item.keys = item.dataList.map((citem) => {
      // @ts-ignore
      citem.key = citem.dataIndex;
      return citem.dataIndex;
    });
  });
  const columnsConfig = ref(
    cloneDeep(allFieldsConfig[0].dataList.filter((item) => !item.defaultHide))
  );
  const columns = computed(() => [
    {
      title: '线索ID',
      dataIndex: 'clue_uniq_id',
      width: 200,
      fixed: 'left',
    },
    ...columnsConfig.value,
    // 线索阶段
    {
      title: '线索阶段',
      dataIndex: 'clue_stage',
      slotName: 'clue_stage',
      align: 'center',
      width: 90,
      fixed: 'right',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      fixed: 'right',
      width: 140,
    },
  ]);
  const scrollPercent = computed(() => ({
    // x: columns.value.length * 180,
    // maxHeight: '70vh',
    x: 'max-content',
  }));

  const tableParams = computed(() => ({
    ...formModel,
  }));

  const router = useRouter();
  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    if (!formModel.id) {
      route.query.id = null;
      const newUrl = window.location.hash.replace(/\?.*$/, ''); // 获取当前路径，并去除参数
      window.history.replaceState({}, '', newUrl); // 更新浏览器历史记录，不触发页面重新加载
    }
    theTable.value?.search();
  };

  function setColumns(val: any[]) {
    columnsConfig.value = val.filter((item) =>
      allFieldsConfig.some((citem) => citem.keys.includes(item.dataIndex))
    );
    handleSubmit();
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }
  const dataCache = useDataCacheStore();

  function editStatus(record: any, status: string) {
    if (!record.loading) {
      record.loading = true;
      request('/api/thread/setSaleStatus', {
        id: record.id,
        status,
      }).then(() => {
        theTable.value?.fetchData();
      });
    }
  }

  const searchFormRef = ref();
  const resetHandler = () => {
    searchFormRef.value?.resetHandler();
  };

  const expandFlag = ref(false);
  const expandFn = () => {
    expandFlag.value = !expandFlag.value;
    if (!expandFlag.value) {
      searchFormRef.value?.resetHandler();
    }
  };

  function gotoChat(record: any) {
    // 1\先切换到线索管理页面
    // 2\再选中账号
    emit('goChat', record);
  }
</script>

<style scoped lang="less">
  .text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>

<template>
  <div class="clue-chat">
    <div class="chat-msg-box">
      <template v-if="msgList.length">
        <div
          v-for="(item, index) in msgList"
          :key="index"
          :class="{
            'chat-msg-item': true,
            'chat-msg-item-right': item.msg_show === 'right',
          }"
        >
          <div v-if="item.msg_show === 'right'" class="chat-msg-time">
            <div>
              <div>{{ item.send_user }} </div>
              <div>{{ item.send_time }}</div>
            </div>
            <a-avatar :size="34">{{ item.send_user?.slice(0, 2) }}</a-avatar>
          </div>
          <div v-else class="chat-msg-time">
            <a-avatar :size="34">{{ item.send_user?.slice(0, 2) }}</a-avatar>
            <div>
              <div>{{ item.send_user }} </div>
              <div>{{ item.send_time }}</div>
            </div>
          </div>
          <div v-if="item.message_type === 'image'" class="chat-msg">
            <div v-if="item.msg_show === 'right'" class="msg-status">
              <icon-loading
                v-if="item.send_status === 1"
                style="color: #165dff"
                size="10"
              />
              <icon-check
                v-else-if="item.send_status === 3"
                style="color: #00b42a"
                size="10"
              />
              <icon-close
                v-else-if="item.send_status === 5"
                style="color: #f53f3f"
                size="10"
              />
              <span
                class="status-text"
                :style="{
                  color:
                    item.send_status === 1
                      ? '#165DFF'
                      : item.send_status === 3
                      ? '#00b42a'
                      : '#f53f3f',
                }"
              >
                {{
                  item.send_status === 1
                    ? '发送中'
                    : item.send_status === 3
                    ? '已发送'
                    : '发送失败'
                }}
              </span>
            </div>
            <img
              class="msg-img"
              :src="item.message"
              alt=""
              @click="
                $previewPlayer({
                  mediaList: [
                    {
                      url: item.message,
                      title: item.message.split('/').pop(),
                      extra: item,
                    },
                  ],
                })
              "
            />
          </div>
          <div v-else class="chat-msg">
            <div v-if="item.msg_show === 'right'" class="msg-status">
              <icon-loading
                v-if="item.send_status === 1"
                style="color: #165dff"
                size="10"
              />
              <icon-check
                v-else-if="item.send_status === 3"
                style="color: #00b42a"
                size="10"
              />
              <icon-close
                v-else-if="item.send_status === 5"
                style="color: #f53f3f"
                size="10"
              />
              <span
                class="status-text"
                :style="{
                  color:
                    item.send_status === 1
                      ? '#165DFF'
                      : item.send_status === 3
                      ? '#00b42a'
                      : '#f53f3f',
                }"
              >
                {{
                  item.send_status === 1
                    ? '发送中'
                    : item.send_status === 3
                    ? '已发送'
                    : '发送失败'
                }}
              </span>
            </div>
            <span v-html="getMessage(item.message || '')"> </span>
          </div>
        </div>
      </template>
      <a-empty v-else description="暂无消息" />
    </div>
    <div v-if="!noChat" class="ipt-box">
      <a-input-group class="w100p">
        <a-input
          ref="inputRef"
          v-model="msgTxt"
          allow-clear
          placeholder="请输入内容"
          @keyup.enter="sendMsg"
        />
        <a-upload
          action="/api/uploadFile"
          :show-file-list="false"
          :data="{ type: 'send_msg_image' }"
          accept="image/*"
          class="upload-btn"
          @change="uploadChange"
          @success="uploadSuccess"
        >
          <template #upload-button>
            <a-button
              :loading="uploading"
              style="border-radius: 0"
              type="outline"
              @click="sendMsg"
            >
              <template #icon>
                <icon-image />
              </template>
            </a-button>
          </template>
        </a-upload>
        <chat-emoji
          class="emoji-btn"
          :platform="sendParams.platform"
          @select="selectEmoji"
        >
          <a-button type="outline" class="emoji-btn">
            <template #icon>
              <icon-face-smile-fill />
            </template>
          </a-button>
        </chat-emoji>
        <a-button type="primary" class="send-btn" @click="sendMsg">
          <template #icon>
            <icon-send />
          </template>
        </a-button>
      </a-input-group>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, onBeforeUnmount, ref } from 'vue';
  import request from '@/api/request';
  import { FileItem, Message } from '@arco-design/web-vue';
  import ChatEmoji from '@/views/travel-clue/sale-clue-list/chat-emoji.vue';
  import { setCaretPosition } from '@/utils/util';
  import dayjs from 'dayjs';
  import { isLogin } from '@/utils/auth';

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    noChat: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => ({}),
    },
  });
  const msgTxt = ref('');
  const inputRef = ref();
  const msgList = ref<any[]>([]);

  const xiaohongshu = computed(() => props.sendParams.platform === '小红书');

  function getMsgList() {
    if (isLogin()) {
      request('/api/thread/getChatList', {
        ...props.sendParams,
      }).then((res) => {
        msgList.value = res.data || [];
      });
    }
  }
  let timer = setInterval(() => {
    if (window.location.hostname !== 'localhost') {
      getMsgList();
    }
  }, 5000);
  getMsgList();
  onBeforeUnmount(() => {
    clearInterval(timer);
  });

  function uploadSuccess(file: FileItem) {
    if (file.status === 'done' && file.response?.code === 0) {
      const newMsg = {
        message: file.response?.data?.url,
        msg_show: 'right',
        message_type: 'image',
        send_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        send_status: 1, // 发送中
        send_user: props.info.from_setting || '用户',
      };
      msgList.value.push(newMsg);
      nextTick(() => {
        document.querySelector('.chat-msg-box')?.scrollTo({ top: 10000 });
      });
      request('/api/thread/sendDYChatV2', {
        ...props.sendParams,
        image_url: file.response?.data?.file_path,
        type: 'image',
      })
        .then((res) => {
          if (res.code === 0) {
            // newMsg.send_status = 3; // 发送成功
          } else {
            newMsg.send_status = 5; // 发送失败
            Message.error(res.msg || '发送失败');
          }
        })
        .catch(() => {
          newMsg.send_status = 5; // 发送失败
          Message.error('发送失败');
        });
    } else if (file.status === 'error') {
      Message.error(file.response?.data?.msg || '上传失败');
    }
  }

  const uploading = ref(false);
  function uploadChange(fileList: FileItem[], fileItem: FileItem) {
    switch (fileItem.status) {
      case 'init':
        uploading.value = true;
        break;
      case 'done':
      case 'error':
        uploading.value = false;
        break;
      default:
        break;
    }
  }

  function sendMsg() {
    if (msgTxt.value) {
      const newMsg = {
        message: msgTxt.value,
        msg_show: 'right',
        send_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        send_status: 1, // 发送中
        send_user: props.info.from_setting || '用户',
      };
      msgList.value.push(newMsg);
      nextTick(() => {
        document.querySelector('.chat-msg-box')?.scrollTo({ top: 10000 });
      });
      request('/api/thread/sendDYChatV2', {
        ...props.sendParams,
        message: msgTxt.value,
      })
        .then((res) => {
          if (res.code === 0) {
            // newMsg.send_status = 3; // 发送成功
          } else {
            newMsg.send_status = 5; // 发送失败
            Message.error(res.msg || '发送失败');
          }
        })
        .catch(() => {
          newMsg.send_status = 5; // 发送失败
          Message.error('发送失败');
        });
      msgTxt.value = '';
    }
  }

  function getMessage(msg = '') {
    let reg = /\[(.+?)\]/gi;
    let emojis = msg.match(reg);
    emojis = Array.from(new Set(emojis));
    emojis?.forEach((item) => {
      if (item === '[表情]') {
        msg = item;
      } else {
        msg = msg.replaceAll(
          item,
          `<img src="/${
            xiaohongshu.value ? 'xiaohongshu-emoji' : 'douyin-emoji'
          }/${item}.png" style="width: 20px;height:20px;margin: 0;vertical-align: middle;margin: 0 1px 2px;" />`
        );
      }
    });
    return msg;
  }

  function selectEmoji(msg: string) {
    let iptDom = inputRef.value.inputRef;
    let newName: string = msgTxt.value;
    let index = iptDom.selectionStart || newName?.length || 0;
    if (!newName) {
      newName = `${msg}`;
    } else {
      newName = `${newName.slice(0, index)}${msg}${newName.slice(index)}`;
    }
    msgTxt.value = newName;
    nextTick(() => {
      iptDom?.focus();
      const len = msg.length + index;
      setCaretPosition(iptDom, len);
    });
  }
</script>

<style scoped lang="less">
  .clue-chat {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .chat-msg-box {
    overflow-y: auto;
    padding: 10px;
    padding-bottom: 0;
    flex: 1;
    .chat-msg-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
    .chat-msg-item-right {
      align-items: flex-end;
      :deep(.arco-avatar) {
        background: #353845;
        margin: 0;
        margin-left: 4px;
      }
      .chat-msg {
        background: #353845;
        min-width: 0;
        margin-left: 0px;
        margin-right: 38px;
      }
      .chat-msg-time {
        text-align: right;
      }
    }
    .chat-msg-time {
      font-size: 12px;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
    }
    :deep(.arco-avatar) {
      background: #936d56;
      margin-right: 4px;
    }
    .chat-msg {
      position: relative;
      background: #936d56;
      padding: 6px 10px;
      margin-left: 38px;
      border-radius: 6px;
      max-width: 66%;
      min-width: 20px;
      box-sizing: content-box;
      color: #fff;
      margin-bottom: 10px;
      word-break: break-all;
      line-height: 1.5;
      .msg-img {
        width: 200px;
      }
      .msg-status {
        position: absolute;
        left: -55px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 2px;
        white-space: nowrap;
        width: 50px;
        .status-text {
          font-size: 10px;
        }
      }
    }
  }
  .ipt-box {
    margin: 10px;
    background: #fff;
    .upload-btn {
      border-radius: 0;
      // margin-right: 5px;
      border-right: none;
    }
    .emoji-btn {
      border-radius: 0;
      // margin-right: 5px;
    }
    .send-btn {
      border-radius: 0;
    }
  }
</style>

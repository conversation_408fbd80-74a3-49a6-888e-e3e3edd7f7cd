<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="作废线索"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="460px"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" class="mt-10" :model="formModel" auto-label-width>
      <a-form-item label="线索ID">
        <a-input v-model="formModel.id" disabled />
      </a-form-item>
      <a-form-item
        label="作废原因"
        field="cancellation_reason"
        :rules="requiredRule"
      >
        <a-textarea
          v-model="formModel.cancellation_reason"
          :auto-size="{ minRows: 2, maxRows: 6 }"
        />
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import DModal from '@/components/d-modal/d-modal.vue';
  import { requiredRule } from '@/utils/util';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import RequestSelect from '@/components/select/request-select.vue';

  const emit = defineEmits(['refresh']);
  const formRef = ref<FormInstance>();

  const generateFormModel = () => {
    return {
      id: '',
      cancellation_reason: '',
    };
  };
  const formModel = ref(generateFormModel());
  const info = ref({});

  const visible = ref(false);
  const okLoading = ref(false);
  const show = (data: any = {}) => {
    if (data) {
      let initForm = generateFormModel();
      formModel.value = initForm;
      if (data) {
        Object.keys(initForm).forEach((key) => {
          // @ts-ignore
          formModel.value[key] =
            data[key] || initForm[key as keyof typeof initForm];
        });
      }
      info.value = data;
      visible.value = true;
      okLoading.value = false;
    }
  };

  const handleCancel = () => {
    visible.value = false;
    formModel.value = generateFormModel();
    info.value = {};
    formRef.value?.clearValidate();
  };
  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      okLoading.value = true;
      request('/api/thread/nullifyThread', {
        ...formModel.value,
      })
        .then(() => {
          emit('refresh');
          handleCancel();
        })
        .finally(() => {
          okLoading.value = false;
        });
    }
  };
  defineExpose({
    show,
  });
</script>

<style lang="less"></style>

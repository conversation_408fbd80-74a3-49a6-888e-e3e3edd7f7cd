<template>
  <div class="table-card">
    <a-card>
      <div class="df jc-sb ai-cen mb-10">
        <a-space>
          <a-button type="primary" @click="getList()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <request-select
            v-model="formModel.model_id"
            placeholder="请选择产品"
            class="w-300"
            api="product"
            value-key="ai_service_model_id"
            @change="handleSubmit()"
          />
        </a-space>
        <a-space>
          <a-button type="primary" @click="uploadRef?.show()">
            <template #icon><icon-upload /></template>
            <span>上传文档</span>
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <a-table
        :loading="loading"
        :columns="columns"
        :data="list"
        :scroll="{ y: 'calc(100vh - 223px)' }"
        @page-change="(val:number)=>{pagination.current=val;getList()}"
        @page-size-change="(val:number)=>{pagination.pageSize=val;getList()}"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="saveRef?.show(record)"> <icon-edit />编辑 </a-link>
          </a-space>
        </template>
      </a-table>
    </a-card>
    <qa-upload-modal ref="uploadRef" @refresh="handleSubmit()" />
    <qa-save-modal ref="saveRef" @refresh="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { TableColumnSlot } from '@/global';
  import RequestSelect from '@/components/select/request-select.vue';
  import { pageConfig } from '@/utils/table-utils/table-util';
  import QaUploadModal from '@/views/travel-clue/ai-service/qa-upload-modal.vue';
  import QaSaveModal from '@/views/travel-clue/ai-service/qa-save-modal.vue';

  const generateFormModel = () => {
    return {
      model_id: null,
    };
  };
  const loading = ref(false);
  const uploadRef = ref();
  const saveRef = ref();
  const formModel = reactive(generateFormModel());

  const list = ref<any[]>([]);
  const pagination = ref(pageConfig());

  const getList = async () => {
    loading.value = true;
    request('/api/qa/doc/list', {
      ...formModel,
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })
      .then((res) => {
        list.value = res.data.docs;
        pagination.value.total = res.data.page_info.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };
  getList();

  const columns = [
    {
      title: '产品',
      dataIndex: 'product.name',
    },
    {
      title: '文档名称',
      dataIndex: 'doc_name',
    },
    // {
    //  title: '创建时间',
    //  dataIndex: 'add_time',
    // },
    // {
    //  title: '更新时间',
    //  dataIndex: 'completion_time',
    // },
    {
      title: '操作',
      dataIndex: 'action',
      slotName: 'action',
      align: 'center',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    pagination.value.current = 1;
    getList();
  };
</script>

<style scoped lang="less"></style>

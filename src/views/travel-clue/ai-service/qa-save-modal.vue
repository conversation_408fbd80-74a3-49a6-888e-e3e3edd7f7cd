<template>
  <d-modal
    v-model:visible="visible"
    :mask-closable="false"
    width="1000px"
    :footer="false"
    :ok-loading="okLoading"
    title="编辑文档"
    @cancel="handleCancel"
  >
    <div class="df jc-sb mb-10 ai-cen">
      <a-input-search
        v-model="keyword"
        class="w-200"
        allow-clear
        placeholder="请输入关键词"
        @keydown.enter="resetSearch"
        @search="resetSearch"
      ></a-input-search>
      <a-button type="primary" status="success" @click="addNewItem">
        <template #icon>
          <icon-plus />
        </template>
        新建
      </a-button>
    </div>
    <a-table
      :loading="loading"
      :data="formModel.commentArr"
      :pagination="pagination"
      :scroll="{ y: '60vh' }"
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange"
    >
      <template #columns>
        <a-table-column title="问题">
          <template #cell="{ record }: TableColumnSlot">
            <a-textarea
              v-if="record.is_editing"
              v-model="record.question"
              placeholder="请输入"
              :auto-size="{ minRows: 4, maxRows: 6 }"
            />
            <div v-else>
              {{ record.question || '-' }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="答案">
          <template #cell="{ record }: TableColumnSlot">
            <a-textarea
              v-if="record.is_editing"
              v-model="record.answer"
              :auto-size="{ minRows: 4, maxRows: 6 }"
              placeholder="请输入"
            />
            <div v-else>
              {{ record.answer || '-' }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="180" align="center">
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <a-space>
              <template v-if="!record.is_editing">
                <a-link @click="record.is_editing = true">
                  <template #icon>
                    <icon-edit />
                  </template>
                  编辑
                </a-link>
                <a-link
                  v-if="!record.is_editing"
                  status="danger"
                  @click="delItem(record)"
                >
                  <template #icon>
                    <icon-delete />
                  </template>
                  删除
                </a-link>
              </template>

              <template v-else>
                <a-link @click="updateItem(record)">
                  <template #icon>
                    <icon-check />
                  </template>
                  保存
                </a-link>
                <a-link status="danger" @click="cancelItem(record, rowIndex)">
                  <template #icon>
                    <icon-close />
                  </template>
                  取消
                </a-link>
              </template>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </d-modal>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { pageConfig } from '@/utils/table-utils/table-util';
  import request from '@/api/request';
  import DModal from '@/components/d-modal/d-modal.vue';
  import { cloneDeep } from 'lodash';

  const emit = defineEmits(['refresh']);

  const generateFormModel = () => {
    return {
      id: null,
      model_id: null,
      commentArr: [],
    };
  };
  let formModel: any = ref(generateFormModel());
  const visible = ref(false);
  const okLoading = ref(false);
  const loading = ref(false);

  // 生成分页参数
  let pagination = reactive(pageConfig({ pageSize: 10 }));

  // 查询详情
  const keyword: any = ref('');
  function getPromptItemsFn() {
    loading.value = true;
    request('/api/qa/info/list', {
      page: pagination.current,
      pageSize: pagination.pageSize,
      model_id: formModel.value.model_id,
      doc_id: formModel.value.id,
      keyword: keyword.value,
    }).then((res) => {
      formModel.value.commentArr =
        res.data.info_list.map((item: any) => ({
          ...item,
          is_editing: false,
          origin_info: cloneDeep(item),
        })) || [];
      pagination.current = res.data.page_info.current_page;
      pagination.total = res.data.page_info.total;
      loading.value = false;
    });
  }

  function cancelItem(item: any, rowIndex: number) {
    if (item.id) {
      Object.assign(item, cloneDeep(item.origin_info), { is_editing: false });
    } else {
      formModel.value.commentArr.splice(rowIndex, 1);
    }
  }

  function updateItem(item: any) {
    if (!item.question && !item.answer) {
      Message.warning('请填写完整信息！');
      return;
    }
    loading.value = true;
    request(item.id ? '/api/qa/info/update' : ' /api/qa/info/add', {
      doc_id: formModel.value.id,
      model_id: formModel.value.model_id,
      info_id: item.id,
      answer: item.answer,
      question: item.question,
    })
      .then((res) => {
        item.is_editing = false;
        getPromptItemsFn();
      })
      .catch((err) => {
        item.is_editing = false;
        console.log(err);
      });
  }

  function addNewItem(key: any) {
    loading.value = true;
    formModel.value.commentArr.unshift({
      id: null,
      model_id: formModel.value.model_id,
      answer: '',
      question: '',
      is_editing: true,
    });
    loading.value = false;
  }
  const delItem = (item: any) => {
    loading.value = true;
    request('/api/qa/info/delete', {
      model_id: formModel.value.model_id,
      info_id: item.id,
      doc_id: formModel.value.id,
    })
      .then((res) => {
        getPromptItemsFn();
      })
      .catch((err) => {
        loading.value = false;
        console.log(err);
      });
  };

  function resetSearch() {
    pagination.current = 1;
    getPromptItemsFn();
  }

  const show = (data: any = {}) => {
    visible.value = true;
    keyword.value = null;
    formModel.value = generateFormModel();
    formModel.value.id = data.id;
    formModel.value.model_id = data.model_id;
    getPromptItemsFn();
    okLoading.value = false;
  };

  // 4.监听页码更换
  const onPageChange = (current: number) => {
    pagination.current = current;
    getPromptItemsFn();
  };
  // 5.页码大小变化
  const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    getPromptItemsFn();
  };

  const handleCancel = () => {
    visible.value = false;
  };
  defineExpose({
    show,
  });
</script>

<style lang="less">
  .pagination-box {
    display: flex;
    justify-content: flex-end;
  }
</style>

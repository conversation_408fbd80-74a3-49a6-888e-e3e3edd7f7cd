<template>
  <a-modal
    :visible="visible"
    :simple="false"
    :mask-closable="false"
    :ok-loading="okLoading"
    width="420px"
    title="上传文档"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <template #title> 上传文档</template>
    <a-form ref="formRef" :model="formModel" auto-label-width layout="vertical">
      <a-form-item field="model_id" label="产品" :rules="requiredRule">
        <request-select
          v-model="formModel.model_id"
          select-first
          value-key="ai_service_model_id"
          api="product"
        />
      </a-form-item>
      <a-form-item field="fileList" :rules="requiredUploadRuleArr">
        <template #label>
          <span>文件</span>
          <a-button
            type="primary"
            size="mini"
            class="ml-10"
            @click="downloadLinkFile('excel-template/AI语料模板.xlsx')"
          >
            <template #icon>
              <icon-download />
            </template>
            下载模板文件
          </a-button>
        </template>
        <a-upload
          v-model:file-list="formModel.fileList"
          tip="支持excel，csv格式文件"
          accept=".xls,.xlsx,.csv"
          :multiple="false"
          draggable
          :auto-upload="false"
          list-type="list-card"
          @change="(fileList: any, fileItem: FileItem) => {
            fileItem.status = 'done';
          }"
        ></a-upload>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { FileItem, Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import {
    requiredRule,
    requiredUploadRule,
    requiredUploadRuleArr,
  } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';
  import { downloadLinkFile } from '@/utils/table-utils/table-util';

  const emit = defineEmits(['update:modelValue', 'refresh']);
  const formRef = ref<FormInstance>();

  const generateFormModel = () => {
    return {
      model_id: '',
      fileList: [] as FileItem[],
    };
  };
  const formModel = ref(generateFormModel());

  const visible = ref(false);
  const okLoading = ref(false);

  const show = () => {
    visible.value = true;
    okLoading.value = false;
  };

  const handleCancel = () => {
    formModel.value = generateFormModel();
    visible.value = false;
  };
  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      try {
        okLoading.value = true;
        const data = new FormData();
        let { file } = formModel.value.fileList[0];
        data.append('file', file as any);
        data.append('model_id', formModel.value.model_id);
        request('/api/qa/doc/upload', data)
          .then(() => {
            Message.success('上传成功');
            handleCancel();
            emit('refresh');
          })
          .finally(() => {
            okLoading.value = false;
          });
      } catch (err) {
        console.log(err);
      }
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

<template>
  <d-modal
    v-model:visible="visible"
    width="800px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}产品角色设定`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <!--    <a-form-item label="名称" field="area" :rules="requiredRule">
        <a-input v-model="formModel.area" readonly />
      </a-form-item>-->
      <a-form-item label="">
        <div class="w100p">
          <template
            v-for="(item, index) in formModel.ai_service_prompt"
            :key="index"
          >
            <template v-if="index === 2">
              <a-textarea v-model="item.c_prompt" :auto-size="{ minRows: 4 }" />
              <!--<template #actions>
                <a-space direction="vertical" class="ml-10">
                  <a-button
                    type="primary"
                    size="mini"
                    @click="formModel.ai_prompt_video.splice(index + 1, 0, {})"
                  >
                    <template #icon>
                      <icon-plus />
                    </template>
                  </a-button>
                  <a-button
                    type="primary"
                    status="danger"
                    size="mini"
                    :disabled="formModel.ai_prompt_video.length < 2"
                    @click="formModel.ai_prompt_video.splice(index, 1)"
                  >
                    <template #icon>
                      <icon-delete />
                    </template>
                  </a-button>
                </a-space>
              </template>-->
            </template>
          </template>
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    requiredRule,
    requiredRuleArr,
    requiredRuleArrIpt,
  } from '@/utils/util';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import { cloneDeep } from 'lodash';

  const defaultForm = () => ({
    id: null,
    ai_service_prompt: [] as any[],
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };
  const show = (dinfo: any) => {
    formModel.value = defaultForm();
    Object.assign(formModel.value, cloneDeep(dinfo));
    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      if (
        !formModel.value.ai_service_prompt.filter(
          (item) => item.c_prompt?.trim() || ''
        ).length
      ) {
        return Message.error('请填写Prompt');
      }
      loading.value = true;
      request('/api/thread/baseSettingSave', {
        ...formModel.value,
        ai_service_model_id: undefined,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

<template>
  <a-tabs lazy-load>
    <a-tab-pane key="语料配置" title="语料配置">
      <qa-config />
    </a-tab-pane>
    <a-tab-pane key="角色设定" title="角色设定">
      <role-config />
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  import QaConfig from '@/views/travel-clue/ai-service/qa-config.vue';
  import RoleConfig from '@/views/travel-clue/ai-service/role-config.vue';
</script>

<style scoped lang="less">
  :deep(.arco-tabs-content) {
    padding: 0;
  }
</style>

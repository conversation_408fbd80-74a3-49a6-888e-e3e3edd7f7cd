<template>
  <a-card class="mt-10" :body-style="{ minHeight: 'calc(100vh - 145px)' }">
    <template v-if="list.length">
      <a-row :gutter="[10, 10]">
        <a-col v-for="item in list" :key="item.id" :span="6">
          <a-card class="card-item">
            <template #title>
              <span class="vertical-title">{{ item.area }}</span>
            </template>
            <template #extra>
              <a-link @click="showEditFn(item)">
                <template #icon> <icon-edit /> 编辑 </template>
              </a-link>
            </template>
            <div
              style="
                height: 30vh;
                overflow: auto;
                white-space: pre-line;
                line-height: 1.6;
              "
            >
              {{ item.ai_service_prompt?.[2]?.c_prompt || '无' }}
            </div>
          </a-card>
        </a-col>
      </a-row>
      <div class="df jc-sb mt-10">
        <div></div>
        <a-pagination
          v-bind="pagination"
          @change="(val:number)=>{pagination.current=val;getList()}"
          @page-size-change="(val:number)=>{pagination.pageSize=val;getList()}"
        ></a-pagination>
      </div>
    </template>
    <div v-else style="min-height: 600px" class="df ai-cen">
      <a-empty />
    </div>
    <role-config-edit ref="editRef" @save="handleSubmit()" />
    <long-text-modal ref="textRef" title="Prompt" />
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import LongTextModal from '@/components/LongTextModal.vue';
  import RoleConfigEdit from '@/views/travel-clue/ai-service/role-config-edit.vue';
  import { pageConfig } from '@/utils/table-utils/table-util';

  const pagination = reactive(pageConfig({ pageSize: 8, showPageSize: false }));
  const loading = ref(false);
  const list = ref<any[]>([]);

  const getList = async () => {
    loading.value = true;
    request('/api/threadSetting/list', {
      page: pagination.current,
      pageSize: pagination.pageSize,
    })
      .then((res) => {
        list.value = res.data.data || [];
        pagination.total = res.data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const editRef = ref();
  const textRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }
  const handleSubmit = () => {
    pagination.current = 1;
    getList();
  };
  handleSubmit();
</script>

<style scoped lang="less">
  .card-item {
    border: 1px solid var(--color-neutral-4);
    &:hover {
      border: 1px solid rgb(var(--arcoblue-4));
    }
  }
</style>

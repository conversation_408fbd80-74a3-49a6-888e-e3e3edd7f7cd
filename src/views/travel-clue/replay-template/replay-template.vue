<template>
  <a-card>
    <div class="jc-sb">
      <a-space>
        <a-button type="primary" @click="tableRef?.fetchData()">
          <template #icon>
            <icon-refresh />
          </template>
          <span>刷新</span>
        </a-button>
      </a-space>
      <a-space>
        <a-button type="primary" status="success" @click="showEdit()">
          <template #icon>
            <icon-plus />
          </template>
          新增
        </a-button>
      </a-space>
    </div>
    <base-table
      ref="tableRef"
      v-model:loading="loading"
      class="mt-10"
      :columns-config="columns"
      :data-config="dataConfig"
      no-pagination
    >
      <template
        #thread_base_setting_keywords_names="{ record }: TableColumnSlot"
      >
        <template v-if="record.thread_base_setting_keywords_names?.length">
          <component
            :is="
              record.thread_base_setting_keywords_names.length > 2
                ? Popover
                : 'div'
            "
            :content-style="{
              maxWidth: '420px',
              minWidth: '200px',
              maxHeight: '200px',
              overflow: 'auto',
            }"
          >
            <template #content>
              <a-space direction="vertical">
                <div
                  v-for="(
                    item, index
                  ) in record.thread_base_setting_keywords_names"
                  :key="index"
                >
                  {{ item?.account_name || item }}
                </div>
              </a-space>
            </template>
            <a-space wrap size="mini">
              <a-tag
                v-for="item in record.thread_base_setting_keywords_names.slice(
                  0,
                  2
                )"
                :key="item"
                class="text-overflow"
                color="blue"
              >
                {{ item?.account_name || item }}
              </a-tag>
              <a-tag
                v-if="record.thread_base_setting_keywords_names.length > 2"
                color="blue"
              >
                等共{{ record.thread_base_setting_keywords_names.length }}个
              </a-tag>
            </a-space>
          </component>
        </template>
        <span v-else>-</span>
      </template>
      <template #action="{ record }: TableColumnSlot">
        <a-space>
          <a-link @click="showEdit(record)"> <icon-edit /> 编辑 </a-link>
          <a-link @click="goStatistics(record)">
            <icon-find-replace /> 统计
          </a-link>
        </a-space>
      </template>
      <template #reply_template_thread_seas="{ record }: TableColumnSlot">
        <div>
          <a-tooltip content="使用AI润色功能">
            <span
              class="a-text mr-5"
              @click="
                aiKeywordsParams.reply_template_id = record.id;
                aiKeywordsParams.template_type = 'thread_seas';
                aiRef?.show();
              "
            >
              <icon-font type="icon-AiLab" size="16" />
            </span>
          </a-tooltip>
          <span>{{ record.reply_template_thread_seas }}</span>
        </div>
      </template>
      <template #reply_template_one_thread="{ record }: TableColumnSlot">
        <div>
          <a-tooltip content="使用AI润色功能">
            <span
              class="a-text mr-5"
              @click="
                aiKeywordsParams.reply_template_id = record.id;
                aiKeywordsParams.template_type = 'one_thread';
                aiRef?.show();
              "
            >
              <icon-font type="icon-AiLab" size="16" />
            </span>
          </a-tooltip>
          <span>{{ record.reply_template_one_thread }}</span>
        </div>
      </template>
      <template #reply_template_get_contact="{ record }: TableColumnSlot">
        <div>
          <a-tooltip content="使用AI润色功能">
            <span
              class="a-text mr-5"
              @click="
                aiKeywordsParams.reply_template_id = record.id;
                aiKeywordsParams.template_type = 'get_contact';
                aiRef?.show();
              "
            >
              <icon-font type="icon-AiLab" size="16" />
            </span>
          </a-tooltip>
          <span>{{ record.reply_template_get_contact }}</span>
        </div>
      </template>
      <template #reply_template_rescue_order="{ record }: TableColumnSlot">
        <div>
          <a-tooltip content="使用AI润色功能">
            <span
              class="a-text mr-5"
              @click="
                aiKeywordsParams.reply_template_id = record.id;
                aiKeywordsParams.template_type = 'rescue_order';
                aiRef?.show();
              "
            >
              <icon-font type="icon-AiLab" size="16" />
            </span>
          </a-tooltip>
          <span>{{ record.reply_template_rescue_order }}</span>
        </div>
      </template>
      <template #imgs="{ record }: TableColumnSlot">
        <div>
          <a-link
            @click="imgRef?.show({ ...record, reply_template_id: record.id })"
          >
            配置
          </a-link>
        </div>
      </template>
    </base-table>
    <replay-template-ai-modal ref="aiRef" :send-params="aiKeywordsParams" />
    <replay-template-save
      ref="saveRef"
      :active-label="activeLabel"
      @save="refreshData()"
    />
  </a-card>
</template>

<script setup lang="ts">
  import request from '@/api/request';
  import { computed, ref } from 'vue';
  import { Message, Popover } from '@arco-design/web-vue';
  import { useAppStore, useUserStore } from '@/store';
  import router from '@/router';
  import ReplayTemplateAiModal from './replay-template-ai-modal.vue';
  import ReplayTemplateSave from './replay-template-save.vue';

  const userStore = useUserStore();
  // 判断collect_switch是否open
  const isCollectSwitchOpen = computed(() => {
    return userStore.collect_switch === 'open';
  });
  // 根据是否开启 动态更换title
  const activeLabel = computed(() => {
    return isCollectSwitchOpen.value ? '适用分组' : '适用账号';
  });

  const columns = computed(() => [
    {
      title: '模板名称',
      dataIndex: 'template_name',
      width: 150,
      fixed: 'left',
    },
    {
      title: activeLabel,
      dataIndex: 'thread_base_setting_keywords_names',
      width: 200,
    },
    {
      title: '陌拜私信模板',
      dataIndex: 'reply_template_thread_seas',
      description: '系统主动给陌生用户发送的私信消息模板',
    },
    {
      title: '意向回复模板',
      dataIndex: 'reply_template_one_thread',
      description: '陌拜触达用户后，用户回复消息，系统继续回复的消息模板',
    },
    {
      title: '留资结束语模板',
      dataIndex: 'reply_template_get_contact',
      description: '用户回复联系方式(手机号或微信号）后，系统回复的结束语模板',
    },
    {
      title: '挽单模板',
      dataIndex: 'reply_template_rescue_order',
      description:
        '如用户24h内不回复，为提升留资率，系统主动再次发送的消息模板',
    },
    {
      title: '询问联系方式模版',
      dataIndex: 'reply_template_ask_contact',
    },
    // { title: '图片配置', dataIndex: 'imgs' },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      align: 'center',
    },
  ]);
  const tableRef = ref();
  const saveRef = ref();
  const aiRef = ref();
  const imgRef = ref();
  const loading = ref(false);

  const aiKeywordsParams = ref<any>({});

  function dataConfig(params: any) {
    return request('/api/thread/replyTemplateList', params);
  }

  function refreshData() {
    tableRef.value?.search();
  }

  function showEdit(record?: any) {
    saveRef.value?.show(record);
  }

  function goStatistics(record: any) {
    router.push({
      name: 'replay-template-total',
      query: {
        id: record.id,
      },
    });
  }
</script>

<style scoped lang="less">
  .text-overflow {
    display: inline-block;
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>

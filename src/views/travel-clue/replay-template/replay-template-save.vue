<template>
  <a-modal
    :visible="visible"
    width="600px"
    :title="`${formModel.id ? '编辑' : '新增'}回复模板`"
    :body-style="{ maxHeight: '80vh' }"
    :ok-loading="loading"
    @ok="sendInfo"
    @cancel="visible = false"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item label="模版名称" :rules="requiredRule" field="template_name">
        <a-input
          v-model="formModel.template_name"
          placeholder="请输入模版名称"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        :label="activeLabel"
        :rules="requiredRuleArr"
        field="thread_base_setting_keywords_ids"
      >
        <!-- <request-select
          v-model="formModel.thread_base_setting_keywords_ids"
          request-url="/api/thread/getBaseSettingKeywords"
          label-key="title"
          allow-clear
        /> -->
        <PopoverCheckGroup
          v-model="formModel.thread_base_setting_keywords_ids"
          :active-label="activeLabel"
        />
      </a-form-item>

      <a-form-item label="陌拜私信模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="threadSeasRef"
            v-model="formModel.reply_template_thread_seas"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3, maxRows: 8 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  threadSeasRef,
                  'reply_template_thread_seas',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="意向回复模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="oneThreadRef"
            v-model="formModel.reply_template_one_thread"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3, maxRows: 8 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  oneThreadRef,
                  'reply_template_one_thread',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="留资结束语模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="getContactRef"
            v-model="formModel.reply_template_get_contact"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3, maxRows: 8 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  getContactRef,
                  'reply_template_get_contact',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <a-form-item label="挽单模板">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="rescueOrderRef"
            v-model="formModel.reply_template_rescue_order"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3, maxRows: 8 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in replayTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  rescueOrderRef,
                  'reply_template_rescue_order',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
      <!-- 询问联系方式模版 -->
      <a-form-item label="询问联系方式模版">
        <div class="df fd-cl w100p">
          <a-textarea
            ref="rescueOrderRef"
            v-model="formModel.reply_template_ask_contact"
            placeholder="请输入模版内容"
            :auto-size="{ minRows: 3, maxRows: 8 }"
          />
          <a-space wrap>
            <span>插入通配符</span>
            <a-button
              v-for="item in askContactTemplateM"
              :key="item.value"
              type="text"
              size="mini"
              @click="
                insertName(
                  rescueOrderRef,
                  'reply_template_ask_contact',
                  item.value
                )
              "
            >
              +{{ item.label }}
            </a-button>
          </a-space>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
  import { PropType, ref, computed } from 'vue';
  import {
    requiredRule,
    requiredRuleArr,
    setCaretPosition,
  } from '@/utils/util';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    replayTemplateM,
    replayTemplateTypeM,
    askContactTemplateM,
  } from '@/components/dict-select/dict-clue';
  import { getText } from '@/components/dict-select/dict-util';
  import RequestSelect from '@/components/select/request-select.vue';
  import PopoverCheckGroup from './popover-check-group.vue';

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const emits = defineEmits(['save']);

  const props = defineProps({
    activeLabel: {
      type: String,
      default: '适用账号',
    },
  });

  function defaultForm() {
    return {
      template_name: '',
      thread_base_setting_keywords_ids: [],
      reply_template_thread_seas: '',
      reply_template_one_thread: '',
      reply_template_get_contact: '',
      reply_template_rescue_order: '',
      reply_template_ask_contact: '',
      id: null,
    };
  }
  const formModel = ref(defaultForm());
  const threadSeasRef = ref();
  const oneThreadRef = ref();
  const getContactRef = ref();
  const rescueOrderRef = ref();

  function insertName(
    iptRef: any,
    key:
      | 'reply_template_thread_seas'
      | 'reply_template_one_thread'
      | 'reply_template_get_contact'
      | 'reply_template_rescue_order'
      | 'reply_template_ask_contact',
    name: string
  ) {
    let newName: string = formModel.value[key];
    let iptDom = iptRef?.$el.querySelector('textarea');
    let index = iptDom.selectionStart || newName?.length || 0;
    if (!newName) {
      newName = name;
    } else {
      newName = `${newName.slice(0, index)}${name}${newName.slice(index)}`;
    }
    formModel.value[key] = newName;
    setCaretPosition(iptDom, index + name.length);
  }

  // 获取配置信息
  function show(data?: any) {
    formRef.value.clearValidate();
    formModel.value = defaultForm();
    let initForm = defaultForm();
    formModel.value = initForm;
    if (data) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          data[key] || initForm[key as keyof typeof initForm];
      });
    }

    visible.value = true;
  }
  function sendInfo() {
    formRef.value.validate((err: any) => {
      if (!err) {
        loading.value = true;
        request('/api/thread/replyTemplateSave', {
          ...formModel.value,
        })
          .then(() => {
            Message.success('操作成功');
            visible.value = false;
            emits('save');
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

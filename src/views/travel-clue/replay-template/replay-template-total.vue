<template>
  <a-card>
    <div class="jc-sb">
      <a-space wrap>
        <a-button type="primary" @click="refreshData()">
          <template #icon>
            <icon-refresh />
          </template>
          <span>刷新</span>
        </a-button>
        <!-- 重按钮置 -->
        <a-button @click="resetSearch">
          <span>重置</span>
        </a-button>
        <c-range-picker
          v-model="formModel.time_range"
          @change="changeTimeRange"
        ></c-range-picker>
        <!-- 分组 -->
        <folder-tree-select
          v-model="formModel.dir_id"
          :send-params="{
            type: 'account',
          }"
          value-key="id"
          label-key="dir_name"
          placeholder="请选择分组"
          request-url="/api/material/dirList"
          style="width: 200px"
          :format-data="(arr:any) => arr?.children || []"
          :allow-clear="true"
          @change="refreshData"
        ></folder-tree-select>
        <!-- 全部账号 -->
        <request-select
          v-model="formModel.account_id"
          request-url="/api/devices/accountList"
          placeholder="请选择账号"
          allow-clear
          label-key="account_name"
          style="width: 200px"
          @change="handleSubmit({})"
        />

        <!-- 模版名称 -->
        <request-select
          v-model="formModel.template_name"
          request-url="/api/thread/replyTemplateList"
          placeholder="请选择模版名称"
          allow-clear
          label-key="template_name"
          style="width: 200px"
          @change="handleSubmit({})"
        />

        <!-- 模版标题 -->
        <dict-select
          v-model="formModel.template_id"
          :data-list="replayTemplateTitleM"
          placeholder="请选择模版标题"
          allow-clear
          style="width: 200px"
          @change="handleSubmit({})"
        />
        <a-input-search
          v-model="formModel.reply_tpl_aigc_id"
          placeholder="输入文案ID或文案内容"
          allow-clear
          style="width: 200px"
          @search="handleSubmit({})"
          @keydown.enter="handleSubmit({})"
        />
      </a-space>
    </div>
    <a-table
      ref="tableRef"
      v-model:loading="loading"
      class="mt-10"
      :scroll="{ x: 'max-content' }"
      :columns="columns"
      :data="tableData"
      :summary="summary"
      :pagination="false"
    >
    </a-table>
  </a-card>
</template>

<script setup lang="ts">
  import request from '@/api/request';
  import { computed, ref } from 'vue';
  import { Message, Popover } from '@arco-design/web-vue';
  import { useAppStore, useUserStore } from '@/store';
  import dayjs from 'dayjs';
  import { rateFormatShow } from '@/utils/table-utils/columns-config';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import ReplayTemplateAiModal from './replay-template-ai-modal.vue';
  import ReplayTemplateSave from './replay-template-save.vue';

  const replayTemplateTitleM = [
    { label: '陌拜私信模板', value: 'thread_seas' },
    { label: '意向回复模板', value: 'one_thread' },
    { label: '留资结束语模板', value: 'get_contact' },
    { label: '挽单模板', value: 'rescue_order' },
    { label: '询问联系方式模版', value: 'ask_contact' },
  ];

  const userStore = useUserStore();

  const generateFormModel = () => {
    return {
      time_range: [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      start_date: dayjs().startOf('month').format('YYYY-MM-DD'),
      end_date: dayjs().format('YYYY-MM-DD'),
      dir_id: null,
      template_id: '',
      reply_tpl_aigc_id: '',
      account_id: null,
    };
  };

  const formModel = ref(generateFormModel());

  const columns = computed(() => [
    {
      title: '分组',
      dataIndex: 'self_account_dir_name',
      fixed: 'left',
    },
    // zha
    {
      title: '账号名称',
      dataIndex: 'self_account_name',
    },
    {
      title: '模板名称',
      dataIndex: 'reply_template_name',
    },
    {
      title: '模版标题',
      dataIndex: 'reply_template_type_text',
    },
    {
      title: '文案ID',
      dataIndex: 'reply_tpl_aigc_id',
      align: 'center',
    },
    {
      title: '文案',
      dataIndex: 'message',
      width: 300,
    },
    {
      title: '累计发送人数',
      dataIndex: 'type_send_count',
      align: 'right',
      width: 120,
      fixed: 'right',
    },
    {
      title: '累计回复人数',
      dataIndex: 'type_receive_count',
      align: 'right',
      fixed: 'right',
      width: 120,
    },
    // 回复率
    {
      title: '回复率',
      dataIndex: 'type_receive_ratio',
      render: rateFormatShow('type_receive_ratio'),
      align: 'right',
      fixed: 'right',
      width: 120,
    },
  ]);

  const tableRef = ref();
  const loading = ref(false);
  const tableData = ref([]);
  const summaryData = ref({});
  // 模版合计数据
  const selfTotal = ref({});

  function getTableData() {
    request('/api/thread/replyTemplateStatisticsList', formModel.value)
      .then((res) => {
        tableData.value = res.data.list || [];
        // 汇总行数据
        summaryData.value = res.data.extends.total || [];
        // 模版合计数据
        selfTotal.value = res.data.extends.self_total || [];
      })
      .catch((err) => {
        console.log(err);
        return {
          list: [],
          total: 0,
        };
      });
  }
  getTableData();

  const summary = ({ data }) => {
    return [
      {
        self_account_dir_name: '汇总',
        ...summaryData.value,
      },
    ];
  };

  const changeTimeRange = (time_range: any) => {
    if (time_range && time_range.length === 2) {
      [formModel.value.start_date, formModel.value.end_date] = time_range;
    } else {
      formModel.value.start_date = null;
      formModel.value.end_date = null;
    }
    tableRef.value?.search();
  };

  function refreshData() {
    getTableData();
  }

  function handleSubmit() {
    getTableData();
  }

  function resetSearch() {
    formModel.value = generateFormModel();
    tableRef.value?.search();
  }
</script>

<style scoped lang="less">
  .text-overflow {
    display: inline-block;
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>

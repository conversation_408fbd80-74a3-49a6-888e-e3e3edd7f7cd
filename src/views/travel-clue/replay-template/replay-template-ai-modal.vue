<template>
  <d-modal
    v-model:visible="visible"
    title="AI润色回复模版"
    width="900px"
    unmount-on-close
    :footer="null"
  >
    <div class="table-card mt-0">
      <div class="table-card-header">
        <a-space>
          <a-radio-group
            v-model="formModel.state"
            type="button"
            @change="queryAction()"
          >
            <a-radio :value="1">已挑选可用（{{ tmpNum.select }}）</a-radio>
            <a-radio :value="2">新润色待挑选（{{ tmpNum.notSelect }}）</a-radio>
          </a-radio-group>
          <span class="title-box">
            已选择
            <a-tag>
              {{ rowSelection.selectedRowKeys.length }}
            </a-tag>
            个回复模版
          </span>
          <icon-close-circle class="cur-por" @click="resetHandler" />
        </a-space>
        <a-space>
          <a-button
            v-if="formModel.state === 2"
            :disabled="!rowSelection.selectedRows.length"
            type="primary"
            :loading="actioning"
            @click="selectAction"
          >
            <template #icon>
              <icon-check />
            </template>
            设置为可用
          </a-button>
          <a-popconfirm
            :content="`确定要删除${rowSelection.selectedRows.length}个回复模板吗?`"
            @ok="delAction()"
          >
            <a-button
              :disabled="!rowSelection.selectedRows.length"
              type="primary"
              :loading="actioning"
              status="danger"
            >
              <template #icon>
                <icon-delete />
              </template>
              删除
            </a-button>
          </a-popconfirm>
          <a-button type="primary" :loading="creating" @click="createAction">
            <template #icon>
              <icon-plus />
            </template>
            新润色20条
          </a-button>
        </a-space>
      </div>
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :row-selection="rowSelection"
        :auto-request="false"
        :scroll-percent="{ x: '100%', y: '70vh' }"
        :send-params="tableParams"
        @select-change="selectionChange"
      >
        <template #result="{ record }: TableColumnSlot">
          <div style="text-align: left">
            <a-input-group v-if="record.showIpt" class="w100p">
              <a-input v-model="record.template_ai" />
              <a-button
                :loading="record.loading"
                type="primary"
                @click="editTmp(record)"
              >
                <icon-check />
              </a-button>
            </a-input-group>
            <template v-else>
              <span>{{ record.template_ai }}</span>
              <a-link @click="record.showIpt = true">
                <icon-edit />
              </a-link>
            </template>
          </div>
        </template>
      </base-table>
    </div>
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, reactive, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const columns = computed(() => [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '结果',
      dataIndex: 'result',
    },
  ]);
  const visible = ref(false);
  const loading = ref(false);
  const theTable = ref();
  function defaultForm() {
    return {
      state: 1,
    };
  }
  const formModel = ref(defaultForm());

  const tableParams = computed(() => ({
    ...formModel.value,
    ...props.sendParams,
  }));

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
  });
  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys.filter((item: any) => item);
    rowSelection.selectedRows = selectedRows;
  };
  const resetHandler = () => {
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  const tmpNum = ref({
    select: '',
    notSelect: '',
  });
  function getTmpNum() {
    request('/api/thread/getAiReplyTpl', {
      ...tableParams.value,
      page: 1,
      pageSize: 1,
      state: 1,
    }).then((res) => {
      tmpNum.value.select = res.data.total;
    });
    request('/api/thread/getAiReplyTpl', {
      ...tableParams.value,
      page: 1,
      pageSize: 1,
      state: 2,
    }).then((res) => {
      tmpNum.value.notSelect = res.data.total;
    });
  }

  function queryAction() {
    theTable.value?.search();
  }

  const show = () => {
    visible.value = true;
    nextTick(() => {
      queryAction();
      getTmpNum();
    });
  };

  let cancelToken: AbortController;
  function getList(data: any) {
    resetHandler();
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    return request(
      '/api/thread/getAiReplyTpl',
      {
        ...data,
      },
      cancelToken.signal
    );
  }

  const actioning = ref(false);
  function delAction() {
    actioning.value = true;
    request('/api/thread/delAiReplyTpl', {
      tpl_id: rowSelection.selectedRows.map((item: any) => item.id),
    })
      .then(() => {
        Message.success('操作成功');
        queryAction();
      })
      .finally(() => {
        actioning.value = false;
      });
  }
  function selectAction() {
    actioning.value = true;
    request('/api/thread/selectAiReplyTpl', {
      tpl_id: rowSelection.selectedRows.map((item: any) => item.id),
    })
      .then(() => {
        Message.success('操作成功');
        queryAction();
        getTmpNum();
      })
      .finally(() => {
        actioning.value = false;
      });
  }

  const creating = ref(false);
  function createAction() {
    creating.value = true;
    request('/api/thread/generateAiSendMsg', {
      ...props.sendParams,
      count: 20,
    })
      .then(() => {
        Message.success('操作成功');
        queryAction();
        getTmpNum();
      })
      .finally(() => {
        creating.value = false;
      });
  }
  function editTmp(record: any) {
    if (!record.template_ai) {
      return Message.error('请填写模板内容');
    }
    record.loading = true;
    request('/api/thread/setAiReplyTpl', {
      tpl_id: record.id,
      template_ai: record.template_ai,
    })
      .then(() => {
        record.showIpt = false;
      })
      .finally(() => {
        record.loading = false;
      });
  }

  defineExpose({
    show,
  });
</script>

<style scoped></style>

<template>
  <div class="popover-check-group">
    <a-popover
      v-model:popup-visible="popupVisible"
      trigger="click"
      position="bottom"
      :unmount-on-close="false"
      :popup-style="{
        position: 'fixed',
        top: popoverPosition.top + 'px',
        left: popoverPosition.left + 'px',
        transform: 'none',
      }"
      @popup-visible-change="handlePopupVisibleChange"
    >
      <template #content>
        <div class="popover-content">
          <div class="popover-header">
            <span class="header-title">选择{{ activeLabel }}</span>
            <div class="header-actions">
              <a-tooltip
                v-if="activeLabel === '适用分组'"
                content="可多选不同类型的分组"
              >
                <icon-question-circle class="help-icon" />
              </a-tooltip>
              <a-button type="text" size="mini" @click="handleClear">
                <template #icon><icon-delete /></template>
                清空
              </a-button>
              <a-button type="primary" size="mini" @click="handleConfirm">
                确定
              </a-button>
            </div>
          </div>
          <div class="groups-container">
            <div
              v-for="(group, type) in groupedData"
              v-show="group.length > 0"
              :key="type"
              class="group-section"
            >
              <div class="group-header">
                <span class="group-title">{{ getTypeLabel(type) }}</span>
                <span class="group-count">({{ group.length }})</span>
                <a-checkbox
                  :model-value="isGroupAllSelected(type)"
                  :indeterminate="isGroupIndeterminate(type)"
                  class="select-all-checkbox"
                  @change="(checked) => handleGroupSelectAll(type, checked)"
                >
                  全选
                </a-checkbox>
              </div>
              <a-checkbox-group
                v-model:model-value="selectedValues[type]"
                class="checkbox-group"
                @change="handleChange"
              >
                <a-checkbox
                  v-for="item in group"
                  :key="item.id"
                  :value="item.id"
                  class="checkbox-item"
                >
                  {{ item.title }}
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
        </div>
      </template>
      <div class="trigger-content" @click="togglePopup">
        <div v-if="hasSelectedItems" class="selected-items">
          <span
            v-for="item in displaySelectedItems"
            :key="item.id"
            class="selected-tag"
          >
            <span class="tag-content">
              <span class="tag-type">{{ getTypeLabel(item.type) }}</span>
              <span class="tag-name">{{ item.title }}</span>
            </span>
            <icon-close
              class="tag-close"
              @click.stop="handleRemoveItem(item.type, item.id)"
            />
          </span>
          <span v-if="hasMoreItems" class="more-tag">...</span>
        </div>
        <span v-else class="placeholder">请选择模板</span>
        <icon-down :class="{ 'is-active': popupVisible }" />
      </div>
    </a-popover>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, nextTick, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconDown,
    IconQuestionCircle,
    IconClose,
    IconDelete,
  } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';
  import { useAppStore } from '@/store';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    activeLabel: {
      type: String,
      default: '适用账号',
    },
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  const popupVisible = ref(false);
  const templateData = ref([]);
  const selectedValues = ref({
    keyword: [],
    tk_account: [],
    live_room: [],
  });

  const popoverPosition = ref({ top: 0, left: 0 });

  // 获取浮窗容器
  const getPopupContainer = () => {
    return (
      document?.body ||
      document?.documentElement ||
      document?.querySelector('body')
    );
  };

  // 获取模板数据
  const fetchTemplateData = async () => {
    try {
      const response = await request('/api/thread/getBaseSettingKeywords');
      if (response.code === 0) {
        const { data } = response;
        templateData.value = data || [];
      } else {
        Message.error('获取模板数据失败');
      }
    } catch (error) {
      Message.error('获取模板数据失败');
      console.error(error);
    }
  };

  // 按类型分组数据
  const groupedData = computed(() => {
    const groups = {
      keyword: [],
      tk_account: [],
      live_room: [],
    };

    templateData.value.forEach((item) => {
      if (groups[item.type]) {
        groups[item.type].push(item);
      }
    });

    // 如果是适用账号，只返回内容号分组
    if (props.activeLabel === '适用账号') {
      return {
        tk_account: groups.tk_account,
      };
    }

    // 过滤掉空数组
    return Object.fromEntries(
      Object.entries(groups).filter(([_, items]) => items.length > 0)
    );
  });

  // 获取类型标签
  const getTypeLabel = (type) => {
    const labels = {
      keyword: '关键词',
      tk_account: '内容号',
      live_room: '直播间',
    };
    return labels[type] || type;
  };

  // 获取已选择项的详细信息
  const selectedItemsMap = computed(() => {
    const map = {};
    Object.entries(selectedValues.value).forEach(([type, ids]) => {
      if (ids.length > 0) {
        map[type] = templateData.value.filter(
          (item) => item.type === type && ids.includes(item.id)
        );
      }
    });
    return map;
  });

  // 获取用于显示的选中项（最多10个）
  const displaySelectedItems = computed(() => {
    const allItems = [];
    Object.entries(selectedItemsMap.value).forEach(([type, items]) => {
      items.forEach((item) => {
        allItems.push({ ...item, type });
      });
    });
    return allItems.slice(0, 10);
  });

  // 计算总选中项数量
  const totalSelectedCount = computed(() => {
    return Object.values(selectedValues.value).reduce(
      (total, ids) => total + ids.length,
      0
    );
  });

  // 是否有超出10个的项目
  const hasMoreItems = computed(() => {
    return totalSelectedCount.value > 10;
  });

  // 是否有选中的项
  const hasSelectedItems = computed(() => {
    return Object.values(selectedValues.value).some((ids) => ids.length > 0);
  });

  // 处理选择变化
  const handleChange = () => {
    // 将所有选中的 id 合并到一个数组中
    const selectedIds = Object.values(selectedValues.value).flat();
    emit('update:modelValue', selectedIds);
    emit('change', selectedIds);
  };

  // 清空选择
  const handleClear = () => {
    selectedValues.value = {
      keyword: [],
      tk_account: [],
      live_room: [],
    };
    handleChange();
  };

  // 切换弹出框
  const togglePopup = () => {
    popupVisible.value = !popupVisible.value;
  };

  // 移除选中项
  const handleRemoveItem = (type, id) => {
    const index = selectedValues.value[type].indexOf(id);
    if (index > -1) {
      selectedValues.value[type].splice(index, 1);
      handleChange();
    }
  };

  // 处理浮窗显示状态变化
  const handlePopupVisibleChange = (visible) => {
    if (visible) {
      nextTick(() => {
        const trigger = document.querySelector('.trigger-content');
        if (trigger) {
          const rect = trigger.getBoundingClientRect();
          popoverPosition.value = {
            top: rect.bottom + 4,
            left: rect.left,
          };
        }
      });
    }
  };

  // 处理确认按钮点击
  const handleConfirm = () => {
    popupVisible.value = false;
  };

  // 初始化时处理数据
  onMounted(() => {
    fetchTemplateData();
  });

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue && Array.isArray(newValue)) {
        // 重置选中值
        selectedValues.value = {
          keyword: [],
          tk_account: [],
          live_room: [],
        };
        // 将 id 数组分配到对应的类型中
        const ids = new Set(newValue);
        templateData.value.forEach((item) => {
          if (ids.has(item.id) && selectedValues.value[item.type]) {
            selectedValues.value[item.type].push(item.id);
          }
        });
      }
    },
    { immediate: true }
  );

  // 监听模板数据变化
  watch(
    () => templateData.value,
    (newValue) => {
      if (props.modelValue && Array.isArray(props.modelValue)) {
        // 重置选中值
        selectedValues.value = {
          keyword: [],
          tk_account: [],
          live_room: [],
        };
        // 将 id 数组分配到对应的类型中
        const ids = new Set(props.modelValue);
        newValue.forEach((item) => {
          if (ids.has(item.id) && selectedValues.value[item.type]) {
            selectedValues.value[item.type].push(item.id);
          }
        });
      }
    }
  );

  // 判断分组是否全选
  const isGroupAllSelected = (type) => {
    const group = groupedData.value[type] || [];
    return (
      group.length > 0 && selectedValues.value[type].length === group.length
    );
  };

  // 判断分组是否部分选中
  const isGroupIndeterminate = (type) => {
    const group = groupedData.value[type] || [];
    return (
      selectedValues.value[type].length > 0 &&
      selectedValues.value[type].length < group.length
    );
  };

  // 处理分组全选
  const handleGroupSelectAll = (type, checked) => {
    if (checked) {
      // 全选：将该分组下所有项的 id 添加到选中值中
      selectedValues.value[type] = groupedData.value[type].map(
        (item) => item.id
      );
    } else {
      // 取消全选：清空该分组的选中值
      selectedValues.value[type] = [];
    }
    handleChange();
  };
</script>

<style scoped>
  .popover-check-group {
    display: inline-block;
    width: 100%;
  }

  .trigger-content {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    padding: 4px 8px;
    border: 1px solid transparent;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: var(--color-fill-2);
    font-size: 13px;
    min-height: 32px;
    width: 100%;
    /* max-width: 300px; */
  }

  .trigger-content:hover {
    border-color: rgb(var(--primary-6));
  }

  .trigger-text {
    color: var(--color-text-1);
    font-size: 14px;
  }

  .is-active {
    transform: rotate(180deg);
    transition: transform 0.2s ease;
  }

  .popover-content {
    min-width: 400px;
    max-width: 300px;
    /* max-height: 400px;
    padding-right: 10px;
    overflow-y: auto; */
    position: relative;
  }

  .popover-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--color-border);
  }

  .header-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text-1);
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .help-icon {
    color: var(--color-text-3);
    cursor: help;
    font-size: 14px;
  }

  .groups-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 12px;
    max-height: 300px;
    padding-right: 10px;
    overflow-y: auto;
  }

  .group-section {
    flex: 0 0 auto;
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 12px;
  }

  .group-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .group-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
  }

  .group-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--color-text-1);
  }

  .group-count {
    font-size: 12px;
    color: var(--color-text-3);
    padding: 1px 6px;
    border-radius: 10px;
  }

  .select-all-checkbox {
    margin-left: auto;
    font-size: 12px;
  }

  .checkbox-group {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    padding: 4px 0;
  }

  .checkbox-item {
    margin-right: 0;
    padding: 2px 8px;
    border-radius: 12px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    font-size: 12px;
    /* height: 20px; */
    display: flex;
    align-items: center;
  }

  .checkbox-item:hover {
    color: rgb(var(--primary-6));
    border-color: transparent;
  }

  .popover-content::-webkit-scrollbar {
    width: 4px;
  }

  .popover-content::-webkit-scrollbar-thumb {
    background-color: var(--color-fill-3);
    border-radius: 2px;
  }

  .popover-content::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    overflow: hidden;
    flex: 1;
    padding: 2px 0;
  }

  .selected-tag {
    display: inline-flex;
    align-items: center;
    background-color: var(--color-bg-1);
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 12px;
    color: var(--color-text-2);
    white-space: nowrap;
    transition: all 0.2s ease;
  }

  .selected-tag:hover {
    background-color: var(--color-fill-3);
  }

  .tag-content {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .tag-type {
    color: var(--color-text-2);
    font-size: 12px;
  }

  .tag-name {
    color: var(--color-text-1);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tag-close {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    color: var(--color-text-3);
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;
    font-size: 10px;
  }

  .tag-close:hover {
    color: var(--color-text-1);
    background-color: var(--color-fill-4);
  }

  .more-tag {
    color: var(--color-text-3);
    margin-left: 2px;
    font-size: 14px;
    font-weight: bold;
  }

  .placeholder {
    color: var(--color-text-3);
    flex: 1;
  }

  :deep(.arco-popover-popup-content) {
    padding: 0;
    border: none;
  }

  :deep(.arco-checkbox-label) {
    color: var(--color-text-2);
    font-size: 12px;
  }

  :deep(.arco-checkbox:hover .arco-checkbox-label) {
    color: rgb(var(--primary-6));
  }

  :deep(.arco-checkbox-checked .arco-checkbox-label) {
    color: rgb(var(--primary-6));
  }
</style>

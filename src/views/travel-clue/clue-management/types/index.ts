/**
 * 意向线索管理模块类型定义
 */

// 账号信息接口
export interface AccountInfo {
  id: string;
  self_account_id: string; // API 调用所需的账号ID
  name: string;
  platform: string;
  avatar?: string;
  status: 'online' | 'offline' | 'busy';
  clueCount: number;
  unreadCount: number;
  lastActiveTime?: string;
}

// 线索信息接口
export interface ClueInfo {
  id: string;
  thread_seas_id: string;
  account_id: string;
  account_name: string;
  platform: string;
  source: string;
  thread_content: string;
  status: '待分配' | '已分配' | '已跟进' | '已成交';
  status_two: '未留资' | '已留资';
  comment_time: string;
  time_distribute?: string;
  time_first_response?: string;
  deal_time?: string;
  service_user?: string;
  phone?: string;
  wechat?: string;
  remark?: string;
  media_sec_uid?: string;
  from_user_id?: string;
  source_account?: string;
  from_setting?: string;
  area?: string;
  intentionality_intent?:
    | 'positive'
    | 'potential_positive'
    | 'potential_negative'
    | 'negative';
}

// 用户信息接口
export interface UserInfo {
  id: string;
  name: string;
  platform: string;
  avatar?: string;
  phone?: string;
  wechat?: string;
  location?: string;
  tags?: string[];
  followCount?: number;
  fansCount?: number;
  workCount?: number;
  likeCount?: number;
  profile?: string;
  registerTime?: string;
  lastActiveTime?: string;
}

// 聊天消息接口
export interface ChatMessage {
  id: string;
  message: string;
  message_type: 'text' | 'image' | 'emoji';
  msg_show: 'left' | 'right';
  send_time: string;
  send_status: 1 | 3 | 5; // 1-发送中, 3-发送成功, 5-发送失败
  send_user: string;
  avatar?: string;
}

// 聊天参数接口
export interface ChatParams {
  platform: string;
  account_id: string;
  thread_seas_id: string;
}

// 搜索筛选参数接口
export interface SearchParams {
  id?: string;
  thread_setting_id?: string;
  platform?: string;
  source?: string;
  status?: string;
  status_two?: string;
  from_setting?: string;
  account_id?: string;
  thread_content?: string;
  comment_time?: [string, string];
  time_distribute?: [string, string];
  time_first_response?: [string, string];
  deal_time?: [string, string];
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 列表响应接口
export interface ListResponse<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 账号统计信息接口
export interface AccountStats {
  totalClues: number;
  pendingClues: number;
  followedClues: number;
  dealClues: number;
  unreadMessages: number;
}

// 线索统计信息接口
export interface ClueStats {
  total: number;
  pending: number;
  assigned: number;
  followed: number;
  deal: number;
  unregistered: number;
  registered: number;
}

// 表格列配置接口
export interface TableColumn {
  title: string;
  dataIndex: string;
  key?: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  defaultHide?: boolean;
  sortable?: boolean;
}

// 表格操作接口
export interface TableAction {
  label: string;
  key: string;
  type?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  loading?: boolean;
  visible?: boolean;
}

// 模块状态接口
export interface ModuleState {
  loading: boolean;
  error: string | null;
  data: any;
  pagination?: PaginationParams;
}

// 全局状态接口
export interface GlobalState {
  selectedAccount: AccountInfo | null;
  selectedClue: ClueInfo | null;
  selectedUser: UserInfo | null;
  accountList: AccountInfo[];
  clueList: ClueInfo[];
  chatMessages: ChatMessage[];
  searchParams: SearchParams;
  accountStats: AccountStats;
  clueStats: ClueStats;
}

// 事件类型定义
export type EventType =
  | 'account-change'
  | 'clue-select'
  | 'message-sent'
  | 'status-change'
  | 'search-change'
  | 'refresh-data';

// 事件处理函数类型
export type EventHandler<T = any> = (data: T) => void;

// 组件Props类型
export interface ComponentProps {
  loading?: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  theme?: 'light' | 'dark';
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
}

// 表单字段配置类型
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'select' | 'radio' | 'checkbox' | 'date' | 'textarea';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  rules?: ValidationRule[];
  defaultValue?: any;
  disabled?: boolean;
  visible?: boolean;
}

// 消息项接口
export interface MessageItem {
  id: string;
  username: string;
  avatar?: string;
  preview: string;
  time: string;
  unread: number;
  tags?: string[];
  platform: string;
  account_id: string;
  self_account_id: string;
  thread_seas_id: string;
  clue_id?: string;
}

// 通知设置接口
export interface NotificationSettings {
  newMessage: boolean;
  tabBlink: boolean;
}

// MessageList 组件 Props 接口
export interface MessageListProps {
  selectedAccount?: AccountInfo | null;
  searchKeyword?: string;
  notificationSettings?: NotificationSettings;
}

// MessageList 组件 Events 接口
export interface MessageListEmits {
  (event: 'messageSelect', message: MessageItem): void;
  (event: 'loadMore'): void;
  (event: 'unreadNavigate', messageId: string): void;
  (event: 'messagesLoaded', messages: MessageItem[]): void;
  (event: 'loadError', error: string): void;
  (event: 'notificationSettingsChange', settings: NotificationSettings): void;
}

// API 消息列表响应接口
export interface ApiMessageItem {
  id: string;
  thread_seas_id: string;
  account_id: string;
  account_name: string;
  platform: string;
  avatar_url?: string;
  last_message: string;
  last_message_time: string;
  unread_count: number;
  tags?: string[];
}

// API 聊天记录响应接口
export interface ApiChatRecord {
  id: string;
  message: string;
  message_type: 'text' | 'image' | 'emoji';
  msg_show: 'left' | 'right';
  send_time: string;
  send_status: 1 | 3 | 5; // 1-发送中, 3-发送成功, 5-发送失败
  send_user: string;
  avatar?: string;
}

// ChatWindow 组件 Props 接口
export interface ChatWindowProps {
  selectedMessage?: MessageItem | null;
  selectedAccount?: AccountInfo | null;
  sendParams?: any;
}

// ChatWindow 组件 Events 接口
export interface ChatWindowEmits {
  (event: 'avatarError', evt: Event): void;
  (event: 'retryMessage', messageId: string): void;
  (event: 'messagesLoaded', messages: ChatMessage[]): void;
  (event: 'loadError', error: string): void;
}

// 聊天记录搜索相关接口定义

// 聊天记录搜索请求参数接口
export interface ChatSearchMessageParams {
  content: string; // 搜索关键词
  account_id?: string; // 账户ID（可选）
  platform?: string; // 平台（可选）
  self_account_id?: string; // 自己的账户ID（可选）
  page?: number; // 页码
  pageSize?: number; // 每页数量
}

// 聊天记录搜索用户信息接口
export interface ChatSearchUser {
  account_id: string;
  account_name: string;
  platform: string;
  avatar?: string;
  account_profile_photo?: string;
  last_message_time?: string;
  message_count?: number;
  self_account_id?: string;
}

// 聊天记录搜索消息接口
export interface ChatSearchMessage {
  id: string;
  message: string;
  message_type: 'text' | 'image' | 'emoji';
  msg_show: 'left' | 'right';
  send_time: string;
  send_user: string;
  avatar?: string;
  account_id: string;
  platform: string;
  account_name?: string;
  account_profile_photo?: string;
  self_account_id?: string;
}

// 聊天记录搜索响应数据接口
export interface ChatSearchMessageResponse {
  users: ChatSearchUser[]; // 相关用户列表
  messages: ChatSearchMessage[]; // 消息列表
  total: number; // 总数
  page: number; // 当前页
  pageSize: number; // 每页数量
}

// ChatRecordDialog 组件 Props 接口
export interface ChatRecordDialogProps {
  visible: boolean; // 弹窗显示状态
  initialKeyword?: string; // 初始搜索关键词
  selectedAccount?: AccountInfo | null; // 当前选中的账户
}

// ChatRecordDialog 组件 Events 接口
export interface ChatRecordDialogEmits {
  (event: 'update:visible', visible: boolean): void;
  (event: 'close'): void;
  (
    event: 'locateMessage',
    messageInfo: {
      account_id: string;
      platform: string;
      self_account_id: string;
      message_id: string;
      send_time: string; // 添加发送时间参数
    }
  ): void;
}

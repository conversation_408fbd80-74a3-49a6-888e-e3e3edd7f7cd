<!--
  线索管理界面 - 桌面端专用
  左侧账号列表 + 中间消息列表 + 右侧聊天窗口
-->
<template>
  <div class="main-container">
    <div class="table-card">
      <search-form-clue
        ref="searchFormRef"
        :form-data="formModel"
        :get-default-form-data="generateFormModel"
        @search="handleSubmit"
      >
        <template #formItemGroup>
          <a-form-item label="搜索">
            <!-- 搜索输入框带联想功能 - 使用 a-popover -->
            <a-popover
              v-model:popup-visible="showSuggestions"
              trigger="manual"
              position="bottom"
              :arrow-style="{ display: 'none' }"
              class="no-padding-popover"
              content-class="search-suggestions-popover"
            >
              <template #content>
                <div class="search-suggestions-content">
                  <!-- 客户名称部分 -->
                  <div
                    v-if="customerSuggestions.length > 0"
                    class="suggestion-section"
                  >
                    <div class="suggestion-title">客户名称</div>
                    <div
                      v-for="(customer, index) in customerSuggestions.slice(
                        0,
                        showALlCustomerSuggestions
                          ? customerSuggestions.length
                          : 5
                      )"
                      :key="`customer-${index}`"
                      class="suggestion-item customer-item"
                      @click="selectSuggestion(customer, 'customer')"
                    >
                      <div class="suggestion-avatar">
                        <a-avatar :size="32" @error="handleAvatarError">
                          <img
                            v-if="customer.avatar"
                            :src="customer.avatar"
                            alt=""
                          />
                          <span v-else>
                            {{ customer.name?.charAt(0)?.toUpperCase() || 'U' }}
                          </span>
                        </a-avatar>
                      </div>
                      <div class="suggestion-content">
                        <div class="suggestion-name">{{ customer.name }}</div>
                        <div class="suggestion-meta">{{
                          customer.platform
                        }}</div>
                      </div>
                    </div>
                    <!-- 查看全部选项 -->
                    <div
                      v-if="
                        customerSuggestions.length > 5 &&
                        !showALlCustomerSuggestions
                      "
                      class="suggestion-footer"
                    >
                      <a-button
                        type="text"
                        size="mini"
                        @click="showALlCustomerSuggestions = true"
                      >
                        查看全部 ({{ customerSuggestions.length }})
                      </a-button>
                    </div>
                  </div>

                  <!-- 聊天记录部分 -->
                  <div
                    v-if="chatSuggestions.length > 0"
                    class="suggestion-section"
                  >
                    <div class="suggestion-title">聊天记录</div>
                    <div
                      v-for="(chat, index) in chatSuggestions.slice(
                        0,
                        showALlChatSuggestions ? chatSuggestions.length : 5
                      )"
                      :key="`chat-${index}`"
                      class="suggestion-item chat-item"
                      @click="selectSuggestion(chat, 'chat')"
                    >
                      <div class="suggestion-avatar">
                        <a-avatar
                          :size="32"
                          :image-url="chat.customer_avatar"
                          @error="handleAvatarError"
                        >
                          {{
                            chat.customer_name?.charAt(0)?.toUpperCase() || 'U'
                          }}
                        </a-avatar>
                      </div>
                      <div class="suggestion-content">
                        <div class="suggestion-name">{{
                          chat.customer_name
                        }}</div>
                        <div class="suggestion-count"
                          >{{ chat.record_count || 1 }}条相关聊天记录</div
                        >
                      </div>
                    </div>
                  </div>

                  <!-- 查看全部选项 -->
                  <div
                    v-if="chatSuggestions.length > 5 && !showALlChatSuggestions"
                    class="suggestion-footer"
                  >
                    <a-button
                      type="text"
                      size="mini"
                      @click="showALlChatSuggestions = true"
                    >
                      查看全部 ({{ chatSuggestions.length }})
                    </a-button>
                  </div>

                  <a-empty v-if="totalCount === 0">
                    <template #image>
                      <icon-message />
                    </template>
                    <template #description> 暂无搜索结果 </template>
                  </a-empty>
                </div>
              </template>

              <a-input-search
                v-model="formModel.content"
                placeholder="请输入客户名称 / 聊天内容"
                allow-clear
                class="w-300"
                @input="handleSearchInput"
                @search="handleSubmit()"
                @keydown.enter="handleSubmit()"
                @focus="handleInputFocus"
                @blur="handleInputBlur"
              />
            </a-popover>
            <a-space class="ml-10">
              <!-- 展开 -->
              <a-button type="text" @click="expandFn()">
                <template #icon>
                  <icon-filter />
                </template>
                <span v-if="expandFlag"> 收起 </span>
                <span v-else> 展开 </span>
              </a-button>
            </a-space>
          </a-form-item>
          <template v-if="expandFlag">
            <a-form-item label="产品">
              <request-radio
                v-model="formModel.thread_setting_id"
                api="product"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item label="来源媒体">
              <dict-radio
                v-model="formModel.platform"
                :data-list="contactWayListM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item v-if="isCollectSwitchOpen" label="线索来源">
              <dict-radio
                v-model="formModel.source"
                :data-list="clueSourceTypeM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item label="跟进状态">
              <dict-radio
                v-model="formModel.status"
                :data-list="clueStatusM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
            <a-form-item label="留资状态">
              <dict-radio
                v-model="formModel.status_two"
                :data-list="clueStatusTwoM"
                show-all
                @change="handleSubmit()"
              />
            </a-form-item>
          </template>
        </template>
      </search-form-clue>
    </div>
    <div class="clue-management">
      <!-- 桌面端三栏布局 -->
      <div class="desktop-layout">
        <!-- 左侧账号列表 -->
        <div class="account-sidebar">
          <MediaAccountList
            ref="accountListRef"
            :selected-account="selectedAccount"
            @account-select="handleAccountSelect"
            @accounts-loaded="handleAccountsLoaded"
            @load-error="handleLoadError"
          />
        </div>

        <!-- 中间消息列表区域 -->
        <MessageList
          ref="messageListRef"
          :selected-account="selectedAccount"
          :search-keyword="searchKeyword"
          @message-select="handleMessageSelect"
          @load-more="handleLoadMore"
          @unread-navigate="handleUnreadNavigate"
        />

        <!-- 右侧聊天窗口 -->
        <ChatWindow
          ref="chatWindowRef"
          :selected-message="selectedMessage"
          :selected-account="selectedAccount"
          @avatar-error="handleAvatarError"
        />
      </div>
    </div>

    <!-- 聊天记录详情弹窗 -->
    <ChatRecordDialog
      ref="chatRecordDialogRef"
      @close="handleChatRecordDialogClose"
      @locate-message="handleLocateMessage"
    />
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    reactive,
    computed,
    watch,
    onMounted,
    onUnmounted,
    provide,
    nextTick,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconMore,
    IconMessage,
    IconImage,
    IconAttachment,
    IconGift,
    IconSend,
    IconSearch,
    IconSettings,
    IconExclamationCircle,
    IconRefresh,
    IconLoading,
    IconCheck,
    IconExclamation,
    // IconFaceSmile,
  } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';
  import AccountInfoCard from '@/components/account-info/account-info.vue';
  import MediaAccountList from '@/views/travel-clue/clue-management/components/MediaAccountList.vue';
  import MessageList from '@/views/travel-clue/clue-management/components/MessageList.vue';
  import ChatWindow from '@/views/travel-clue/clue-management/components/ChatWindow.vue';
  import ChatRecordDialog from '@/views/travel-clue/clue-management/components/ChatRecordDialog.vue';
  import SearchFormClue from '@/components/search-form-tile/search-form-clue.vue';
  import RequestRadio from '@/components/select/request-radio.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import {
    clueSourceTypeM,
    clueStatusTwoM,
    clueStatusM,
  } from '@/components/dict-select/dict-clue';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import { useDataCacheStore, useAppStore, useUserStore } from '@/store';
  import type { AccountInfo, ClueInfo, UserInfo, MessageItem } from './types';

  const expandFlag = ref(false);
  const searchFormRef = ref();
  const accountListRef = ref();
  const messageListRef = ref();
  const chatWindowRef = ref();
  const chatRecordDialogRef = ref();
  const showALlCustomerSuggestions = ref(false);
  const showALlChatSuggestions = ref(false);
  const expandFn = () => {
    expandFlag.value = !expandFlag.value;
    if (!expandFlag.value) {
      searchFormRef.value?.resetHandler();
    }
  };
  const generateFormModel = () => {
    return {
      id: null as string | null,
      thread_seas_id: null,
      distribute_rule: null,
      status: null,
      status_two: null,
      source: null,
      platform: null,
      thread_setting_id: '',
      from_setting: null,
      time_distribute: null,
      service_user_fs_id: null,
      time_first_response: null,
      cancellation_time: null,
      content: null,
    };
  };

  const userStore = useUserStore();

  // 判断collect_switch是否open
  const isCollectSwitchOpen = computed(() => {
    return userStore.collect_switch === 'open';
  });

  const formModel = reactive(generateFormModel());

  // 搜索联想相关状态
  const showSuggestions = ref(false);
  const customerSuggestions = ref<any[]>([]);
  const chatSuggestions = ref<any[]>([]);
  const totalCount = ref(0);
  const searchTimer = ref<number | null>(null);
  const isInputFocused = ref(false);

  // 账号列表数据 - 现在由 MediaAccountList 组件管理
  const accountList = ref<AccountInfo[]>([]);

  // 选中的账号
  const selectedAccount = ref<AccountInfo | null>(null);

  // 搜索关键词
  const searchKeyword = ref('');

  // 选中的消息 - 现在聊天数据由 ChatWindow 组件内部管理
  const selectedMessage = ref<MessageItem | null>(null);

  // 聊天记录弹窗相关数据
  const chatRecordDialogVisible = ref(false);
  const chatRecordKeyword = ref('');

  // 事件处理函数 - 移除冗余提示，提升用户体验
  const handleAccountSelect = (account: any) => {
    selectedAccount.value = account;

    // 重置选中的消息状态，确保切换账号后清空之前的选中状态
    selectedMessage.value = null;

    console.log('选中账号已切换:', account?.name, '头像:', account?.avatar);
    console.log('✅ 已重置选中消息状态，右侧聊天窗口将清空');
    // 移除账号选择提示，避免过于频繁的消息干扰
  };

  // 处理来自 MediaAccountList 组件的事件
  const handleAccountsLoaded = (accounts: AccountInfo[]) => {
    accountList.value = accounts;
    console.log('主页面接收到账号列表:', accounts.length, '个账号');
  };

  const handleLoadError = (error: string) => {
    console.error('账号列表加载失败:', error);
    // 错误处理已在组件内部完成，这里可以添加额外的处理逻辑
  };

  // 转换账号数据为 AccountInfo 组件所需格式
  const transformAccountData = (account: any) => {
    // 确保 account_id 有值，优先使用原始数据，否则使用 id 或生成一个
    const accountId =
      account.originalData?.account_id ||
      account.originalData?.id ||
      account.id ||
      `account_${Date.now()}`;

    return {
      avatar_url: account.avatar,
      account_name: account.name,
      account_id: accountId,
      platform: account.platform,
      // 保留原始数据以备扩展使用
      ...account.originalData,
    };
  };

  // 处理消息选择事件 - 简化版本，只更新选中消息
  const handleMessageSelect = async (message: MessageItem) => {
    // 如果选择的是同一个消息，不需要重新加载
    if (selectedMessage.value?.id === message.id) {
      return;
    }

    console.log('用户选择新的聊天对象:', message.username);

    // 更新选中的消息，ChatWindow 组件会自动获取聊天记录
    selectedMessage.value = message;
    console.log('✅ 选中消息已更新，ChatWindow 将自动加载聊天记录');
  };

  // 处理加载更多事件
  const handleLoadMore = () => {
    console.log('加载更多消息');
  };

  // 处理未读消息导航事件
  const handleUnreadNavigate = (messageId: string) => {
    console.log('导航到未读消息:', messageId);
  };

  // 处理聊天记录弹窗关闭事件
  const handleChatRecordDialogClose = () => {
    console.log('聊天记录弹窗已关闭');
    chatRecordDialogVisible.value = false;
    chatRecordKeyword.value = '';
  };

  // 处理消息定位事件
  const handleLocateMessage = async (messageInfo: {
    account_id: string;
    platform: string;
    self_account_id: string;
    message_id: string;
    send_time: string;
  }) => {
    console.log('📍 定位到消息:', messageInfo);

    try {
      // 1. 先选中对应的账号
      await accountListRef.value?.selectAccount(messageInfo.self_account_id);

      // 2. 等待账号选中后，再选中对应的消息
      setTimeout(async () => {
        if (messageInfo.account_id) {
          await messageListRef.value?.selectMessage(messageInfo.account_id);

          // 3. 等待聊天窗口加载完成后，使用时间定位到具体消息
          setTimeout(() => {
            if (chatWindowRef.value && messageInfo.send_time) {
              const success = chatWindowRef.value.scrollToMessageByTime(
                messageInfo.send_time
              );
              if (success) {
                console.log('✅ 消息定位完成，已高亮目标消息');
                // Message.success('已定位到指定消息');
              } else {
                console.log('✅ 消息定位完成（未找到精确时间匹配）');
                // Message.success('已定位到相关聊天');
              }
            } else {
              console.log('✅ 消息定位完成');
              // Message.success('已定位到相关聊天');
            }
          }, 800); // 等待聊天记录加载完成
        }
      }, 300);
    } catch (error) {
      console.error('❌ 消息定位失败:', error);
      Message.error('定位失败，请重试');
    }
  };

  // 处理头像加载错误 - 使用默认头像
  const handleAvatarError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    const originalSrc = img.src;

    console.warn('头像加载失败:', originalSrc);

    // 避免无限循环，检查是否已经是默认头像
    if (!img.src.includes('/icons/common/avatar.png')) {
      // 使用默认头像
      img.src = '/icons/common/avatar.png';
      console.log('已切换到默认头像');
    } else {
      console.error('默认头像也加载失败，使用字母头像');
      // 如果默认头像也失败，移除src让a-avatar显示字母头像
      img.removeAttribute('src');
    }
  };

  // 搜索联想相关方法
  const handleSearchInput = async (value: string) => {
    // 清除之前的定时器
    if (searchTimer.value) {
      clearTimeout(searchTimer.value);
    }

    // 如果输入为空，隐藏联想框
    if (!value || value.trim().length === 0) {
      showSuggestions.value = false;
      customerSuggestions.value = [];
      chatSuggestions.value = [];
      totalCount.value = 0;
      return;
    }

    // 防抖处理，延迟300ms后执行搜索
    searchTimer.value = window.setTimeout(async () => {
      try {
        console.log('🔍 开始搜索联想:', value);

        const response = await request('/api/thread/chat_search', {
          ...formModel,
          content: value.trim(),
        });

        if (response && response.code === 0 && response.data) {
          const { data } = response;

          // 处理客户名称数据
          customerSuggestions.value = (data.account || []).map((item: any) => ({
            ...item,
            name: item.account_name || '未知客户',
            platform: item.platform || '未知平台',
            id: item.account_id,
            avatar: item.account_profile_photo,
          }));

          // 处理聊天记录数据
          chatSuggestions.value = (data.message || []).map((item: any) => ({
            ...item,
            content: item.message || item.content || '无内容',
            customer_name:
              item.account_name || item.customer_name || '未知客户',
            customer_avatar: item.account_profile_photo || item.avatar || '',
            time: item.send_time || item.time || '',
            record_count: item.record_count || item.count || 1,
            id: item.account_id || item.id,
          }));

          // 总数统计
          totalCount.value =
            customerSuggestions.value.length + chatSuggestions.value.length ||
            0;

          // 只有在输入框仍然聚焦且有结果时才显示联想框
          if (isInputFocused.value && totalCount.value > 0) {
            showSuggestions.value = true;
          }

          console.log('✅ 搜索联想结果:', {
            客户数量: customerSuggestions.value.length,
            聊天记录数量: chatSuggestions.value.length,
            总数: totalCount.value,
          });
        } else {
          // 没有结果时隐藏联想框
          showSuggestions.value = false;
          customerSuggestions.value = [];
          chatSuggestions.value = [];
          totalCount.value = 0;
        }
      } catch (error) {
        console.error('❌ 搜索联想失败:', error);
        // 出错时隐藏联想框
        showSuggestions.value = false;
        customerSuggestions.value = [];
        chatSuggestions.value = [];
        totalCount.value = 0;
      }
    }, 300);
  };

  // 处理输入框聚焦
  const handleInputFocus = () => {
    isInputFocused.value = true;
    // 如果有搜索内容且有结果，显示联想框
    if (formModel.id && totalCount.value > 0) {
      showSuggestions.value = true;
    }
  };

  // 处理输入框失焦 - a-popover 版本
  const handleInputBlur = () => {
    isInputFocused.value = false;
  };

  // 选择联想项

  const selectSuggestion = async (info: any, type: 'customer' | 'chat') => {
    console.log('✅ 选择联想项:', { info, type });

    if (type === 'customer') {
      // 选择客户时，打开对应的会话窗口
      // 1、先选中账号
      await accountListRef.value?.selectAccount(info.self_account_id);
      setTimeout(async () => {
        console.log('账号已选中，等待消息列表更新');
        // 2、再选中消息
        await messageListRef.value?.selectMessage(info.account_id);
        showSuggestions.value = false;
      }, 200);
    } else if (type === 'chat') {
      // 选择聊天记录时，打开聊天记录详情弹窗
      console.log('🔍 打开聊天记录详情弹窗:', info);
      chatRecordKeyword.value = formModel.content;
      chatRecordDialogVisible.value = true;
      showSuggestions.value = false;
      chatRecordDialogRef.value?.openDialog(chatRecordKeyword.value, info);
    }
    formModel.content = '';
  };

  // 移除重复的消息发送处理函数，统一在 handleSendMessage 中处理
  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    // 隐藏联想框
    showSuggestions.value = true;
    handleSearchInput(formModel.content);
  };

  // 监听选中账号变化，确保头像实时更新和状态重置
  watch(
    () => selectedAccount.value,
    (newAccount, oldAccount) => {
      if (newAccount !== oldAccount) {
        // 重置选中的消息状态，确保账号切换时清空之前的选中状态
        selectedMessage.value = null;

        if (newAccount) {
          console.log('账号切换检测到:', {
            新账号: newAccount.name,
            新头像: newAccount.avatar,
            旧账号: oldAccount?.name,
          });
          console.log('✅ 已重置选中消息状态，确保状态一致性');
        } else {
          console.log('账号已清空，重置所有相关状态');
        }
      }
    },
    { deep: true }
  );

  // 生命周期
  onMounted(() => {
    // 初始化数据 - 账号列表现在由 MediaAccountList 组件自动加载
    console.log('线索管理界面初始化开始');
  });

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (searchTimer.value) {
      clearTimeout(searchTimer.value);
    }
  });

  defineExpose({
    handleLocateMessage,
  });
</script>

<style scoped lang="less">
  .main-container {
    margin-top: 10px;
    .table-card {
      margin-bottom: 10px;
    }
  }
  .clue-management {
    height: 100vh;
    background: var(--color-bg-1);
    font-family: var(--font-family);
    overflow: hidden; // 禁用页面级别滚动
    height: calc(100vh - 205px);
    border-radius: var(--border-radius-large);
  }

  // 桌面端三栏布局
  .desktop-layout {
    display: flex;
    height: 100%;
    gap: 1px;
    background: var(--color-border-1);

    // 左侧账号列表 - 固定 80px
    .account-sidebar {
      background: var(--color-bg-2);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }

  .search-suggestions-content {
    max-height: 400px;
    overflow-y: auto;
    min-width: 260px;
    min-height: 50px;
    background: var(--color-bg-2);
    border-radius: 8px;

    .suggestion-section {
      &:not(:first-child) {
        margin-top: 6px;
      }

      .suggestion-title {
        padding: 4px 8px;
        font-size: 12px;
        font-weight: 400;
        color: var(--color-text-3);
        background: transparent;
        line-height: 1.2;
      }

      .suggestion-item {
        padding: 4px 6px;
        cursor: pointer;
        transition: background-color 0.15s ease;
        margin: 6px 4px;
        border-radius: 6px;
        display: flex;
        align-items: flex-start;
        gap: 12px;

        &:hover {
          background: rgba(0, 0, 0, 0.04);
        }

        .suggestion-avatar {
          flex-shrink: 0;
          margin-top: 2px;
        }

        .suggestion-content {
          flex: 1;
          min-width: 0;

          .suggestion-name {
            font-size: 14px;
            font-weight: 400;
            color: var(--color-text-1);
            margin-bottom: 3px;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .suggestion-count {
            font-size: 11px;
            color: var(--color-text-3);
            margin-bottom: 4px;
            line-height: 1.2;
          }

          .suggestion-text {
            font-size: 12px;
            color: var(--color-text-2);
            margin-bottom: 4px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
          }

          .suggestion-meta {
            font-size: 11px;
            color: var(--color-text-3);
            line-height: 1.2;
          }
        }
      }

      // 客户名称项样式
      .customer-item {
        .suggestion-content {
          .suggestion-meta {
            margin-top: 2px;
          }
        }
      }

      // 聊天记录项样式
      .chat-item {
        .suggestion-content {
          .suggestion-count {
            font-weight: 400;
          }
        }
      }
    }

    .suggestion-footer {
      margin-top: 6px;
      padding: 8px 0 4px 0;
      text-align: center;
    }

    // 微信风格滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      transition: background-color 0.15s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.15);
      }
    }

    // 确保滚动条不占用内容空间
    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }
</style>

<!--
  线索详情面板组件 - 独立的客户信息展示和编辑模块
  支持客户标注、获客工具、用户信息展示、表单编辑等功能
  遵循Arco Design设计规范，提供优雅的交互体验
-->
<template>
  <div class="clue-detail-panel">
    <!-- 面板头部 - 标签页和关闭按钮 -->
    <div class="panel-header"> 客资 </div>

    <!-- 面板内容区域 -->
    <div class="panel-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin tip="正在加载线索详情...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-content">
          <icon-exclamation class="error-icon" />
          <span class="error-text">{{ error }}</span>
          <a-button
            size="mini"
            type="primary"
            @click="fetchClueDetail(selectedMessage?.clue_id)"
          >
            重试
          </a-button>
        </div>
      </div>

      <!-- 正常内容 -->
      <template v-else>
        <!-- 客户标注标签页 -->
        <!-- 用户基本信息卡片 -->
        <div class="user-info-card">
          <div class="user-profile">
            <a-avatar
              :size="56"
              :style="
                getAvatarStyle(
                  clueDetailInfo?.username ||
                    selectedMessage?.username ||
                    '用户'
                )
              "
              class="user-avatar"
            >
              <img
                v-if="selectedMessage?.avatar"
                :src="selectedMessage?.avatar"
                alt=""
              />
              <span v-else>
                {{
                  (
                    clueDetailInfo?.username || selectedMessage?.username
                  )?.charAt(0) || '用'
                }}
              </span>
            </a-avatar>
            <div class="user-details">
              <div class="username">{{
                clueDetailInfo?.username || selectedMessage?.username || '-'
              }}</div>
              <div class="user-tags">
                <span class="tag-item">{{ clueDetailInfo?.status_two }}</span>
              </div>
            </div>
          </div>

          <!-- 基本信息列表 -->
          <div class="contact-info">
            <div class="contact-item">
              <span class="item-label">线索ID</span>
              <span class="item-value">{{
                clueDetailInfo?.clue_uniq_id || '-'
              }}</span>
            </div>
            <div class="contact-item">
              <span class="item-label">公海线索ID</span>
              <span class="item-value">{{
                clueDetailInfo?.thread_seas_id || '-'
              }}</span>
              <a-button
                type="text"
                size="mini"
                class="copy-button"
                @click="handleCopy(clueDetailInfo?.seas_id || '')"
              >
                <template #icon>
                  <icon-copy />
                </template>
              </a-button>
            </div>
            <div class="contact-item">
              <span class="item-label">用户账号ID</span>
              <span class="item-value">{{
                clueDetailInfo?.account_id || '-'
              }}</span>
              <a-button
                type="text"
                size="mini"
                class="copy-button"
                @click="handleCopy(clueDetailInfo?.account_id || '')"
              >
                <template #icon>
                  <icon-copy />
                </template>
              </a-button>
            </div>
            <div class="contact-item">
              <span class="item-label">手机号</span>
              <template v-if="editPhoneMode">
                <a-input
                  v-model="editPhone"
                  size="small"
                  placeholder="请输入"
                  style="width: 120px; margin-right: 8px"
                />
                <a-button
                  type="primary"
                  size="mini"
                  :loading="editLoading"
                  @click="saveContactEdit"
                  >保存</a-button
                >
                <a-button type="text" size="mini" @click="cancelEditContact"
                  >取消</a-button
                >
              </template>
              <template v-else>
                <span class="item-value">{{
                  clueDetailInfo?.phone || '-'
                }}</span>
                <a-button
                  type="text"
                  size="mini"
                  class="copy-button"
                  @click="handleCopy(clueDetailInfo?.phone || '')"
                >
                  <template #icon><icon-copy /></template>
                </a-button>
                <a-button
                  type="text"
                  size="mini"
                  @click="startEditContact('phone')"
                >
                  <template #icon><icon-edit /></template>
                </a-button>
              </template>
            </div>
            <div class="contact-item">
              <span class="item-label">微信</span>
              <template v-if="editWechatMode">
                <a-input
                  v-model="editWechat"
                  size="small"
                  placeholder="请输入"
                  style="width: 120px; margin-right: 8px"
                />
                <a-button
                  type="primary"
                  size="mini"
                  :loading="editLoading"
                  @click="saveContactEdit"
                  >保存</a-button
                >
                <a-button type="text" size="mini" @click="cancelEditContact"
                  >取消</a-button
                >
              </template>
              <template v-else>
                <span class="item-value">{{
                  clueDetailInfo?.wechat || '-'
                }}</span>
                <a-button
                  type="text"
                  size="mini"
                  class="copy-button"
                  @click="handleCopy(clueDetailInfo?.wechat || '')"
                >
                  <template #icon><icon-copy /></template>
                </a-button>
                <a-button
                  type="text"
                  size="mini"
                  @click="startEditContact('wechat')"
                >
                  <template #icon><icon-edit /></template>
                </a-button>
              </template>
            </div>
          </div>
        </div>

        <!-- 线索详情信息区域 -->
        <div class="clue-detail-section">
          <div class="section-header">
            <icon-link class="header-icon" />
            <span class="header-title">线索详情</span>
          </div>

          <div class="detail-info">
            <div class="detail-item">
              <span class="detail-label">来源媒体</span>
              <span class="detail-value">{{
                clueDetailInfo?.platform || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">来源账号</span>
              <span class="detail-value">{{
                clueDetailInfo?.from_account_id || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">账号类型</span>
              <span class="detail-value">{{
                clueDetailInfo?.source || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">关注产品</span>
              <span class="detail-value">{{
                clueDetailInfo?.area || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">触发关键词</span>
              <span class="detail-value">
                {{
                  clueDetailInfo?.source === '关键词'
                    ? clueDetailInfo?.from_setting || '-'
                    : '-'
                }}</span
              >
            </div>
            <div class="detail-item">
              <span class="detail-label">线索内容</span>
              <span class="detail-value">{{
                clueDetailInfo?.thread_content || '-'
              }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">获取时间</span>
              <span class="detail-value">{{
                clueDetailInfo?.add_time || '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 跟进信息区域 -->
        <div class="follow-section">
          <div class="section-header">
            <icon-user class="header-icon" />
            <span class="header-title">跟进信息</span>
          </div>

          <div class="follow-info">
            <div class="follow-item">
              <span class="follow-label">跟进人员</span>
              <span class="follow-value">{{
                clueDetailInfo?.two_service_user || '-'
              }}</span>
            </div>
            <div class="follow-item">
              <span class="follow-label">线索阶段</span>
              <span class="follow-value">{{
                clueDetailInfo?.status || '-'
              }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconClose,
    IconUser,
    IconTool,
    IconQuestionCircle,
    IconCopy,
    IconEye,
    IconLink,
    IconLocation,
    IconRight,
    IconExclamation,
    IconEdit,
  } from '@arco-design/web-vue/es/icon';
  import type {
    AccountInfo,
    MessageItem,
  } from '@/views/travel-clue/clue-management/types';
  import request from '@/api/request';

  // 线索详情数据接口定义
  interface ClueDetailInfo {
    id: number; // 线索ID
    seas_id: string; // 公海线索ID
    username: string; // 用户名
    user_account_id: string; // 用户账号ID
    phone: string; // 手机号
    wechat: string; // 微信
    source_media: string; // 来源媒体
    source_account: string; // 来源账号
    account_type: string; // 账号类型
    focus_product: string; // 关注产品
    trigger_keyword: string; // 触发关键词
    clue_content: string; // 线索内容
    get_time: string; // 获取时间
    follow_user: string; // 跟进人员
    clue_stage: string; // 线索阶段
  }

  // 组件 Props 接口定义
  interface ClueDetailPanelProps {
    selectedMessage?: MessageItem | null; // 当前选中的消息
    selectedAccount?: AccountInfo | null; // 当前选中的账号
    visible?: boolean; // 面板显示状态
  }

  // 组件 Events 接口定义
  interface ClueDetailPanelEmits {
    (event: 'close'): void; // 关闭面板
    (event: 'update:customerTag', value: string): void; // 更新客户标签
    (event: 'update:phoneNumber', value: string): void; // 更新手机号码
    (event: 'update:wechatAccount', value: string): void; // 更新微信账号
    (event: 'update:location', value: string): void; // 更新地域信息
    (event: 'update:remarks', value: string): void; // 更新备注信息
    (event: 'tabChange', tab: string): void; // 标签页切换
  }

  // 定义 Props 和 Emits
  const props = withDefaults(defineProps<ClueDetailPanelProps>(), {
    selectedMessage: null,
    selectedAccount: null,
    visible: true,
  });

  const emit = defineEmits<ClueDetailPanelEmits>();

  // 当前激活的标签页
  const activeTab = ref('customer');

  // 标签页配置
  const tabs = [
    { key: 'customer', label: '客户标注', icon: IconUser },
    { key: 'tools', label: '获客工具', icon: IconTool },
  ];

  // 线索详情数据状态
  const clueDetailInfo = ref<ClueDetailInfo | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 获取线索详情信息
  const fetchClueDetail = async (clueId: string | number) => {
    if (!clueId) {
      console.warn('线索ID为空，无法获取详情');
      clueDetailInfo.value = null;
      return;
    }

    // 立即清空之前的数据，避免显示旧数据
    clueDetailInfo.value = null;
    loading.value = true;
    error.value = null;

    try {
      console.log('🔄 开始获取线索详情...', { clue_id: clueId });

      const response = await request('/api/thread/clue_detail', {
        id: clueId,
      });

      if (response && response.code === 0 && response.data) {
        clueDetailInfo.value = response.data;
        console.log('✅ 线索详情获取成功:', response.data);
      } else {
        throw new Error(response?.msg || '获取线索详情失败');
      }
    } catch (err) {
      console.error('❌ 获取线索详情失败:', err);
      error.value = '获取线索详情失败，请重试';
      Message.error('获取线索详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 生成用户头像背景色 - 参考飞书设计风格
  const getAvatarStyle = (username: string) => {
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ];

    let hash = 0;
    for (let i = 0; i < username.length; i += 1) {
      hash = username.charCodeAt(i) + (hash * 31 - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;

    return {
      backgroundColor: colors[colorIndex],
      color: '#FFFFFF',
      fontWeight: '500',
    };
  };

  // 事件处理函数
  const handleClose = () => {
    emit('close');
  };

  const handleTabChange = (tab: string) => {
    activeTab.value = tab;
    emit('tabChange', tab);
  };

  /**
   * 增强的复制功能 - 支持多种复制方法的降级策略
   * 1. 优先使用现代的navigator.clipboard API
   * 2. 降级到document.execCommand('copy')
   * 3. 提供手动复制的提示
   * @param text 要复制的文本内容
   */
  const handleCopy = async (text: string) => {
    // 如果文本为空，则不执行复制操作
    if (!text || text === '-') {
      Message.warning('没有可复制的内容');
      return;
    }

    try {
      // 方法1: 尝试使用现代的Clipboard API (需要HTTPS或localhost环境)
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        Message.success('复制成功');
        return;
      }

      // 方法2: 降级到传统的document.execCommand方法
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // 设置样式使元素不可见
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      // 选择文本并执行复制命令
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        Message.success('复制成功');
      } else {
        throw new Error('execCommand复制失败');
      }
    } catch (err) {
      console.error('复制失败:', err);

      // 提供更详细的错误信息和替代方案
      if (err instanceof DOMException && err.name === 'NotAllowedError') {
        // 用户权限被拒绝的情况
        Message.error({
          content: '复制失败: 浏览器权限被拒绝，请手动复制',
          duration: 3000,
        });
      } else if (err instanceof DOMException && err.name === 'SecurityError') {
        // 安全策略限制（如非HTTPS环境）
        Message.error({
          content: '复制失败: 安全限制，请使用HTTPS或手动复制',
          duration: 3000,
        });
      } else {
        // 其他未知错误
        Message.error({
          content: '复制失败，请手动选择并复制',
          duration: 3000,
        });
      }

      // 显示可供用户手动复制的文本提示
      Message.info({
        content: `请手动复制: ${text}`,
        duration: 5000,
      });
    }
  };

  const handlePlatformClick = () => {
    Message.info('跳转到小红书主页');
  };

  const togglePhoneVisibility = () => {
    Message.info('切换手机号显示状态');
  };

  const toggleWechatVisibility = () => {
    Message.info('切换微信号显示状态');
  };

  // 编辑手机号/微信相关状态
  const editPhoneMode = ref(false);
  const editWechatMode = ref(false);
  const editPhone = ref('');
  const editWechat = ref('');
  const editLoading = ref(false);

  function startEditContact(type: 'phone' | 'wechat') {
    if (!clueDetailInfo.value) return;
    if (type === 'phone') {
      editPhoneMode.value = true;
      editPhone.value = clueDetailInfo.value.phone || '';
    } else {
      editWechatMode.value = true;
      editWechat.value = clueDetailInfo.value.wechat || '';
    }
  }

  function cancelEditContact() {
    editPhoneMode.value = false;
    editWechatMode.value = false;
    editPhone.value = '';
    editWechat.value = '';
  }

  async function saveContactEdit() {
    if (!clueDetailInfo.value) return;
    editLoading.value = true;
    try {
      const params = {
        id: clueDetailInfo.value.id,
        phone: editPhoneMode.value
          ? editPhone.value
          : clueDetailInfo.value.phone,
        wechat: editWechatMode.value
          ? editWechat.value
          : clueDetailInfo.value.wechat,
      };
      await request('/api/clue/updClueContact', params);
      Message.success('保存成功');
      editPhoneMode.value = false;
      editWechatMode.value = false;
      fetchClueDetail(clueDetailInfo.value.id);
    } catch (e) {
      Message.error('保存失败');
    } finally {
      editLoading.value = false;
    }
  }

  // 监听选中消息变化，获取线索详情
  watch(
    () => props.selectedMessage,
    (newMessage, oldMessage) => {
      const newClueId = newMessage?.clue_id;
      const oldClueId = oldMessage?.clue_id;

      // 当消息发生变化时，立即清空之前的数据
      if (newMessage !== oldMessage) {
        clueDetailInfo.value = null;
        error.value = null;
        loading.value = false;
      }

      if (newClueId && newClueId !== oldClueId) {
        fetchClueDetail(newClueId);
      }
    },
    { immediate: true, deep: true }
  );

  // 组件挂载时获取线索详情
  onMounted(() => {
    if (props.selectedMessage?.clue_id) {
      fetchClueDetail(props.selectedMessage.clue_id);
    }
  });
</script>

<style scoped lang="less">
  // 线索详情面板 - 遵循Arco Design设计规范
  .clue-detail-panel {
    // flex: 0 0 40%; // 占据40%宽度
    background: var(--color-bg-1);
    display: flex;
    flex-direction: column;
    border-left: 1px solid var(--color-border-1);
    // width: 220px;
    flex: 1;
    height: 100%;

    // 面板头部样式
    .panel-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid var(--color-border-1);
      background: var(--color-bg-2);
      flex-shrink: 0;
      height: 40px;
      font-weight: bold;

      .header-tabs {
        display: flex;
        gap: 24px;

        .tab-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 500;
          color: var(--color-text-2);
          cursor: pointer;
          padding: 8px 0;
          border-bottom: 2px solid transparent;
          transition: all 0.2s ease;

          .tab-icon {
            font-size: 16px;
          }

          &.active {
            color: var(--color-primary-6);
            border-bottom-color: var(--color-primary-6);

            .tab-icon {
              color: var(--color-primary-6);
            }
          }

          &:hover:not(.active) {
            color: var(--color-text-1);
            transform: translateY(-1px);
          }
        }
      }

      .close-button {
        color: var(--color-text-3);
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          color: var(--color-text-1);
          background: var(--color-fill-2);
          transform: scale(1.05);
        }
      }
    }

    // 面板内容区域
    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 0px 12px;

      // 隐藏滚动条但保持滚动功能
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }

      .tab-content {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      // 加载状态样式
      .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 48px 16px;
        min-height: 200px;

        .loading-placeholder {
          width: 100%;
          height: 100px;
        }
      }

      // 错误状态样式
      .error-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 48px 16px;
        min-height: 200px;

        .error-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          text-align: center;

          .error-icon {
            font-size: 32px;
            color: var(--color-danger-6);
          }

          .error-text {
            font-size: 14px;
            color: var(--color-text-2);
            line-height: 1.4;
            max-width: 200px;
          }
        }
      }

      // 用户信息卡片样式
      .user-info-card {
        background: var(--color-bg-2);
        border-radius: 12px;
        padding: 16px;
        // border: 1px solid var(--color-border-1);
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--color-border-2);
        }

        .user-profile {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 16px;

          .user-avatar {
            flex-shrink: 0;
            border: 2px solid var(--color-bg-1);
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.05);
            }
          }

          .user-details {
            flex: 1;

            .username {
              font-size: 16px;
              font-weight: 600;
              color: var(--color-text-1);
              margin-bottom: 8px;
              line-height: 1.4;
            }

            .user-tags {
              display: flex;
              gap: 6px;
              flex-wrap: wrap;

              .tag-item {
                padding: 2px 8px;
                background: var(--color-success-light-1);
                color: var(--color-success-6);
                border-radius: 12px;
                font-size: 11px;
                font-weight: 500;
                line-height: 1.2;
                border: 1px solid var(--color-success-3);
              }
            }

            .location {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
              color: var(--color-text-3);

              .location-icon {
                font-size: 14px;
                color: var(--color-text-3);
              }

              .platform-link {
                margin-left: 8px;
                font-size: 12px;
                display: flex;
                align-items: center;
                gap: 2px;
                transition: all 0.2s ease;

                .link-arrow {
                  font-size: 10px;
                  transition: transform 0.2s ease;
                }

                &:hover .link-arrow {
                  transform: translateX(2px);
                }
              }
            }
          }
        }

        .contact-info {
          .contact-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 0;
            border-bottom: 1px solid var(--color-border-1);
            transition: all 0.2s ease;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: var(--color-fill-1);
              margin: 0 -12px;
              padding-left: 12px;
              padding-right: 12px;
              border-radius: 6px;
            }

            .item-label {
              font-size: 12px;
              color: var(--color-text-3);
              min-width: 60px;
              font-weight: 500;
            }

            .item-value {
              flex: 1;
              font-size: 11px;
              color: var(--color-text-1);
              margin: 0 8px;
              word-break: break-all;
              line-height: 1.4;
            }

            .copy-button {
              color: var(--color-text-3);
              transition: all 0.2s ease;

              &:hover {
                color: var(--color-primary-6);
                transform: scale(1.1);
              }
            }
          }
        }
      }

      // 区域标题样式
      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 600;
        color: var(--color-text-1);
        padding-bottom: 8px;
        border-bottom: 1px solid var(--color-border-1);

        .header-icon {
          font-size: 16px;
          color: var(--color-primary-6);
        }

        .header-title {
          flex: 1;
        }

        .help-icon {
          font-size: 14px;
          color: var(--color-text-3);
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            color: var(--color-primary-6);
            transform: scale(1.1);
          }
        }
      }

      // 线索详情区域样式
      .clue-detail-section {
        padding: 10px;
        .detail-info {
          .detail-item {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid var(--color-border-1);
            transition: all 0.2s ease;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: var(--color-fill-1);
              margin: 0 -12px;
              padding-left: 12px;
              padding-right: 12px;
              border-radius: 6px;
            }

            .detail-label {
              font-size: 12px;
              color: var(--color-text-3);
              min-width: 80px;
              font-weight: 500;
              flex-shrink: 0;
            }

            .detail-value {
              flex: 1;
              font-size: 12px;
              color: var(--color-text-1);
              margin-left: 16px;
              line-height: 1.4;
              word-break: break-all;
            }
          }
        }
      }

      // 跟进信息区域样式
      .follow-section {
        padding: 10px;
        .follow-info {
          .follow-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid var(--color-border-1);
            transition: all 0.2s ease;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: var(--color-fill-1);
              margin: 0 -12px;
              padding-left: 12px;
              padding-right: 12px;
              border-radius: 6px;
            }

            .follow-label {
              font-size: 12px;
              color: var(--color-text-3);
              min-width: 80px;
              font-weight: 500;
            }

            .follow-value {
              flex: 1;
              font-size: 12px;
              color: var(--color-text-1);
              margin-left: 16px;
              line-height: 1.4;
            }
          }
        }
      }

      // 获客工具占位样式
      .tools-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px 16px;
        text-align: center;
        color: var(--color-text-3);

        .placeholder-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.5;
          color: var(--color-text-4);
        }

        .placeholder-text {
          font-size: 14px;
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }

  // 响应式设计优化
  @media (max-width: 768px) {
    .clue-detail-panel {
      min-width: 280px;
      max-width: 100%;

      .panel-header {
        padding: 8px 12px;

        .header-tabs {
          gap: 16px;

          .tab-item {
            font-size: 13px;
          }
        }
      }

      .panel-content {
        padding: 12px;

        .user-info-card {
          padding: 12px;

          .user-profile {
            .user-avatar {
              width: 48px;
              height: 48px;
            }

            .user-details .username {
              font-size: 15px;
            }
          }
        }
      }
    }
  }

  // 动画效果优化
  @media (prefers-reduced-motion: reduce) {
    .clue-detail-panel * {
      transition: none !important;
      animation: none !important;
      transform: none !important;
    }
  }
</style>

<!--
  媒体账号列表组件
  功能：展示媒体账号列表、支持选择、未读消息显示、平台角标等、可展开详情面板

  心跳检测功能特性：
  - 5秒间隔轮询账号列表API，实时更新未读消息角标
  - 心跳检测时只更新未读消息数量，不影响其他账号状态和界面显示
  - 组件挂载后自动启动心跳检测，卸载时自动清理定时器
  - 网络异常时静默重试，不显示错误提示，避免干扰用户体验
  - 与聊天组件心跳检测协调工作，确保消息状态的一致性
-->
<template>
  <div class="media-account-list" :class="{ expanded: isExpanded }">
    <!-- 加载状态 -->
    <div v-if="loading" class="account-loading">
      <a-spin />
      <span class="loading-text">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="account-error">
      <div class="error-content">
        <icon-exclamation-circle class="error-icon" />
        <span class="error-text">{{ error }}</span>
        <a-button size="mini" type="primary" @click="handleRetry">
          重试
        </a-button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="accounts.length === 0" class="account-empty">
      <div class="empty-content">
        <!-- 可爱的空状态图标 -->
        <div class="empty-icon">
          <div class="whale-container">
            <!-- 蓝色鲸鱼插图 -->
            <div class="whale">
              <div class="whale-body">
                <div class="whale-eyes">
                  <div class="eye left"></div>
                  <div class="eye right"></div>
                </div>
                <div class="whale-mouth"></div>
                <div class="whale-fin"></div>
              </div>
              <div class="whale-tail"></div>
              <!-- 水花效果 -->
              <div class="water-spout">
                <div class="water-drop"></div>
                <div class="water-drop"></div>
                <div class="water-drop"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 友好的提示文字 -->
        <div class="empty-text">
          <h4 class="empty-title">暂无账号</h4>
        </div>

        <!-- 操作按钮 -->
        <div class="empty-actions">
          <a-button size="small" type="text" @click="handleRetry">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </div>
      </div>
    </div>

    <!-- 账号列表 -->
    <div v-else class="account-list">
      <!-- 标题栏 -->
      <div class="account-list-header">
        <span></span>
        <span class="header-title">账号</span>
        <a-button
          type="text"
          size="mini"
          class="expand-button"
          @click="toggleExpanded"
        >
          <template #icon>
            <icon-menu-unfold v-if="!isExpanded" />
            <icon-menu-fold v-else />
          </template>
        </a-button>
      </div>

      <!-- 账号项列表 -->
      <div class="account-items">
        <div
          v-for="account in accounts"
          :key="account.id"
          :class="[
            'account-item',
            { active: selectedAccount?.id === account.id },
          ]"
          @click="handleAccountSelect(account)"
        >
          <!-- 自定义未读消息徽章 -->
          <div class="custom-badge-container">
            <div
              v-if="account.unreadCount > 0"
              class="custom-unread-badge"
              :class="{
                'high-count': account.unreadCount > 9,
                'selected-account': selectedAccount?.id === account.id,
              }"
            >
              {{ account.unreadCount > 99 ? '99+' : account.unreadCount }}
            </div>
            <div class="avatar-container">
              <a-avatar :size="42" @error="handleAvatarError">
                <img
                  :src="account.avatar"
                  :alt="account.name"
                  @error="handleAvatarError"
                />
              </a-avatar>
              <!-- 平台角标 - 使用真实图标 -->
              <div
                class="platform-badge"
                :class="`platform-${account.platform.replace(
                  /[^a-zA-Z0-9\u4e00-\u9fa5]/g,
                  ''
                )}`"
              >
                <img
                  :src="getPlatformIcon(account.platform)"
                  :alt="account.platform"
                  class="platform-icon-img"
                  @error="handlePlatformIconError"
                />
                <!-- 备用 Emoji 图标，当图片加载失败时显示 -->
                <span
                  class="platform-icon-fallback"
                  :style="{ display: 'none' }"
                >
                  {{ getPlatformIconFallback(account.platform) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 展开状态下显示的账号详情 -->
          <div class="account-details">
            <div class="account-name">{{ account.name }}</div>
            <div class="account-platform">{{ account.self_account_id }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconMessage,
    IconExclamationCircle,
    IconRefresh,
    IconRight,
    IconDown,
  } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';
  import type { AccountInfo } from '../types';

  // Props 定义
  interface Props {
    selectedAccount?: AccountInfo | null;
    autoLoad?: boolean; // 是否自动加载数据
  }

  const props = withDefaults(defineProps<Props>(), {
    selectedAccount: null,
    autoLoad: true,
  });

  // Events 定义
  interface Emits {
    (e: 'accountSelect', account: AccountInfo): void;
    (e: 'accountsLoaded', accounts: AccountInfo[]): void;
    (e: 'loadError', error: string): void;
  }

  const emit = defineEmits<Emits>();

  // 响应式数据
  const accounts = ref<AccountInfo[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const isExpanded = ref(false); // 控制展开/收起状态

  // 心跳检测相关
  const heartbeatTimer = ref<number | null>(null);
  const HEARTBEAT_INTERVAL = 2000; // 5秒间隔
  const isHeartbeatActive = ref(false); // 心跳检测状态标识

  // 新消息提醒相关
  const originalTitle = ref<string>(''); // 保存原始标题
  const titleBlinkTimer = ref<number | null>(null); // 标题闪烁定时器
  const isShowingNewMessageTitle = ref(false); // 当前是否显示新消息标题
  const hasNewMessage = ref(false); // 是否有新消息
  const newMessageStartTime = ref<number>(0); // 新消息开始时间
  const minDisplayDuration = 200000; // 最少显示10秒（毫秒）
  const clearReminderTimer = ref<number | null>(null); // 延迟清除提醒的定时器

  // 从 localStorage 读取提醒配置
  const getNotificationSettings = () => {
    try {
      const settingsStr = localStorage.getItem(
        'message-list-notification-settings'
      );
      if (settingsStr) {
        const settings = JSON.parse(settingsStr);
        // 验证数据结构的完整性，确保包含所有必需的字段
        if (
          typeof settings === 'object' &&
          typeof settings.newMessage === 'boolean' &&
          typeof settings.tabBlink === 'boolean'
        ) {
          return {
            newMessage: settings.newMessage,
            tabBlink: settings.tabBlink,
          };
        }
        console.warn('⚠️ localStorage 中的通知设置格式不正确，使用默认设置');
      }
      // 使用默认设置：新消息提醒开启，标签栏闪烁关闭
      return { newMessage: true, tabBlink: false };
    } catch (err) {
      console.warn('❌ 读取提醒配置失败:', err);
      return { newMessage: true, tabBlink: false };
    }
  };

  // 设置新消息标题提醒
  const setNewMessageTitle = () => {
    if (!originalTitle.value) {
      originalTitle.value = document.title;
    }

    const settings = getNotificationSettings();
    if (settings.newMessage && !isShowingNewMessageTitle.value) {
      // 使用更醒目的符号来吸引注意力
      document.title = `😊【有新的线索消息】`;
      isShowingNewMessageTitle.value = true;
      console.log('🔔 已设置增强版新消息标题提醒');
    }
  };

  // 启动标签栏闪烁提醒
  const startTitleBlink = () => {
    const settings = getNotificationSettings();

    if (!settings.tabBlink) {
      return;
    }

    // 清除现有的闪烁定时器
    if (titleBlinkTimer.value) {
      clearInterval(titleBlinkTimer.value);
    }

    if (!originalTitle.value) {
      originalTitle.value = document.title;
    }

    // 增强的新消息标题，使用更醒目的符号和格式（不包含原标题）
    const newMessageTitles = ['😊【有新的线索消息】', '🚨【有新的线索消息】'];

    let currentTitleIndex = 0;
    let blinkState = 0; // 0: 原标题, 1: 新消息标题, 2: 空标题(增强对比)
    let blinkCycle = 0; // 闪烁周期计数器
    const maxCyclesBeforePause = 6; // 快闪6次后暂停
    const pauseDuration = 4; // 暂停4个周期(约1.2秒)
    let pauseCounter = 0;

    titleBlinkTimer.value = window.setInterval(() => {
      // 如果在暂停期间
      if (pauseCounter > 0) {
        document.title = originalTitle.value;
        pauseCounter -= 1;
        return;
      }

      // 正常闪烁模式
      switch (blinkState) {
        case 0:
          // 显示原标题
          document.title = originalTitle.value;
          blinkState = 1;
          break;
        case 1:
          // 显示新消息标题（轮换不同的醒目符号）
          document.title = newMessageTitles[currentTitleIndex];
          blinkState = 2;
          break;
        case 2:
          // 显示空标题增强对比度
          document.title = '●●●发现新线索●●●';
          blinkState = 0;
          blinkCycle += 1;

          // 每完成一个完整周期，切换到下一个新消息标题样式
          if (blinkCycle % 2 === 0) {
            currentTitleIndex =
              (currentTitleIndex + 1) % newMessageTitles.length;
          }

          // 检查是否需要暂停
          if (blinkCycle >= maxCyclesBeforePause) {
            pauseCounter = pauseDuration;
            blinkCycle = 0;
          }
          break;
        default:
          // 默认情况，重置状态
          blinkState = 0;
          break;
      }
    }, 800); // 每0.3秒切换一次，更加频繁

    console.log(
      '✨ 已启动增强版标签栏闪烁提醒 (0.3秒间隔，多样化符号，快闪+暂停模式)'
    );
  };

  // 清除新消息提醒（立即清除，不检查时间）
  const clearNewMessageReminderImmediately = () => {
    // 清除延迟清除定时器
    if (clearReminderTimer.value) {
      clearTimeout(clearReminderTimer.value);
      clearReminderTimer.value = null;
    }

    // 停止标题闪烁
    if (titleBlinkTimer.value) {
      clearInterval(titleBlinkTimer.value);
      titleBlinkTimer.value = null;
    }

    // 恢复原始标题
    if (originalTitle.value) {
      document.title = originalTitle.value;
      isShowingNewMessageTitle.value = false;
    }

    // 重置新消息状态
    hasNewMessage.value = false;
    newMessageStartTime.value = 0;

    console.log('🔕 已清除新消息提醒');
  };

  // 清除新消息提醒（检查最少显示时间）
  const clearNewMessageReminder = () => {
    if (!hasNewMessage.value) {
      return; // 如果没有新消息，直接返回
    }

    const currentTime = Date.now();
    const elapsedTime = currentTime - newMessageStartTime.value;

    if (elapsedTime >= minDisplayDuration) {
      // 已经显示足够时间，立即清除
      clearNewMessageReminderImmediately();
      console.log('🔕 新消息提醒已显示足够时间，立即清除');
    } else {
      // 还没有显示足够时间，延迟清除
      const remainingTime = minDisplayDuration - elapsedTime;
      console.log(
        `⏰ 新消息提醒需要再显示 ${Math.ceil(remainingTime / 1000)} 秒后清除`
      );

      // 清除之前的延迟定时器
      if (clearReminderTimer.value) {
        clearTimeout(clearReminderTimer.value);
      }

      // 设置延迟清除定时器
      clearReminderTimer.value = window.setTimeout(() => {
        clearNewMessageReminderImmediately();
        console.log('🔕 新消息提醒延迟清除完成');
      }, remainingTime);
    }
  };

  // 处理新消息检测
  const handleNewMessageDetection = (is_has_new_msg: boolean) => {
    console.log('handleNewMessageDetection', is_has_new_msg);
    if (is_has_new_msg) {
      // 只有在之前没有新消息时才记录开始时间和启动提醒
      hasNewMessage.value = true;
      newMessageStartTime.value = Date.now(); // 记录新消息开始时间
      const settings = getNotificationSettings();

      console.log(
        '🆕 检测到新消息，提醒配置:',
        settings,
        '开始时间:',
        new Date(newMessageStartTime.value).toLocaleTimeString()
      );

      // 根据配置启用相应的提醒功能
      if (settings.newMessage) {
        setNewMessageTitle();
      }

      if (settings.tabBlink) {
        startTitleBlink();
      }
    } else if (!is_has_new_msg) {
      // 如果API返回没有新消息且当前有新消息状态，检查时间后清除提醒状态
      clearNewMessageReminder();
    }
  };

  // 事件处理函数
  const handleAccountSelect = (account: AccountInfo) => {
    emit('accountSelect', account);
    // 用户选择账号时，清除新消息提醒状态
    clearNewMessageReminder();
  };

  // 更新未读消息数量 - 心跳检测时使用，只更新数量不影响其他状态
  const updateUnreadCounts = (newAccounts: AccountInfo[]) => {
    // 创建新账号数据的映射，便于快速查找
    const newAccountsMap = new Map(
      newAccounts.map((account) => [account.self_account_id, account])
    );

    // 更新现有账号列表中的未读消息数量
    accounts.value.forEach((account) => {
      const newAccount = newAccountsMap.get(account.self_account_id);
      if (newAccount && account.unreadCount !== newAccount.unreadCount) {
        const oldCount = account.unreadCount;
        account.unreadCount = newAccount.unreadCount;

        // 只在调试模式下输出详细日志
        if (process.env.NODE_ENV === 'development') {
          console.log(
            `💓 账号 ${account.name} 未读消息数量更新: ${oldCount} -> ${newAccount.unreadCount}`
          );
        }
      }
    });
  };

  // 停止心跳检测
  const stopHeartbeat = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value);
      heartbeatTimer.value = null;
      isHeartbeatActive.value = false;
      console.log('❌ 账号列表心跳检测已停止');
    }
  };

  // 获取账号列表数据 - 从API获取真实数据
  const getAccountList = async (isHeartbeat = false) => {
    // 心跳检测时不显示加载状态，避免界面闪烁
    if (!isHeartbeat) {
      loading.value = true;
    }
    error.value = null;

    try {
      const response = await request('/api/thread/all_chat_account', {
        page: 1,
        pageSize: 1000, // 获取前50个账号用于线索管理
      });

      if (response && response.data) {
        // 是否有新消息
        let is_has_new_msg = response.data.is_has_new_msg || false;
        console.log('是否包含新消息:', is_has_new_msg);
        // 处理新消息提醒
        handleNewMessageDetection(is_has_new_msg);

        // 数据转换：将API数据格式转换为组件所需格式
        const apiData = response.data.list || [];
        const newAccounts = apiData.map((item: any, index: number) => {
          // 处理头像URL - 优先使用API返回的真实头像
          let avatarUrl = null;

          // 1. 优先使用API返回的avatar_url
          if (item.profile_photo && item.profile_photo.trim()) {
            avatarUrl = item.profile_photo.trim();
            if (!isHeartbeat) {
              console.log(`账号 ${item.account_name} 使用API头像:`, avatarUrl);
            }
          }

          // 2. 如果API头像为空，使用默认头像
          if (!avatarUrl) {
            avatarUrl = '/icons/common/avatar.png';
            if (!isHeartbeat) {
              console.log(`账号 ${item.account_name} 使用默认头像`);
            }
          }

          // 处理在线状态映射
          const statusMap: { [key: string]: string } = {
            online: 'online',
            offline: 'offline',
            busy: 'busy',
            disable: 'offline',
          };

          return {
            id: item.self_account_id || index + 1,
            self_account_id: item.self_account_id,
            name: item.account_name || '未知账号',
            platform: item.platform_text || '未知平台',
            avatar: avatarUrl,
            status: statusMap[item.online_status] || 'offline',
            unreadCount: item.unread_num || 0,
            lastActiveTime: item.send_time,
            // 保留原始API数据以备后用
            originalData: item,
          } as AccountInfo;
        });

        // 心跳检测时，只更新未读消息数量，保持其他状态不变
        if (isHeartbeat && accounts.value.length > 0) {
          updateUnreadCounts(newAccounts);
          console.log('💓 心跳检测更新未读消息数量完成');
        } else {
          // 初始加载时，完全替换账号列表
          accounts.value = newAccounts;
          console.log('账号列表加载成功:', accounts.value.length, '个账号');
          emit('accountsLoaded', accounts.value);

          // 如果没有选中账号，自动选中第一个账号
          if (
            !props.selectedAccount?.self_account_id &&
            accounts.value.length > 0
          ) {
            handleAccountSelect(accounts.value[0]);
          }
        }
      } else {
        throw new Error('API返回数据格式错误');
      }
    } catch (err) {
      console.error('获取账号列表失败:', err);
      const errorMessage = '获取账号列表失败，请重试';

      // 心跳检测失败时不显示错误消息，避免过于频繁的提示
      if (!isHeartbeat) {
        error.value = errorMessage;
        emit('loadError', errorMessage);
        Message.error(errorMessage);
        // 使用备用数据以确保界面正常显示
        accounts.value = [];
      } else {
        console.warn('💓 心跳检测失败，将在下次检测时重试');
      }
    } finally {
      if (!isHeartbeat) {
        loading.value = false;
      }
    }
  };

  // 启动心跳检测
  const startHeartbeat = () => {
    // 清除现有定时器
    stopHeartbeat();

    // 只有当账号列表不为空时才启动心跳检测
    if (accounts.value.length === 0) {
      console.log('⚠️ 账号列表为空，无法启动心跳检测');
      return;
    }

    console.log(
      '💓 启动账号列表心跳检测，间隔:',
      HEARTBEAT_INTERVAL / 1000,
      '秒'
    );
    isHeartbeatActive.value = true;

    heartbeatTimer.value = window.setInterval(() => {
      // 静默获取数据，不显示加载状态，标记为心跳检测请求
      getAccountList(true);
    }, HEARTBEAT_INTERVAL);
  };

  const handleRetry = () => {
    getAccountList();
  };

  // 切换展开/收起状态
  const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value;
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const statusMap = {
      online: '在线',
      offline: '离线',
      busy: '忙碌',
    };
    return statusMap[status] || '未知';
  };

  // 获取状态时间描述
  const getStatusTime = (status: string) => {
    const timeMap = {
      online: '刚刚活跃',
      offline: '2小时前',
      busy: '正在忙碌',
    };
    return timeMap[status] || '未知';
  };

  // 获取平台图标 - 使用真实的平台图标文件
  const getPlatformIcon = (platform: string) => {
    const iconMap = {
      小红书: '/icons/platform/小红书.png',
      抖音: '/icons/platform/抖音.png',
      微博: '/icons/platform/微博.png',
      B站: '/icons/platform/B站.png',
      快手: '/icons/platform/快手.png',
      视频号: '/icons/platform/视频号.png',
      Instagram: '/icons/platform/Instagram.png',
    };
    return iconMap[platform] || '/icons/platform/default.svg';
  };

  // 获取平台图标的备用 Emoji（当图片加载失败时使用）
  const getPlatformIconFallback = (platform: string) => {
    const iconMap = {
      小红书: '📖',
      抖音: '🎵',
      微博: '📱',
      B站: '📺',
      快手: '⚡',
      视频号: '📹',
      Instagram: '📷',
    };
    return iconMap[platform] || '📱';
  };

  // 处理头像加载错误 - 使用默认头像
  const handleAvatarError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    const originalSrc = img.src;

    console.warn('头像加载失败:', originalSrc);

    // 避免无限循环，检查是否已经是默认头像
    if (!img.src.includes('/icons/common/avatar.png')) {
      // 使用默认头像
      img.src = '/icons/common/avatar.png';
      console.log('已切换到默认头像');
    } else {
      console.error('默认头像也加载失败，使用字母头像');
      // 如果默认头像也失败，移除src让a-avatar显示字母头像
      img.removeAttribute('src');
    }
  };

  // 处理平台图标加载错误 - 显示备用 Emoji
  const handlePlatformIconError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    const fallbackSpan = img.nextElementSibling as HTMLSpanElement;

    if (img && fallbackSpan) {
      img.style.display = 'none';
      fallbackSpan.style.display = 'block';
      console.log('平台图标加载失败，已切换到备用 Emoji');
    }
  };

  // 根据 self_account_id 手动选中特定的账号
  const selectAccount = (self_account_id: any) => {
    return new Promise((resolve, reject) => {
      const account = accounts.value.find(
        (acc) => acc.self_account_id === self_account_id
      );
      if (!account) {
        console.warn('未找到对应账号:', self_account_id);
        return;
      }
      handleAccountSelect(account);
      resolve();
    });
  };

  // 暴露方法给父组件调用
  defineExpose({
    handleAvatarError,
    getAccountList,
    accounts,
    loading,
    error,
    isExpanded,
    toggleExpanded,
    selectAccount,
    startHeartbeat,
    stopHeartbeat,
    isHeartbeatActive,
    // 新消息提醒相关方法
    clearNewMessageReminder,
    clearNewMessageReminderImmediately,
    hasNewMessage,
    getNotificationSettings,
  });

  // 生命周期
  onMounted(async () => {
    // 保存原始页面标题
    originalTitle.value = document.title;
    console.log('📄 已保存原始页面标题:', originalTitle.value);

    if (props.autoLoad) {
      console.log('MediaAccountList 组件初始化，开始加载账号列表');
      await getAccountList();

      // 账号列表加载完成后，启动心跳检测
      if (accounts.value.length > 0) {
        console.log('💓 账号列表加载完成，启动心跳检测');
        startHeartbeat();
      }
    }
  });

  // 组件卸载时清理心跳检测定时器和新消息提醒
  onUnmounted(() => {
    console.log('🔥 MediaAccountList 组件销毁，清理心跳检测和新消息提醒');
    stopHeartbeat();
    clearNewMessageReminderImmediately(); // 组件销毁时立即清除，不检查时间
  });
</script>

<style scoped lang="less">
  .media-account-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 90px; // 默认宽度
    transition: width 0.2s ease-in-out; // 宽度动画效果

    // 展开状态下增加宽度
    &.expanded {
      width: 220px; // 展开后的宽度，足够显示账号详情
      transition: width 0.3s ease-in-out; // 宽度动画效果
    }

    // 账号加载状态
    .account-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px 8px;
      gap: 8px;

      .loading-text {
        font-size: 12px;
        color: var(--color-text-3);
      }
    }

    // 账号错误状态
    .account-error {
      padding: 12px 8px;

      .error-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 16px 8px;
        background: var(--color-fill-1);
        border-radius: 8px;
        text-align: center;

        .error-icon {
          font-size: 20px;
          color: var(--color-danger-6);
        }

        .error-text {
          font-size: 12px;
          color: var(--color-text-2);
          line-height: 1.4;
        }
      }
    }

    // 账号空状态
    .account-empty {
      padding: 20px 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      animation: fadeIn 0.6s ease-out;

      .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        max-width: 100%;

        .empty-icon {
          margin-bottom: 16px;
          animation: bounceIn 0.8s ease-out;

          .whale-container {
            position: relative;
            width: 48px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            // 蓝色鲸鱼插图
            .whale {
              position: relative;
              z-index: 2;

              .whale-body {
                width: 28px;
                height: 20px;
                background: linear-gradient(135deg, #42a5f5, #1976d2);
                border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .whale-eyes {
                  display: flex;
                  gap: 6px;
                  margin-bottom: 2px;
                  margin-top: -2px;

                  .eye {
                    width: 2px;
                    height: 2px;
                    background: #fff;
                    border-radius: 50%;
                    animation: blink 4s infinite;
                    position: relative;

                    &::after {
                      content: '';
                      position: absolute;
                      top: 0;
                      left: 0;
                      width: 1px;
                      height: 1px;
                      background: #333;
                      border-radius: 50%;
                    }
                  }
                }

                .whale-mouth {
                  width: 8px;
                  height: 2px;
                  border-radius: 50%;
                  background: #1565c0;
                  margin-bottom: 2px;
                }

                .whale-fin {
                  position: absolute;
                  right: -2px;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 4px;
                  height: 8px;
                  background: linear-gradient(135deg, #1976d2, #1565c0);
                  border-radius: 0 50% 50% 0;
                }
              }

              .whale-tail {
                position: absolute;
                left: -8px;
                top: 50%;
                transform: translateY(-50%);
                width: 8px;
                height: 12px;
                background: linear-gradient(135deg, #42a5f5, #1976d2);
                border-radius: 50% 0 0 50%;

                &::before {
                  content: '';
                  position: absolute;
                  right: -2px;
                  top: -2px;
                  width: 6px;
                  height: 6px;
                  background: linear-gradient(135deg, #42a5f5, #1976d2);
                  border-radius: 0 50% 0 50%;
                  transform: rotate(45deg);
                }

                &::after {
                  content: '';
                  position: absolute;
                  right: -2px;
                  bottom: -2px;
                  width: 6px;
                  height: 6px;
                  background: linear-gradient(135deg, #42a5f5, #1976d2);
                  border-radius: 50% 0 50% 0;
                  transform: rotate(45deg);
                }
              }

              .water-spout {
                position: absolute;
                top: -8px;
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                gap: 2px;
                animation: waterSpout 2s infinite ease-in-out;

                .water-drop {
                  width: 2px;
                  height: 3px;
                  background: linear-gradient(180deg, #81d4fa, #4fc3f7);
                  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
                  animation: waterDrop 2s infinite ease-in-out;

                  &:nth-child(1) {
                    animation-delay: 0s;
                  }

                  &:nth-child(2) {
                    animation-delay: 0.3s;
                  }

                  &:nth-child(3) {
                    animation-delay: 0.6s;
                  }
                }
              }
            }

            // 空盒子
            .empty-box {
              position: relative;
              z-index: 1;

              .box-top {
                width: 16px;
                height: 2px;
                background: linear-gradient(135deg, #e0e0e0, #bdbdbd);
                border-radius: 1px;
                margin-bottom: 1px;
              }

              .box-body {
                width: 18px;
                height: 12px;
                background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
                border-radius: 2px;
                border: 1px solid #bdbdbd;
                position: relative;

                &::before {
                  content: '';
                  position: absolute;
                  top: 2px;
                  left: 2px;
                  right: 2px;
                  bottom: 2px;
                  border: 1px dashed #ccc;
                  border-radius: 1px;
                }
              }
            }
          }
        }

        .empty-text {
          margin-bottom: 16px;
          animation: slideUp 0.6s ease-out 0.2s both;

          .empty-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--color-text-2);
            margin: 0 0 6px 0;
            line-height: 1.2;
          }

          .empty-subtitle {
            font-size: 11px;
            color: var(--color-text-3);
            line-height: 1.4;
            margin: 0;
            max-width: 70px;
          }
        }

        .empty-actions {
          animation: slideUp 0.6s ease-out 0.4s both;

          .arco-btn {
            font-size: 11px;
            height: 24px;
            padding: 0 8px;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
            }

            .arco-icon {
              font-size: 12px;
            }
          }
        }
      }
    }

    .account-list {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      // 标题栏样式
      .account-list-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        height: 40px;
        border-bottom: 1px solid var(--color-border-1);

        .header-title {
          font-size: 14px;
          font-weight: 600;
          color: var(--color-text-1);
        }

        .expand-button {
          padding: 4px;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            background: var(--color-fill-2);
          }

          .arco-icon {
            font-size: 14px;
            color: var(--color-text-2);
            transition: transform 0.3s ease;
          }
        }
      }

      // 账号项容器
      .account-items {
        padding: 16px 8px; // 增加上下内边距，提供更多呼吸空间
        display: flex;
        flex-direction: column;
        gap: 14px; // 从8px增加到14px，提供更舒适的账号项间距
        overflow-y: auto;
        flex: 1;
      }

      .account-item {
        position: relative;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        padding: 6px; // 从4px增加到6px，为账号项提供更多内边距
        border-radius: 12px;
        gap: 12px; // 头像和详情之间的间距
        border: 1px solid var(--color-bg-1);
        display: flex;
        justify-content: center;
        padding-left: 20px;

        &.center {
          justify-content: center;
        }

        &:hover {
          transform: scale(1.01);
          background: var(--color-fill-1);
        }

        &.active {
          transform: scale(1.01);
          background: var(--color-bg-1);
          box-shadow: 0 0 4px 1px var(--color-fill-2);
          border: 1px solid rgb(var(--primary-4));
          .account-details {
            .account-name {
              color: rgb(var(--primary-6));
              font-weight: bold;
            }
          }

          &:hover {
            transform: scale(1.02);
          }
        }

        // 账号详情样式（展开状态下显示）
        .account-details {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;
          min-width: 0; // 防止文本溢出

          .account-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--color-text-1);
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .account-platform {
            font-size: 12px;
            color: var(--color-text-3);
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        // 自定义徽章容器
        .custom-badge-container {
          position: relative;
          display: inline-block;
          flex-shrink: 0; // 防止头像被压缩

          .custom-unread-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            min-width: 18px;
            height: 18px;
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            font-size: 10px;
            font-weight: 700;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
            border: 2px solid var(--color-bg-1);
            z-index: 10;
            transform-origin: center;
            // 默认状态下不显示动画

            // 只有选中的账号才显示动画效果
            &.selected-account {
              animation: badge-bounce 2s infinite;

              // 数字动画效果
              &::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(
                  135deg,
                  rgba(255, 71, 87, 0.3),
                  rgba(255, 55, 66, 0.3)
                );
                border-radius: 12px;
                z-index: -1;
                animation: badge-glow 2s infinite;
              }
            }

            &.high-count {
              background: linear-gradient(135deg, #ff6b7a, #ff4757);

              // 高数量徽章只在选中时才有强烈动画
              &.selected-account {
                animation: badge-pulse-strong 1.5s infinite;
              }
            }
          }
        }

        // 头像容器 - 支持平台角标，优化视觉效果
        .avatar-container {
          position: relative;
          display: inline-block;
          transition: all 0.3s ease;

          // 头像样式 - 移除阴影，保持干净
          .arco-avatar {
            transition: all 0.3s ease;
          }

          // 平台角标样式 - 更大尺寸，无边框无阴影设计
          .platform-badge {
            position: absolute;
            bottom: -3px;
            right: -3px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--color-bg-1);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            overflow: hidden;

            // 平台图标图片
            .platform-icon-img {
              width: 16px;
              height: 16px;
              object-fit: cover;
              border-radius: 3px;
            }

            // 备用 Emoji 图标
            .platform-icon-fallback {
              font-size: 10px;
              line-height: 1;
              display: none;
            }

            // 不同平台的特色背景（当使用备用图标时）
            &.platform-小红书 {
              background: #ff2442;
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }

            &.platform-抖音 {
              background: #000000;
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }

            &.platform-微博 {
              background: #e6162d;
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }

            &.platform-B站 {
              background: #00a1d6;
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }

            &.platform-快手 {
              background: #ff6600;
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }

            &.platform-视频号 {
              background: #07c160;
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }

            &.platform-Instagram {
              background: linear-gradient(
                45deg,
                #f09433 0%,
                #e6683c 25%,
                #dc2743 50%,
                #cc2366 75%,
                #bc1888 100%
              );
              color: white;

              .platform-icon-fallback {
                color: white;
              }
            }
          }
        }
      }
    }
  }

  // 动画定义
  @keyframes badge-bounce {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes badge-pulse-strong {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.15);
    }
  }

  @keyframes badge-glow {
    0%,
    100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  @keyframes pulse-dot {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes blink-dot {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.3;
    }
  }

  // 空状态动画
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3) translateY(-20px);
    }
    50% {
      opacity: 1;
      transform: scale(1.05) translateY(-5px);
    }
    70% {
      transform: scale(0.95) translateY(0);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes blink {
    0%,
    90%,
    100% {
      transform: scaleY(1);
    }
    95% {
      transform: scaleY(0.1);
    }
  }

  // 鲸鱼水花动画
  @keyframes waterSpout {
    0%,
    100% {
      opacity: 0.8;
      transform: translateX(-50%) translateY(0);
    }
    50% {
      opacity: 1;
      transform: translateX(-50%) translateY(-2px);
    }
  }

  @keyframes waterDrop {
    0% {
      opacity: 0;
      transform: translateY(0) scale(0.8);
    }
    50% {
      opacity: 1;
      transform: translateY(-4px) scale(1);
    }
    100% {
      opacity: 0;
      transform: translateY(-8px) scale(0.6);
    }
  }
</style>

<!--
  消息列表组件 - 中间消息列表区域
  支持消息搜索、分页加载、消息选择等功能
  集成 API 数据获取和心跳检测机制

  新增功能：
  - 独立的通知设置状态管理，不依赖外部 props
  - 通知设置的 localStorage 持久化存储
  - 自动加载和保存设置，支持数据验证和错误处理
  - 提供完整的设置管理 API（重置、更新、清除等）
  - 组件卸载时的资源清理和内存泄漏防护
-->
<template>
  <div class="message-list-area">
    <!-- 顶部标题栏 -->
    <div class="header-bar">
      <span> </span>
      <div class="header-left">
        <span class="title">
          <!-- 平台图片 -->
          <img
            v-if="selectedAccount"
            :src="getPlatformIcon(selectedAccount.platform)"
            :alt="selectedAccount.platform"
            class="platform-icon"
          />
          {{ selectedAccount ? `${selectedAccount.name}` : '请选择账号' }}
        </span>
      </div>
      <div class="header-right">
        <!-- 未读消息统计按钮 -->
        <!-- <a-badge
          v-if="unreadCount > 0"
          :count="unreadCount"
          :max-count="99"
          class="unread-badge"
        >
          <a-button
            type="text"
            size="small"
            :loading="isNavigating"
            :disabled="unreadCount === 0"
            class="unread-nav-btn"
            @click="navigateToNextUnread"
          >
            <template #icon>
              <icon-message />
            </template>
          </a-button>
        </a-badge>
        <a-button
          v-else
          type="text"
          size="small"
          disabled
          class="unread-nav-btn disabled"
        >
          <template #icon>
            <icon-message />
          </template>
        </a-button> -->
        <!-- 设置按钮 -->
        <a-popover trigger="click" position="bottom">
          <a-button type="text" size="small">
            <template #icon>
              <icon-more-vertical />
            </template>
          </a-button>
          <template #content>
            <div class="settings-panel">
              <div class="setting-item">
                <span class="setting-label">新消息提醒</span>
                <a-switch
                  v-model="notificationSettings.newMessage"
                  size="small"
                />
              </div>
              <div class="setting-item">
                <span class="setting-label">标签栏闪烁提醒</span>
                <a-switch
                  v-model="notificationSettings.tabBlink"
                  size="small"
                />
              </div>
            </div>
          </template>
        </a-popover>
      </div>
    </div>

    <!-- 消息列表 -->
    <div ref="messageListRef" class="message-list" @scroll="handleScroll">
      <div
        v-for="message in displayedMessages"
        :key="message.id"
        :ref="(el) => setMessageRef(message.id, el)"
        :class="[
          'message-item',
          {
            active: selectedMessage?.id === message.id,
            unread: message.unread > 0,
            highlighted: highlightedMessageId === message.id,
          },
        ]"
        @click="handleMessageSelect(message)"
      >
        <!-- 消息项分割线 -->
        <a-badge :count="message.unread" :max-count="99" :offset="[-8, 8]">
          <a-avatar
            :size="40"
            class="message-avatar"
            :style="getAvatarStyle(message.username)"
          >
            <img
              v-if="message.avatar"
              :src="message.avatar"
              alt="用户头像"
              @error="handleAvatarError"
            />
            <span v-else>
              {{ message.username.charAt(0) }}
            </span>
          </a-avatar>
        </a-badge>
        <div class="message-content">
          <div class="message-header">
            <div class="username-with-tags">
              <span class="username">{{ message.username }}</span>
              <div v-if="message.tags" class="message-tags">
                <a-tag
                  v-for="tag in message.tags"
                  :key="tag"
                  size="mini"
                  :color="getTagColor(tag)"
                  class="compact-tag"
                >
                  {{ tag }}
                </a-tag>
              </div>
            </div>
            <span class="time">{{ message.time }}</span>
          </div>
          <div class="message-preview">{{ message.preview }}</div>
        </div>
      </div>

      <!-- 加载更多指示器 -->
      <div v-if="loading" class="loading-indicator">
        <a-spin />
        <span>加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-if="error && displayedMessages.length === 0" class="error-state">
        <div class="error-content">
          <icon-exclamation-circle class="error-icon" />
          <span class="error-text">{{ error }}</span>
          <a-button size="mini" type="primary" @click="handleRetry">
            重试
          </a-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!loading && !error && displayedMessages.length === 0"
        class="empty-state"
      >
        <div class="empty-content">
          <icon-message class="empty-icon" />
          <span class="empty-text">
            {{ selectedAccount ? '暂无消息' : '请选择账号查看消息' }}
          </span>
        </div>
      </div>

      <!-- 加载完成提示 -->
      <div
        v-if="!hasMore && displayedMessages.length > 0"
        class="load-complete"
      >
        <span>已加载全部消息</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    reactive,
    computed,
    watch,
    onMounted,
    onUnmounted,
    readonly,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconMore,
    IconSearch,
    IconSettings,
    IconMessage,
    IconExclamationCircle,
  } from '@arco-design/web-vue/es/icon';
  import request from '@/api/request';
  import type {
    AccountInfo,
    MessageItem,
    NotificationSettings,
    MessageListProps,
    MessageListEmits,
    ApiMessageItem,
  } from '../types/index';

  // Props 定义
  const props = withDefaults(defineProps<MessageListProps>(), {
    selectedAccount: null,
    searchKeyword: '',
  });

  // Events 定义
  const emit = defineEmits<MessageListEmits>();

  // 响应式数据
  const messageListRef = ref<HTMLElement | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null); // 错误状态
  const hasMore = ref(true);
  const pageSize = 100;
  const currentPage = ref(1);
  const selectedMessage = ref<MessageItem | null>(null);

  // 心跳检测相关
  const heartbeatTimer = ref<number | null>(null);
  const HEARTBEAT_INTERVAL = 5000; // 5秒间隔

  // 未读消息导航相关状态
  const isNavigating = ref(false);
  const currentUnreadIndex = ref(0);
  const highlightedMessageId = ref<string | null>(null);
  const messageRefs = ref<Map<string, HTMLElement>>(new Map());

  // 搜索关键词的本地副本
  const searchKeyword = ref(props.searchKeyword);

  // 通知设置的本地存储键名
  const NOTIFICATION_STORAGE_KEY = 'message-list-notification-settings';

  // 通知设置的默认值
  const defaultNotificationSettings: NotificationSettings = {
    newMessage: true,
    tabBlink: false,
  };

  /**
   * 从 localStorage 读取通知设置
   * @returns 保存的通知设置或默认设置
   */
  const loadNotificationSettings = (): NotificationSettings => {
    try {
      const saved = localStorage.getItem(NOTIFICATION_STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        // 验证数据结构的完整性，确保包含所有必需的字段
        if (
          typeof parsed === 'object' &&
          typeof parsed.newMessage === 'boolean' &&
          typeof parsed.tabBlink === 'boolean'
        ) {
          console.log('✅ 成功从 localStorage 加载通知设置:', parsed);
          return parsed;
        }
        console.warn('⚠️ localStorage 中的通知设置格式不正确，使用默认设置');
      }
    } catch (err) {
      console.error('❌ 读取通知设置失败:', err);
    }

    console.log('📝 使用默认通知设置:', defaultNotificationSettings);
    return { ...defaultNotificationSettings };
  };

  /**
   * 保存通知设置到 localStorage
   * @param settings 要保存的通知设置
   */
  const saveNotificationSettings = (settings: NotificationSettings) => {
    try {
      const settingsToSave = JSON.stringify(settings);
      localStorage.setItem(NOTIFICATION_STORAGE_KEY, settingsToSave);
      console.log('💾 通知设置已保存到 localStorage:', settings);
    } catch (err) {
      console.error('❌ 保存通知设置失败:', err);
      // 可以在这里添加用户提示，但不影响功能使用
    }
  };

  // 通知设置的独立响应式状态 - 不依赖 props
  const notificationSettings = reactive<NotificationSettings>(
    loadNotificationSettings()
  );

  // 监听通知设置变化，自动保存到 localStorage
  watch(
    notificationSettings,
    (newSettings) => {
      console.log('🔔 通知设置发生变化:', newSettings);
      saveNotificationSettings(newSettings);

      // 可选：发射事件通知父组件设置变更（保持兼容性）
      emit('notificationSettingsChange', { ...newSettings });
    },
    { deep: true }
  );

  // 监听 props 变化
  watch(
    () => props.searchKeyword,
    (newVal) => {
      searchKeyword.value = newVal;
    }
  );

  // 消息列表数据
  const messageList = ref<MessageItem[]>([]);

  // 转换 API 数据为组件所需格式
  const transformApiData = (apiData: ApiMessageItem[]): MessageItem[] => {
    return apiData.map((item) => ({
      id: item.account_id,
      username: item.account_name || '未知用户',
      avatar: item.account_profile_photo,
      preview:
        item.message_type === 'text'
          ? item.last_message || '暂无消息'
          : '[图片]',
      time: item.send_time_text,
      unread: item.unread_num || 0,
      tags: item.tags || [],
      platform: item.platform || 'unknown',
      account_id: item.account_id,
      self_account_id: item.self_account_id,
      clue_id: item.clue_id,
    }));
  };

  // 获取消息列表数据 - 从API获取真实数据
  const getMessageList = async (showLoading = true) => {
    // 只有当选中账号时才调用接口
    if (!props.selectedAccount?.self_account_id) {
      messageList.value = [];
      error.value = null;
      return;
    }

    if (showLoading) {
      loading.value = true;
    }
    error.value = null;

    try {
      console.log('开始获取消息列表，账号:', props.selectedAccount.name);

      const response = await request('/api/thread/single_account_chat_list', {
        self_account_id: props.selectedAccount.self_account_id,
        platform: props.selectedAccount?.platform,
      });

      if (response && response.data) {
        // 数据转换：将API数据格式转换为组件所需格式
        const apiData = response.data.list || response.data.data || [];
        const transformedMessages = transformApiData(apiData);

        messageList.value = transformedMessages;
        console.log(`消息列表加载成功: ${transformedMessages.length} 条消息`);

        // 发射事件通知父组件
        emit('messagesLoaded', transformedMessages);
      } else {
        throw new Error('API返回数据格式错误');
      }
    } catch (err) {
      console.error('获取消息列表失败:', err);
      const errorMessage = '获取消息列表失败，请重试';
      error.value = errorMessage;
      emit('loadError', errorMessage);

      // 不显示错误提示，避免过于频繁的消息干扰（心跳检测时）
      if (showLoading) {
        Message.error(errorMessage);
      }
    } finally {
      if (showLoading) {
        loading.value = false;
      }
    }
  };

  // 停止心跳检测
  const stopHeartbeat = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value);
      heartbeatTimer.value = null;
      console.log('心跳检测已停止');
    }
  };

  // 启动心跳检测
  const startHeartbeat = () => {
    // 清除现有定时器
    stopHeartbeat();

    // 只有当选中账号时才启动心跳检测
    if (!props.selectedAccount?.self_account_id) {
      return;
    }

    console.log('启动心跳检测，间隔:', HEARTBEAT_INTERVAL / 1000, '秒');

    heartbeatTimer.value = window.setInterval(() => {
      // 静默获取数据，不显示加载状态
      getMessageList(false);
    }, HEARTBEAT_INTERVAL);
  };

  // 重试获取数据
  const handleRetry = () => {
    getMessageList(true);
  };

  // 显示的消息列表（分页后的）
  const displayedMessages = computed(() => {
    const startIndex = 0;
    const endIndex = currentPage.value * pageSize;
    return messageList.value.slice(startIndex, endIndex);
  });

  // 未读消息统计
  const unreadCount = computed(() => {
    return displayedMessages.value.reduce((count, message) => {
      return count + (message.unread > 0 ? 1 : 0);
    }, 0);
  });

  // 未读消息列表
  const unreadMessages = computed(() => {
    return displayedMessages.value.filter((message) => message.unread > 0);
  });

  // 生成用户头像背景色 - 参考飞书设计风格
  const getAvatarStyle = (username: string) => {
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ];

    let hash = 0;
    for (let i = 0; i < username.length; i += 1) {
      hash = username.charCodeAt(i) + (hash * 31 - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;

    return {
      backgroundColor: colors[colorIndex],
      color: '#FFFFFF',
      fontWeight: '500',
    };
  };

  // 获取标签颜色
  const getTagColor = (tag: string) => {
    const colorMap: Record<string, string> = {
      咨询: 'blue',
      意向客户: 'green',
      已报名: 'orange',
      跟进中: 'purple',
      高意向: 'red',
    };
    return colorMap[tag] || 'gray';
  };

  // 设置消息元素引用
  const setMessageRef = (messageId: string, el: HTMLElement | null) => {
    if (el) {
      messageRefs.value.set(messageId, el);
    } else {
      messageRefs.value.delete(messageId);
    }
  };

  // 导航到下一条未读消息
  const navigateToNextUnread = async () => {
    if (isNavigating.value || unreadMessages.value.length === 0) return;

    isNavigating.value = true;

    try {
      // 获取当前未读消息
      const currentMessage = unreadMessages.value[currentUnreadIndex.value];

      if (!currentMessage) {
        currentUnreadIndex.value = 0;
        return;
      }

      // 获取消息元素
      const messageElement = messageRefs.value.get(currentMessage.id);

      if (messageElement && messageListRef.value) {
        // 计算滚动位置
        const containerRect = messageListRef.value.getBoundingClientRect();
        const messageRect = messageElement.getBoundingClientRect();
        const { scrollTop } = messageListRef.value;

        // 计算目标滚动位置（将消息居中显示）
        const targetScrollTop =
          scrollTop +
          messageRect.top -
          containerRect.top -
          containerRect.height / 2 +
          messageRect.height / 2;

        // 平滑滚动到目标位置
        messageListRef.value.scrollTo({
          top: Math.max(0, targetScrollTop),
          behavior: 'smooth',
        });

        // 添加高亮效果
        highlightedMessageId.value = currentMessage.id;

        // 发射导航事件
        emit('unreadNavigate', currentMessage.id);

        // 2秒后移除高亮效果
        setTimeout(() => {
          highlightedMessageId.value = null;
        }, 2000);

        // 更新索引，循环导航
        currentUnreadIndex.value =
          (currentUnreadIndex.value + 1) % unreadMessages.value.length;
      }
    } finally {
      // 延迟移除加载状态，确保滚动动画完成
      setTimeout(() => {
        isNavigating.value = false;
      }, 500);
    }
  };

  // 调用标记已读的API（可选实现）[暂未使用]
  const markMessageReadAPI = async (message: MessageItem) => {
    try {
      // 注意：这里假设后端提供了标记已读的API
      // 如果没有专门的API，可以通过其他方式实现，比如发送一个特殊的请求
      const response = await request('/api/thread/markMessageRead', {
        account_id: message.account_id,
        platform: message.platform,
        self_account_id: props.selectedAccount?.self_account_id,
      });

      if (response && response.code === 0) {
        console.log('✅ 消息已读状态同步成功');
      }
    } catch (err) {
      // 如果API不存在或调用失败，不影响用户体验
      // 心跳检测会在下次轮询时同步最新状态
      console.log('📝 标记已读API调用失败，将通过心跳检测同步状态');
      throw err;
    }
  };

  // 标记消息为已读状态
  const markMessageAsRead = (message: MessageItem) => {
    if (message.unread > 0) {
      console.log(
        `📖 标记消息为已读: ${message.username} (${message.unread} -> 0)`
      );

      // 立即更新本地状态，提供即时视觉反馈
      const originalUnread = message.unread;
      message.unread = 0;

      // 添加临时的已读反馈动画类
      const messageElement = messageRefs.value.get(message.id);
      if (messageElement) {
        messageElement.classList.add('mark-read-animation');
        setTimeout(() => {
          messageElement.classList.remove('mark-read-animation');
        }, 600);
      }

      // 可选：调用标记已读的API（如果后端提供）
      // markMessageReadAPI(message).catch((err) => {
      // console.warn('标记已读API调用失败，但本地状态已更新:', err);
      // 如果API调用失败，可以选择恢复未读状态
      // message.unread = originalUnread;
      // });
    }
  };

  // 处理消息选择
  const handleMessageSelect = (message: MessageItem) => {
    // 立即标记为已读状态，提供即时视觉反馈
    markMessageAsRead(message);

    selectedMessage.value = message;
    emit('messageSelect', message);
  };

  // 加载更多消息
  const loadMoreMessages = async () => {
    if (loading.value || !hasMore.value) return;

    loading.value = true;
    emit('loadMore');

    // 模拟加载延迟
    await new Promise<void>((resolve) => {
      setTimeout(() => resolve(), 1000);
    });

    currentPage.value += 1;

    // 模拟没有更多数据的情况
    if (currentPage.value * pageSize >= messageList.value.length) {
      hasMore.value = false;
    }

    loading.value = false;
  };

  // 滚动加载更多
  const handleScroll = async (event: Event) => {
    const target = event.target as HTMLElement;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // 滚动到底部时加载更多
    if (
      scrollTop + clientHeight >= scrollHeight - 10 &&
      !loading.value &&
      hasMore.value
    ) {
      await loadMoreMessages();
    }
  };

  // 监听选中账号变化，重新获取数据和重启心跳检测
  watch(
    () => props.selectedAccount,
    (newAccount, oldAccount) => {
      console.log('选中账号变化:', oldAccount?.name, '->', newAccount?.name);

      // 停止旧的心跳检测
      stopHeartbeat();

      // 重置状态
      currentUnreadIndex.value = 0;
      highlightedMessageId.value = null;
      selectedMessage.value = null;

      if (newAccount?.self_account_id) {
        // 获取新账号的消息列表
        getMessageList(true);
        // 启动新的心跳检测
        startHeartbeat();
      } else {
        // 清空消息列表
        messageList.value = [];
        error.value = null;
      }
    },
    { immediate: false }
  );

  // 监听未读消息变化，重置导航状态
  watch(unreadMessages, (newUnreadMessages) => {
    if (newUnreadMessages.length === 0) {
      currentUnreadIndex.value = 0;
      highlightedMessageId.value = null;
    } else if (currentUnreadIndex.value >= newUnreadMessages.length) {
      currentUnreadIndex.value = 0;
    }
  });

  // 生命周期
  onMounted(() => {
    console.log('MessageList 组件初始化完成');

    // 如果已经有选中的账号，立即获取数据并启动心跳检测
    if (props.selectedAccount?.self_account_id) {
      console.log('初始化时已有选中账号，开始获取消息列表');
      getMessageList(true);
      startHeartbeat();
    }
  });

  // 组件销毁时清理定时器和资源
  onUnmounted(() => {
    console.log('MessageList 组件销毁，开始清理资源');

    // 清理心跳检测定时器
    stopHeartbeat();

    // 清理消息元素引用，防止内存泄漏
    messageRefs.value.clear();

    // 重置高亮状态
    highlightedMessageId.value = null;

    console.log('✅ MessageList 组件资源清理完成');
  });

  // 根据 account_id 手动选中特定的账号消息
  const selectMessage = (account_id: any) => {
    const message = messageList.value.find(
      (msg) => msg.account_id === account_id
    );
    if (!message) {
      console.warn('未找到对应的消息:', account_id);
      return;
    }
    selectedMessage.value = message;
    emit('messageSelect', message);
  };

  /**
   * 重置通知设置为默认值
   */
  const resetNotificationSettings = () => {
    console.log('🔄 重置通知设置为默认值');
    Object.assign(notificationSettings, defaultNotificationSettings);
  };

  /**
   * 获取当前通知设置的副本
   * @returns 当前通知设置的副本
   */
  const getNotificationSettings = (): NotificationSettings => {
    return { ...notificationSettings };
  };

  /**
   * 更新通知设置
   * @param newSettings 新的通知设置（部分更新）
   */
  const updateNotificationSettings = (
    newSettings: Partial<NotificationSettings>
  ) => {
    console.log('🔔 更新通知设置:', newSettings);
    Object.assign(notificationSettings, newSettings);
  };

  /**
   * 清除本地存储的通知设置
   */
  const clearStoredNotificationSettings = () => {
    try {
      localStorage.removeItem(NOTIFICATION_STORAGE_KEY);
      console.log('🗑️ 已清除本地存储的通知设置');
      // 重置为默认设置
      resetNotificationSettings();
    } catch (err) {
      console.error('❌ 清除本地存储失败:', err);
    }
  };

  // 获取平台图标 - 使用真实的平台图标文件
  const getPlatformIcon = (platform: string) => {
    const iconMap = {
      小红书: '/icons/platform/小红书.png',
      抖音: '/icons/platform/抖音.png',
      微博: '/icons/platform/微博.png',
      B站: '/icons/platform/B站.png',
      快手: '/icons/platform/快手.png',
      视频号: '/icons/platform/视频号.png',
      Instagram: '/icons/platform/Instagram.png',
    };
    return iconMap[platform] || '/icons/platform/default.svg';
  };

  // 暴露组件方法和状态
  defineExpose({
    selectMessage,
    resetNotificationSettings,
    getNotificationSettings,
    updateNotificationSettings,
    clearStoredNotificationSettings,
    notificationSettings: readonly(notificationSettings), // 只读访问
  });
</script>

<style scoped lang="less">
  // 未读消息高亮脉冲动画定义 - 全局定义确保一致性
  @keyframes highlight-pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(22, 93, 255, 0.4);
    }
    50% {
      box-shadow: 0 0 0 4px rgba(22, 93, 255, 0.15);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(22, 93, 255, 0);
    }
  }

  // 标记已读动画定义 - 提供即时视觉反馈
  @keyframes mark-read-feedback {
    0% {
      background: var(--color-success-light-1);
      transform: scale(1);
    }
    50% {
      background: var(--color-success-light-2);
      transform: scale(1.02);
    }
    100% {
      background: transparent;
      transform: scale(1);
    }
  }

  // 中间消息列表区域 - 固定 350px
  .message-list-area {
    flex: 1;
    background: var(--color-bg-2);
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .header-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid var(--color-border-1);
      background: var(--color-bg-2);
      flex-shrink: 0;
      height: 40px;
      padding-right: 4px;

      .header-left {
        // flex: 1;
        .title {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          font-weight: 600;
          color: var(--color-text-1);
          .platform-icon {
            width: 14px;
            height: 14px;
            object-fit: contain;
          }
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 2px;

        // 未读消息统计按钮样式
        .unread-badge {
          .arco-badge-number {
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
            border-radius: 8px;
          }
        }

        .unread-nav-btn {
          color: var(--color-primary-6);
          transition: all 0.2s ease;

          &:hover:not(:disabled) {
            background: var(--color-primary-light-1);
            color: var(--color-primary-7);
          }

          &.disabled {
            color: var(--color-text-4);
            cursor: not-allowed;
          }

          &:disabled {
            color: var(--color-text-4);
            background: transparent;
          }
        }
      }
    }

    // 设置面板样式
    .settings-panel {
      width: 200px;
      padding: 8px 0;

      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;

        .setting-label {
          font-size: 13px;
          color: var(--color-text-1);
        }
      }
    }

    .message-list {
      flex: 1;
      overflow-y: auto;
      padding: 4px;

      .message-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 2px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        gap: 8px;

        // 消息项分割线 - 使用更淡的颜色，减少视觉干扰
        .message-divider {
          position: absolute;
          top: 0;
          left: 12px;
          right: 12px;
          height: 1px;
        }

        &:hover {
          background: var(--color-fill-1);
        }

        &.active {
          background: var(--color-primary-light-1);
          border-left: 3px solid var(--color-primary-6);
        }

        // 未读消息样式
        &.unread {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 4px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--color-primary-6);
            border-radius: 50%;
            transition: all 0.3s ease;
            opacity: 1;
          }

          .message-content {
            .username {
              font-weight: 600;
              color: var(--color-text-1);
              transition: all 0.3s ease;
            }

            .message-preview {
              color: var(--color-text-1);
              font-weight: 500;
              transition: all 0.3s ease;
            }
          }
        }

        // 已读消息样式（平滑过渡）
        &:not(.unread) {
          &::before {
            opacity: 0;
            transform: translateY(-50%) scale(0);
            transition: all 0.3s ease;
          }

          .message-content {
            .username {
              font-weight: 500;
              color: var(--color-text-2);
              transition: all 0.3s ease;
            }

            .message-preview {
              color: var(--color-text-3);
              font-weight: 400;
              transition: all 0.3s ease;
            }
          }
        }

        // 高亮消息样式（导航聚焦时）
        &.highlighted {
          background: var(--color-primary-light-1);
          border: 2px solid var(--color-primary-6);
          border-radius: 8px;
          animation: highlight-pulse 2s ease-in-out;
        }

        // 标记已读动画样式
        &.mark-read-animation {
          animation: mark-read-feedback 0.6s ease-out;
        }

        .message-avatar {
          flex-shrink: 0;
          border: 2px solid var(--color-bg-1);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          // 当没有头像图片时，显示带背景色的默认头像
          &:not([src]) {
            font-weight: 500;
            font-size: 16px;
          }
        }

        .message-content {
          flex: 1;
          min-width: 0;

          .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;

            .username-with-tags {
              display: flex;
              align-items: center;
              gap: 6px;
              flex: 1;
              min-width: 0;

              .username {
                font-weight: 500;
                color: var(--color-text-1);
                font-size: 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                flex-shrink: 0;
                width: 100%;
              }

              .message-tags {
                display: flex;
                gap: 3px;
                flex-wrap: nowrap;
                overflow: hidden;

                .compact-tag {
                  font-size: 10px !important;
                  padding: 1px 4px !important;
                  line-height: 1.2 !important;
                  border-radius: 2px !important;
                  min-width: auto !important;
                  height: 16px !important;
                  display: inline-flex !important;
                  align-items: center !important;
                  flex-shrink: 0;
                }
              }
            }

            .time {
              font-size: 11px;
              color: var(--color-text-3);
              flex-shrink: 0;
              margin-left: 8px;
            }
          }

          .message-preview {
            color: var(--color-text-2);
            font-size: 12px;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            display: block;
          }
        }

        .message-actions {
          flex-shrink: 0;
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        &:hover .message-actions {
          opacity: 1;
        }
      }

      // 加载指示器
      .loading-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
        gap: 8px;
        color: var(--color-text-3);
        font-size: 12px;
      }

      .load-complete {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
        color: var(--color-text-4);
        font-size: 12px;
      }

      // 错误状态样式
      .error-state {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 16px;
        min-height: 200px;

        .error-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          text-align: center;

          .error-icon {
            font-size: 32px;
            color: var(--color-danger-6);
          }

          .error-text {
            font-size: 14px;
            color: var(--color-text-2);
            line-height: 1.4;
            max-width: 200px;
          }
        }
      }

      // 空状态样式
      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 16px;
        min-height: 200px;

        .empty-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          text-align: center;

          .empty-icon {
            font-size: 32px;
            color: var(--color-text-4);
          }

          .empty-text {
            font-size: 14px;
            color: var(--color-text-3);
            line-height: 1.4;
          }
        }
      }
    }
  }

  .platform-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }
</style>

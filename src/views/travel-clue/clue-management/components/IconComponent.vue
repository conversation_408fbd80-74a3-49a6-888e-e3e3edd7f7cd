<!--
  简单的图标组件
  用于显示各种图标
-->
<template>
  <span :class="['icon-component', `icon-${name}`]" :style="iconStyle">
    {{ iconText }}
  </span>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  interface Props {
    name: string;
    size?: number;
    color?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    size: 16,
    color: 'currentColor',
  });

  // 图标映射表
  const iconMap: Record<string, string> = {
    menu: '☰',
    message: '💬',
    robot: '🤖',
    chart: '📊',
    user: '👤',
    more: '⋯',
    comment: '💭',
    image: '🖼️',
    emoji: '😊',
    attachment: '📎',
    gift: '🎁',
    send: '➤',
  };

  const iconText = computed(() => {
    return iconMap[props.name] || props.name;
  });

  const iconStyle = computed(() => ({
    fontSize: `${props.size}px`,
    color: props.color,
  }));
</script>

<style scoped lang="less">
  .icon-component {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    user-select: none;
    transition: all 0.2s ease;

    &.icon-send {
      transform: rotate(-45deg);
    }
  }
</style>

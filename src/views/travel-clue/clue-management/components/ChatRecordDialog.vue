<!--
  聊天记录详情弹窗组件
  功能特性：
  - 微信PC端风格的聊天记录搜索弹窗
  - 左右分栏布局：左侧用户列表，右侧消息列表
  - 支持关键词搜索和防抖处理
  - 支持消息定位跳转功能
  - 完整的状态管理（加载、空状态、错误状态）
  - 遵循 Arco Design 设计规范，无阴影效果
-->
<template>
  <d-modal
    v-model:visible="dialogVisible"
    title="聊天记录搜索"
    width="800px"
    :footer="false"
    :mask-closable="false"
    @cancel="handleClose"
    @close="handleClose"
  >
    <!-- 搜索头部区域 -->
    <div class="search-header">
      <a-input
        v-model="searchKeyword"
        placeholder="搜索聊天记录..."
        size="large"
        allow-clear
        @input="handleSearchInput"
        @keyup.enter="handleSearch"
      >
        <template #prefix>
          <icon-search />
        </template>
      </a-input>
    </div>

    <!-- 主体内容区域 -->
    <div class="dialog-content">
      <!-- 左侧用户列表 -->
      <div class="user-list-panel">
        <div class="panel-header">
          <span class="panel-title">相关用户</span>

          <a-tag v-if="userList.length > 0"> {{ userList.length }}个 </a-tag>
        </div>

        <div class="panel-content">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <a-skeleton :rows="5" :animation="true" />
          </div>

          <!-- 空状态 -->
          <div
            v-else-if="userList.length === 0 && !loading"
            class="empty-state"
          >
            <div class="empty-content">
              <icon-user class="empty-icon" />
              <p class="empty-text">
                {{ searchKeyword ? '未找到相关用户' : '请输入关键词搜索' }}
              </p>
            </div>
          </div>

          <!-- 用户列表 -->
          <div v-else class="user-list">
            <div
              v-for="user in userList"
              :key="`${user.account_id}-${user.platform}`"
              class="user-item"
              :class="{ active: selectedUser?.account_id === user.account_id }"
              @click="selectUser(user)"
            >
              <div class="user-avatar">
                <a-avatar :size="40">
                  <img
                    v-if="user.account_profile_photo || user.avatar"
                    :src="user.account_profile_photo || user.avatar"
                    :alt="user.account_name"
                    @error="handleAvatarError"
                  />
                  <span v-else>{{ getAvatarText(user.account_name) }}</span>
                </a-avatar>
              </div>
              <div class="user-info">
                <div class="user-name">{{ user.account_name }}</div>
                <div class="user-meta">
                  <span class="platform">{{ user.platform }}</span>
                  <span v-if="user.message_count" class="message-count">
                    {{ user.message_count }}条消息
                  </span>
                </div>
                <div v-if="user.last_message_time" class="last-time">
                  {{ formatTime(user.last_message_time) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧消息列表 -->
      <div class="message-list-panel">
        <div class="panel-header">
          <span class="panel-title">
            {{
              selectedUser
                ? `与 ${selectedUser.account_name} 的聊天记录`
                : '聊天消息'
            }}
          </span>
          <a-tag v-if="messageList.length > 0">
            {{ messageList.length }}条消息
          </a-tag>
        </div>

        <div class="panel-content">
          <!-- 加载状态 -->
          <div v-if="messageLoading" class="loading-state">
            <a-skeleton :rows="8" :animation="true" />
          </div>

          <!-- 空状态 -->
          <div v-else-if="messageList.length === 0" class="empty-state">
            <div class="empty-content">
              <icon-message class="empty-icon" />
              <p class="empty-text">
                {{ selectedUser ? '暂无聊天记录' : '请选择用户查看聊天记录' }}
              </p>
            </div>
          </div>

          <!-- 消息列表 -->
          <div v-else class="message-list">
            <div
              v-for="message in messageList"
              :key="message.id"
              class="message-item"
            >
              <div class="message-header">
                <div class="message-user">
                  <a-avatar :size="32">
                    <img
                      v-if="message.avatar"
                      :src="message.avatar"
                      :alt="message.send_user"
                      @error="handleAvatarError"
                    />
                    <span v-else>{{ getAvatarText(message.send_user) }}</span>
                  </a-avatar>
                  <span class="user-name">{{ message.send_user }}</span>
                </div>
                <div class="message-actions">
                  <span class="message-time">
                    {{ formatFullTime(message.send_time) }}
                  </span>
                  <a-button
                    size="mini"
                    type="text"
                    @click="handleLocateMessage(message)"
                  >
                    定位到聊天内容
                  </a-button>
                </div>
              </div>

              <div class="message-content">
                <div class="message-bubble" :class="message.msg_show">
                  <!-- 文本消息 -->
                  <div
                    v-if="message.message_type === 'text'"
                    class="text-message"
                    v-html="highlightKeyword(message.message)"
                  ></div>

                  <!-- 图片消息 -->
                  <div
                    v-else-if="message.message_type === 'image'"
                    class="image-message"
                  >
                    <a-image
                      :src="message.message"
                      :width="200"
                      :preview="true"
                      fit="cover"
                    />
                  </div>

                  <!-- 其他类型消息 -->
                  <div v-else class="other-message">
                    [{{ message.message_type }}]
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态提示 -->
    <div v-if="error" class="error-toast">
      <a-alert
        type="error"
        :message="error"
        show-icon
        closable
        @close="error = ''"
      />
    </div>
  </d-modal>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue';
  import {
    IconSearch,
    IconUser,
    IconMessage,
  } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import type {
    ChatSearchUser,
    ChatSearchMessage,
    ChatSearchMessageParams,
    ChatSearchMessageResponse,
  } from '@/views/travel-clue/clue-management/types/index';

  // 组件属性定义
  const props = defineProps({
    // initialKeyword: String,
    // selectedAccount: Object,
  });

  // 组件事件定义
  const emit = defineEmits<{
    close: [];
    locateMessage: [
      messageInfo: {
        account_id: string;
        platform: string;
        self_account_id: string;
        message_id: string;
        send_time: string; // 添加发送时间参数
      }
    ];
  }>();

  // 响应式数据
  const dialogVisible = ref(false);

  const searchKeyword = ref('');
  const loading = ref(false);
  const messageLoading = ref(false);
  const error = ref('');
  const userList = ref<ChatSearchUser[]>([]);
  const messageList = ref<ChatSearchMessage[]>([]);
  const selectedUser = ref<ChatSearchUser | null>(null);

  // 内部管理的选中账户信息（避免直接修改 props）
  const currentSelectedAccount = ref(null);

  // 搜索防抖定时器
  let searchTimer: number | null = null;

  // 关闭弹窗
  const handleClose = () => {
    dialogVisible.value = false;
    emit('close');
  };

  // 选择用户
  const selectUser = async (user: ChatSearchUser) => {
    if (selectedUser.value?.account_id === user.account_id) {
      return;
    }

    selectedUser.value = user;
    messageLoading.value = true;
    error.value = '';

    try {
      console.log('🔄 获取用户聊天记录:', user.account_name);

      const params: ChatSearchMessageParams = {
        content: searchKeyword.value.trim(),
        account_id: user.account_id,
        platform: user.platform,
        self_account_id: user.self_account_id,
      };

      const response = await request('/api/thread/chat_search_message', params);

      if (response && response.code === 0 && response.data) {
        const data = response.data as ChatSearchMessageResponse;

        // 处理消息列表数据
        messageList.value = (data || []).map((msg: any) => ({
          id: msg.id || `${msg.account_id}-${msg.send_time}`,
          message: msg.message || msg.content || '',
          message_type: msg.message_type || 'text',
          msg_show: msg.msg_show || 'left',
          send_time: msg.send_time || msg.time || '',
          send_user: msg.send_user || msg.account_name || '用户',
          avatar: msg.avatar || msg.account_profile_photo || '',
          account_id: msg.account_id,
          platform: msg.platform,
          account_name: msg.account_name,
          account_profile_photo: msg.account_profile_photo,
          self_account_id: msg.self_account_id,
        }));

        console.log('✅ 获取用户消息成功:', messageList.value.length);
      } else {
        throw new Error(response?.msg || '获取消息失败');
      }
    } catch (err) {
      console.error('❌ 获取用户聊天记录失败:', err);
      error.value = '获取聊天记录失败，请重试';
      messageList.value = [];
    } finally {
      messageLoading.value = false;
    }
  };
  // 执行搜索
  const handleSearch = async () => {
    if (!searchKeyword.value.trim()) {
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      console.log('🔍 开始搜索聊天记录:', searchKeyword.value);

      const params: ChatSearchMessageParams = {
        content: searchKeyword.value.trim(),
      };

      // 如果有选中的账户，添加账户相关参数
      if (currentSelectedAccount.value) {
        params.self_account_id = currentSelectedAccount.value.self_account_id;
        params.platform = currentSelectedAccount.value.platform;
      }

      const response = await request('/api/thread/chat_search', params);

      if (response && response.code === 0 && response.data) {
        const data = response.data as ChatSearchMessageResponse;

        // 处理用户列表数据
        userList.value = (data.message || []).map((user: any) => ({
          account_id: user.account_id,
          account_name: user.account_name || user.name || '未知用户',
          platform: user.platform || '未知平台',
          avatar: user.avatar,
          account_profile_photo: user.account_profile_photo,
          last_message_time: user.last_message_time,
          message_count: user.message_count || 0,
          self_account_id: user.self_account_id,
        }));

        console.log('✅ 搜索成功，找到用户:', userList.value.length);

        // 如果有用户，自动选中第一个 并且没有选中用户时
        if (userList.value.length > 0) {
          // 如果带入的有选中客户
          if (currentSelectedAccount.value) {
            const user = userList.value.find(
              (userVal: any) =>
                userVal.account_id === currentSelectedAccount.value?.account_id
            );
            if (user) {
              console.log('🔍 自动选中用户:', user);
              selectUser(user);
            } else {
              console.log('🔍 自动选中用户:', userList.value[0]);
              selectUser(userList.value[0]);
            }
          } else {
            console.log('🔍 自动选中用户:', userList.value[0]);
            selectUser(userList.value[0]);
          }
        } else {
          messageList.value = [];
          selectedUser.value = null;
        }
      } else {
        throw new Error(response?.msg || '搜索失败');
      }
    } catch (err) {
      console.error('❌ 搜索聊天记录失败:', err);
      error.value = '搜索失败，请重试';
      userList.value = [];
      messageList.value = [];
      selectedUser.value = null;
    } finally {
      loading.value = false;
    }
  };

  // 重置组件状态
  const resetState = () => {
    searchKeyword.value = '';
    userList.value = [];
    messageList.value = [];
    selectedUser.value = null;
    error.value = '';
    loading.value = false;
    messageLoading.value = false;
    if (searchTimer) {
      clearTimeout(searchTimer);
      searchTimer = null;
    }
  };

  // 弹窗显示时初始化
  watch(
    () => dialogVisible.value,
    (visible) => {
      if (!visible) {
        // 重置状态
        resetState();
      }
    }
  );

  // 处理搜索输入（防抖）
  const handleSearchInput = (value: string) => {
    if (searchTimer) {
      clearTimeout(searchTimer);
    }

    searchTimer = window.setTimeout(() => {
      if (value.trim()) {
        handleSearch();
      } else {
        userList.value = [];
        messageList.value = [];
        selectedUser.value = null;
      }
    }, 300);
  };

  // 处理消息定位
  const handleLocateMessage = (message: ChatSearchMessage) => {
    console.log('📍 定位消息:', message);

    emit('locateMessage', {
      account_id: message.account_id,
      platform: message.platform,
      self_account_id: message.self_account_id || '',
      message_id: message.id,
      send_time: message.send_time, // 添加发送时间参数
    });

    // 关闭弹窗
    handleClose();
  };

  // 获取头像文字
  const getAvatarText = (name: string) => {
    if (!name) return '用';
    return name.length > 2 ? name.slice(-2) : name;
  };

  // 处理头像加载错误
  const handleAvatarError = (event: Event) => {
    console.log('头像加载失败:', event);
  };

  // 格式化时间
  const formatTime = (time: string) => {
    if (!time) return '';
    return dayjs(time).format('MM-DD HH:mm');
  };

  // 格式化完整时间
  const formatFullTime = (time: string) => {
    if (!time) return '';
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  };

  // 高亮关键词
  const highlightKeyword = (text: string) => {
    if (!searchKeyword.value || !text) return text;

    const keyword = searchKeyword.value.trim();
    const regex = new RegExp(`(${keyword})`, 'gi');
    return text.replace(regex, '<mark class="highlight">$1</mark>');
  };

  // 打开弹窗的方法
  const openDialog = (keyword?: string, account?: any) => {
    if (keyword) {
      searchKeyword.value = keyword;
    }
    if (account) {
      // 更新内部管理的选中账户信息
      currentSelectedAccount.value = account;
    }

    // 打开弹窗
    dialogVisible.value = true;

    // 如果有关键词，自动搜索
    if (searchKeyword.value.trim()) {
      nextTick(() => {
        handleSearch();
      });
    }
  };

  // 暴露给父组件的方法
  defineExpose({
    openDialog,
    handleSearch,
    resetState,
  });
</script>

<style scoped lang="less">
  // 聊天记录搜索弹窗样式 - 遵循 Arco Design 设计规范
  .search-header {
    padding: 16px 0px;
    background: var(--color-bg-1);
  }

  .dialog-content {
    display: flex;
    background: var(--color-fill-1);
    border: 1px solid var(--color-border-1);
    border-top: none;
    border-radius: 10px;
  }

  // 左侧用户列表面板
  .user-list-panel {
    width: 300px;
    border-right: 1px solid var(--color-border-2);
    background: var(--color-bg-1);
  }

  // 右侧消息列表面板
  .message-list-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--color-bg-1);
    overflow: hidden;
    .panel-header {
      border-radius: 0 10px 0 0;
    }
  }

  // 面板头部
  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--color-border-1);
    background: var(--color-fill-2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px 0 0 0;

    .panel-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-1);
    }

    .user-count,
    .message-count {
      font-size: 12px;
      color: var(--color-text-3);
      background: var(--color-fill-3);
      padding: 2px 6px;
      border-radius: 10px;
    }
  }

  // 面板内容
  .panel-content {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    // height: calc(100% - 120px);
    height: 400px;
    overflow-y: auto;
  }

  // 加载状态
  .loading-state {
    padding: 16px;
  }

  // 空状态
  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .empty-content {
      text-align: center;
      color: var(--color-text-3);

      .empty-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 14px;
        margin: 0;
      }
    }
  }

  // 用户列表
  .user-list {
    flex: 1;
    overflow-y: auto;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .user-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid var(--color-border-1);
      transition: background-color 0.2s ease;
      border-left: 3px solid transparent;
      border-radius: 4px;

      // &:hover {
      // background: var(--color-fill-1);
      // }

      &.active {
        background: var(--color-fill-1);
        box-shadow: 0 0 4px var(--color-fill-1);
      }

      .user-avatar {
        margin-right: 12px;
        flex-shrink: 0;
      }

      .user-info {
        flex: 1;
        min-width: 0;

        .user-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--color-text-1);
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .user-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 2px;

          .platform {
            font-size: 12px;
            color: var(--color-text-3);
            background: var(--color-fill-3);
            padding: 1px 6px;
            border-radius: 8px;
          }

          .message-count {
            font-size: 12px;
            color: var(--color-text-3);
          }
        }

        .last-time {
          font-size: 12px;
          color: var(--color-text-4);
        }
      }
    }
  }

  // 消息列表
  .message-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .message-item {
      padding: 8px 16px;
      border-bottom: 1px solid var(--color-border-2);

      &:last-child {
        border-bottom: none;
      }

      .message-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2px;

        .message-user {
          display: flex;
          align-items: center;
          gap: 8px;

          .user-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--color-text-2);
          }
        }

        .message-actions {
          display: flex;
          align-items: center;
          gap: 8px;

          .message-time {
            font-size: 12px;
            color: var(--color-text-4);
          }
        }
      }

      .message-content {
        margin-left: 40px;

        .message-bubble {
          display: inline-block;
          max-width: 360px;
          padding: 8px 12px;
          border-radius: 12px;
          word-wrap: break-word;

          &.left {
            background: var(--color-fill-2);
            color: var(--color-text-1);
          }

          &.right {
            background: var(--color-primary-light-2);
            color: var(--color-text-1);
          }

          .text-message {
            line-height: 1.4;

            :deep(.highlight) {
              background: var(--color-warning-light-2);
              color: var(--color-warning-6);
              padding: 1px 1px;
              border-radius: 2px;
              font-weight: 500;
            }
          }

          .image-message {
            .arco-image {
              border-radius: 8px;
              overflow: hidden;
            }
          }

          .other-message {
            color: var(--color-text-3);
            font-style: italic;
          }
        }
      }
    }
  }

  // 错误提示
  .error-toast {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    min-width: 300px;
  }
</style>

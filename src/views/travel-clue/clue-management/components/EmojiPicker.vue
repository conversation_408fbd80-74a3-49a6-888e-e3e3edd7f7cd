<!--
  表情选择器组件
  支持不同平台的表情包
-->
<template>
  <div class="emoji-picker">
    <div class="emoji-tabs">
      <a-tabs v-model:active-key="activeTab" size="small" type="card">
        <a-tab-pane key="common" title="常用">
          <div class="emoji-grid">
            <div
              v-for="emoji in commonEmojis"
              :key="emoji"
              class="emoji-item"
              @click="selectEmoji(emoji)"
            >
              {{ emoji }}
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane v-if="platform === '抖音'" key="douyin" title="抖音">
          <div class="emoji-grid">
            <div
              v-for="emoji in douyinEmojis"
              :key="emoji.code"
              class="emoji-item"
              @click="selectEmoji(emoji.text)"
            >
              <img
                v-if="emoji.image"
                :src="emoji.image"
                :alt="emoji.text"
                class="emoji-image"
              />
              <span v-else>{{ emoji.text }}</span>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane
          v-if="platform === '小红书'"
          key="xiaohongshu"
          title="小红书"
        >
          <div class="emoji-grid">
            <div
              v-for="emoji in xiaohongshuEmojis"
              :key="emoji.code"
              class="emoji-item"
              @click="selectEmoji(emoji.text)"
            >
              <img
                v-if="emoji.image"
                :src="emoji.image"
                :alt="emoji.text"
                class="emoji-image"
              />
              <span v-else>{{ emoji.text }}</span>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';

  interface Props {
    platform?: string;
  }

  interface Emits {
    (e: 'select', emoji: string): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    platform: '',
  });

  const emit = defineEmits<Emits>();

  const activeTab = ref('common');

  // 常用表情
  const commonEmojis = [
    '😀',
    '😃',
    '😄',
    '😁',
    '😆',
    '😅',
    '😂',
    '🤣',
    '😊',
    '😇',
    '🙂',
    '🙃',
    '😉',
    '😌',
    '😍',
    '🥰',
    '😘',
    '😗',
    '😙',
    '😚',
    '😋',
    '😛',
    '😝',
    '😜',
    '🤪',
    '🤨',
    '🧐',
    '🤓',
    '😎',
    '🤩',
    '🥳',
    '😏',
    '😒',
    '😞',
    '😔',
    '😟',
    '😕',
    '🙁',
    '☹️',
    '😣',
    '😖',
    '😫',
    '😩',
    '🥺',
    '😢',
    '😭',
    '😤',
    '😠',
    '😡',
    '🤬',
    '🤯',
    '😳',
    '🥵',
    '🥶',
    '😱',
    '😨',
    '😰',
    '😥',
    '😓',
    '🤗',
    '🤔',
    '🤭',
    '🤫',
    '🤥',
    '😶',
    '😐',
    '😑',
    '😬',
    '🙄',
    '😯',
    '😦',
    '😧',
    '😮',
    '😲',
    '🥱',
    '😴',
    '🤤',
    '😪',
    '😵',
    '🤐',
    '🥴',
    '🤢',
    '🤮',
    '🤧',
    '😷',
    '🤒',
    '🤕',
    '🤑',
    '🤠',
    '😈',
    '👿',
    '👹',
    '👺',
    '🤡',
    '💩',
    '👻',
    '💀',
    '☠️',
    '👽',
    '👾',
    '🤖',
    '🎃',
    '😺',
    '😸',
    '😹',
    '😻',
    '😼',
    '😽',
    '🙀',
    '😿',
    '😾',
    '👋',
    '🤚',
    '🖐️',
    '✋',
    '🖖',
    '👌',
    '🤏',
    '✌️',
    '🤞',
    '🤟',
    '🤘',
    '🤙',
    '👈',
    '👉',
    '👆',
    '🖕',
    '👇',
    '☝️',
    '👍',
    '👎',
    '👊',
    '✊',
    '🤛',
    '🤜',
    '👏',
    '🙌',
    '👐',
    '🤲',
    '🤝',
    '🙏',
    '✍️',
    '💅',
    '🤳',
    '💪',
    '🦾',
    '🦿',
    '🦵',
    '🦶',
    '👂',
    '🦻',
    '👃',
    '🧠',
    '🫀',
    '🫁',
    '🦷',
    '🦴',
    '👀',
    '👁️',
    '👅',
    '👄',
    '💋',
    '🩸',
    '👶',
    '🧒',
    '👦',
    '👧',
    '🧑',
  ];

  // 抖音表情
  const douyinEmojis = ref([
    { code: 'smile', text: '[微笑]', image: '/douyin-emoji/smile.png' },
    { code: 'laugh', text: '[大笑]', image: '/douyin-emoji/laugh.png' },
    { code: 'love', text: '[爱心]', image: '/douyin-emoji/love.png' },
    { code: 'cry', text: '[哭泣]', image: '/douyin-emoji/cry.png' },
    { code: 'angry', text: '[生气]', image: '/douyin-emoji/angry.png' },
    { code: 'surprise', text: '[惊讶]', image: '/douyin-emoji/surprise.png' },
    { code: 'cool', text: '[酷]', image: '/douyin-emoji/cool.png' },
    { code: 'shy', text: '[害羞]', image: '/douyin-emoji/shy.png' },
    { code: 'think', text: '[思考]', image: '/douyin-emoji/think.png' },
    { code: 'sleep', text: '[睡觉]', image: '/douyin-emoji/sleep.png' },
    { code: 'thumbsup', text: '[赞]', image: '/douyin-emoji/thumbsup.png' },
    { code: 'ok', text: '[OK]', image: '/douyin-emoji/ok.png' },
    { code: 'rose', text: '[玫瑰]', image: '/douyin-emoji/rose.png' },
    { code: 'gift', text: '[礼物]', image: '/douyin-emoji/gift.png' },
    { code: 'cake', text: '[蛋糕]', image: '/douyin-emoji/cake.png' },
    { code: 'beer', text: '[啤酒]', image: '/douyin-emoji/beer.png' },
    { code: 'coffee', text: '[咖啡]', image: '/douyin-emoji/coffee.png' },
    { code: 'sun', text: '[太阳]', image: '/douyin-emoji/sun.png' },
    { code: 'moon', text: '[月亮]', image: '/douyin-emoji/moon.png' },
    { code: 'star', text: '[星星]', image: '/douyin-emoji/star.png' },
  ]);

  // 小红书表情
  const xiaohongshuEmojis = ref([
    { code: 'smile', text: '[微笑R]', image: '/xiaohongshu-emoji/smile.png' },
    { code: 'laugh', text: '[哈哈R]', image: '/xiaohongshu-emoji/laugh.png' },
    { code: 'love', text: '[心动R]', image: '/xiaohongshu-emoji/love.png' },
    { code: 'cry', text: '[哭惹R]', image: '/xiaohongshu-emoji/cry.png' },
    { code: 'angry', text: '[生气R]', image: '/xiaohongshu-emoji/angry.png' },
    {
      code: 'surprise',
      text: '[惊讶R]',
      image: '/xiaohongshu-emoji/surprise.png',
    },
    { code: 'cool', text: '[酷R]', image: '/xiaohongshu-emoji/cool.png' },
    { code: 'shy', text: '[害羞R]', image: '/xiaohongshu-emoji/shy.png' },
    { code: 'think', text: '[思考R]', image: '/xiaohongshu-emoji/think.png' },
    { code: 'sleep', text: '[困R]', image: '/xiaohongshu-emoji/sleep.png' },
    {
      code: 'thumbsup',
      text: '[赞R]',
      image: '/xiaohongshu-emoji/thumbsup.png',
    },
    { code: 'ok', text: '[OKR]', image: '/xiaohongshu-emoji/ok.png' },
    { code: 'rose', text: '[玫瑰R]', image: '/xiaohongshu-emoji/rose.png' },
    { code: 'gift', text: '[礼物R]', image: '/xiaohongshu-emoji/gift.png' },
    { code: 'cake', text: '[蛋糕R]', image: '/xiaohongshu-emoji/cake.png' },
    { code: 'coffee', text: '[咖啡R]', image: '/xiaohongshu-emoji/coffee.png' },
    { code: 'sun', text: '[太阳R]', image: '/xiaohongshu-emoji/sun.png' },
    { code: 'moon', text: '[月亮R]', image: '/xiaohongshu-emoji/moon.png' },
    { code: 'star', text: '[星星R]', image: '/xiaohongshu-emoji/star.png' },
    { code: 'fire', text: '[火R]', image: '/xiaohongshu-emoji/fire.png' },
  ]);

  const selectEmoji = (emoji: string) => {
    emit('select', emoji);
  };
</script>

<style scoped lang="less">
  .emoji-picker {
    width: 300px;
    max-height: 200px;

    .emoji-tabs {
      :deep(.arco-tabs-content) {
        max-height: 160px;
        overflow-y: auto;
      }
    }

    .emoji-grid {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: 4px;
      padding: 8px;

      .emoji-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: var(--color-fill-2);
        }

        .emoji-image {
          width: 24px;
          height: 24px;
          object-fit: contain;
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .emoji-picker {
      width: 280px;

      .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 2px;
        padding: 4px;

        .emoji-item {
          width: 28px;
          height: 28px;

          .emoji-image {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
</style>

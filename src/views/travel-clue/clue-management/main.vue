<template>
  <div>
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane key="list">
        <template #title> 线索列表 </template>
      </a-tab-pane>
      <a-tab-pane key="chat">
        <template #title> 会话窗口 </template>
      </a-tab-pane>
    </a-tabs>
    <sale-clue-list
      v-if="activeKey === 'list'"
      @go-chat="goChat"
    ></sale-clue-list>
    <chat-index v-else ref="chatIndexRef"></chat-index>
  </div>
</template>

<script lang="ts" setup>
  import { ref, nextTick } from 'vue';
  import chatIndex from './index.vue';
  import saleClueList from '../sale-clue-list/sale-clue-list.vue';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    modelValue: {
      type: [String, Number, Array],
      default: () => '',
    },
  });

  const activeKey = ref('chat');
  const chatIndexRef = ref();

  function goChat(record: any) {
    // 1、切换到会话窗口
    activeKey.value = 'chat';
    // 2、选中账号
    nextTick(() => {
      setTimeout(() => {
        chatIndexRef.value?.handleLocateMessage({
          self_account_id: record.from_account_id,
          platform: record.platform,
          account_id: record.account_id,
        });
      }, 200);
    });
  }
</script>

<style lang="less" scoped></style>

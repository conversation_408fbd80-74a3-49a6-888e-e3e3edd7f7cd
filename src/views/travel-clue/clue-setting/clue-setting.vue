<template>
  <a-tabs lazy-load>
    <a-tab-pane key="关键词" title="关键词">
      <clue-setting-keywords
        :send-params="{
          type: 'keyword',
        }"
      ></clue-setting-keywords>
    </a-tab-pane>
    <a-tab-pane key="内容号" title="内容号">
      <clue-setting-keywords
        :send-params="{
          type: 'tk_account',
        }"
      ></clue-setting-keywords>
    </a-tab-pane>
    <a-tab-pane key="直播间" title="直播间">
      <clue-setting-keywords
        :send-params="{
          type: 'live_room',
        }"
      ></clue-setting-keywords>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  import ClueSettingKeywords from './components/clue-setting-keywords.vue';
</script>

<style scoped lang="less">
  :deep(.arco-tabs-content) {
    padding: 0;
  }
</style>

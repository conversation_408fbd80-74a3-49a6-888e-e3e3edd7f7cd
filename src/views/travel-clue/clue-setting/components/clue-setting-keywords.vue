<template>
  <div class="mt-10">
    <a-card>
      <div class="jc-sb">
        <a-space>
          <a-button type="primary" @click="tableRef?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.state"
            :disabled="loading"
            :data-list="clueConfigStateM"
            @change="refreshData"
          />
        </a-space>
        <a-space>
          <a-button type="primary" status="success" @click="showEdit()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <base-table
        ref="tableRef"
        v-model:loading="loading"
        class="mt-10"
        :scroll-percent="{ x: 1200, y: 'calc(100vh - 224px)' }"
        no-pagination
        :columns-config="columns"
        :send-params="tableParams"
        :data-config="dataConfig"
      >
        <template #keywords="{ record }: TableColumnSlot">
          <template v-if="record.keywords?.length">
            <component
              :is="record.keywords.length > 8 ? Tooltip : 'div'"
              :content="
              record.keywords
                .map((item:any) => item?.account_name || item)
                .join('，')
            "
              :content-style="{ maxWidth: '600px' }"
            >
              <a-space wrap size="mini">
                <a-tag
                  v-for="item in record.keywords.slice(0, 8)"
                  :key="item"
                  color="blue"
                >
                  <span class="text-overflow">
                    {{ item?.account_name || item }}
                  </span>
                </a-tag>
                <a-tag v-if="record.keywords.length > 8" color="blue">
                  等共{{ record.keywords.length }}个
                </a-tag>
              </a-space>
            </component>
          </template>
          <span v-else>-</span>
        </template>
        <template #state="{ record }: TableColumnSlot">
          <a-switch
            :loading="record.loading"
            :model-value="record.state === 1"
            @change="updateState(record)"
          >
            <template #checked> 有效 </template>
            <template #unchecked> 无效 </template>
          </a-switch>
        </template>
        <template #platform="{ record }: TableColumnSlot">
          <a-space align="center">
            <img
              width="20"
              :src="`/icons/platform/${record.platform}.png`"
              :alt="record.platform"
            />
            <span>{{ record.platform }}</span>
          </a-space>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEdit(record)"> <icon-edit /> 编辑 </a-link>
            <a-popconfirm
              trigger="click"
              :content="`确定复制【${record.title}】吗?`"
              @ok="copyAction(record)"
            >
              <a-link> <icon-copy /> 复制 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <clue-setting-keywords-modal
      ref="saveRef"
      :send-params="tableParams"
      @save="refreshData"
    />
  </div>
</template>

<script setup lang="ts">
  import request from '@/api/request';
  import { computed, ref } from 'vue';
  import { Message, Tooltip } from '@arco-design/web-vue';
  import { getText } from '@/components/dict-select/dict-util';
  import {
    clueConfigStateM,
    replayTemplateTypeM,
  } from '@/components/dict-select/dict-clue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import ClueSettingKeywordsModal from './clue-setting-keywords-modal.vue';

  const props = defineProps({
    sendParams: {
      type: Object,
      default: () => ({}),
    },
    platform: {
      type: String,
      default: '',
    },
    area: {
      type: String,
      default: '',
    },
  });

  const formModel = ref({
    state: 1,
  });
  const columns = computed(() => [
    { title: '分组', dataIndex: 'title', width: 200, fixed: 'left' },
    { title: '媒体平台', dataIndex: 'platform', width: 200 },
    { title: '产品', dataIndex: 'area', width: 200 },
    {
      title: getText(replayTemplateTypeM, props.sendParams.type),
      dataIndex: 'keywords',
    },
    { title: '状态', dataIndex: 'state', width: 120, align: 'center' },
    {
      title: '操作',
      dataIndex: 'action',
      width: 140,
      fixed: 'right',
      align: 'center',
    },
  ]);
  const tableRef = ref();
  const saveRef = ref();
  const loading = ref(false);

  const tableParams = computed(() => ({
    ...props.sendParams,
    ...formModel.value,
  }));

  function dataConfig(params: any) {
    return request('/api/thread/getBaseSettingKeywords', params);
  }

  function refreshData() {
    tableRef.value?.search();
  }

  function showEdit(record?: any) {
    saveRef.value?.show(record);
  }

  function updateState(record: any) {
    record.loading = true;
    request('/api/thread/baseSettingKeywordsSave', {
      ...record,
      state: record.state === 1 ? -1 : 1,
      hideMsgTips: true,
    })
      .then((res) => {
        record.state = res.data.state || record.state;
      })
      .catch((res) => {
        Message.error({
          content: res?.msg || '网络错误',
          duration: 0,
          closable: true,
        });
      })
      .finally(() => {
        record.loading = false;
      });
  }
  function copyAction(record: any) {
    let loadingModal = Message.loading('复制中...');
    request('/api/thread/baseSettingKeywordsSave', {
      ...record,
      title: `${record.title}_copy`,
      state: -1,
      id: undefined,
    })
      .then((res) => {
        refreshData();
      })
      .finally(() => {
        record.loading = false;
        loadingModal.close();
      });
  }
</script>

<style scoped lang="less">
  .text-overflow {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>

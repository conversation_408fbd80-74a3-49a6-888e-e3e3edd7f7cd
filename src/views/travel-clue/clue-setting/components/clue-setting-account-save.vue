<template>
  <d-modal
    :visible="visible"
    title="添加内容号"
    @cancel="visible = false"
    @ok="sendInfo"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item
        label="内容号昵称"
        field="account_name"
        :rules="requiredRule"
      >
        <a-input
          v-model="formModel.account_name"
          ::max-length="50"
          show-word-limit
        />
      </a-form-item>
      <a-form-item label="内容号ID" field="account_id" :rules="requiredRule">
        <a-input v-model="formModel.account_id" />
      </a-form-item>
      <a-form-item label="账户主页地址" field="url" :rules="urlRule">
        <a-input v-model="formModel.url" />
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { requiredRule } from '@/utils/util';
  import DModal from '@/components/d-modal/d-modal.vue';

  const visible = ref(false);
  const formRef = ref();
  const emits = defineEmits(['save']);

  // 网址格式校验规则
  const urlRule = [
    {
      required: true,
      message: '请输入账户主页地址',
    },
    {
      type: 'url',
      message: '请输入正确的网址格式',
    },
  ];
  function defaultForm() {
    return {
      account_name: '', // 账户名称
      account_id: '', // 账户id
      url: '', // 账户主页地址
    };
  }
  const formModel = ref(defaultForm());
  function show() {
    formRef.value?.clearValidate();
    formModel.value = defaultForm();
    visible.value = true;
  }
  function sendInfo() {
    formRef.value.validate((err: any) => {
      if (!err) {
        emits('save', formModel.value);
        visible.value = false;
      }
    });
    return false;
  }
  // title
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

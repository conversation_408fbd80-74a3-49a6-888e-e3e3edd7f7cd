<template>
  <d-modal
    :visible="visible"
    title="添加直播间"
    @cancel="visible = false"
    @ok="sendInfo"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item
        field="account_name"
        label="直播间名称"
        :rules="requiredRule"
      >
        <a-input
          v-model="formModel.account_name"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item field="url" label="直播间地址" :rules="requiredRule">
        <a-input
          v-model="formModel.url"
          autocomplete="off"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="自有直播间">
        <a-radio-group v-model="formModel.is_myself">
          <a-radio value="是">是</a-radio>
          <a-radio value="否">否</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { requiredRule } from '@/utils/util';
  import DModal from '@/components/d-modal/d-modal.vue';

  const visible = ref(false);
  const formRef = ref();
  const emits = defineEmits(['save']);
  function defaultForm() {
    return {
      account_name: '',
      is_myself: '是',
      url: '',
    };
  }
  const formModel = ref(defaultForm());
  function show() {
    formRef.value?.clearValidate();
    formModel.value = defaultForm();
    visible.value = true;
  }
  function sendInfo() {
    formRef.value.validate((err: any) => {
      if (!err) {
        emits('save', formModel.value);
        visible.value = false;
      }
    });
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

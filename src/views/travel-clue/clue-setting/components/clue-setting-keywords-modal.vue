<template>
  <a-modal
    :visible="visible"
    width="600px"
    :title="`${getText(replayTemplateTypeM, sendParams.type)}配置`"
    :body-style="{ maxHeight: '80vh' }"
    :ok-loading="loading"
    @ok="sendInfo"
    @cancel="visible = false"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item label="分组" :rules="requiredRule" field="title">
        <a-input
          v-model="formModel.title"
          placeholder="请输入分组名称"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="媒体平台" :rules="requiredRule" field="platform">
        <platform-select v-model="formModel.platform" :allow-clear="false" />
      </a-form-item>
      <a-form-item label="产品" :rules="requiredRule" field="thread_setting_id">
        <request-select
          v-model="formModel.thread_setting_id"
          api="product"
          :allow-clear="false"
          @change="(val, item) => (formModel.area = item?.area)"
        />
      </a-form-item>

      <a-form-item :label="getText(replayTemplateTypeM, sendParams.type)">
        <a-space wrap>
          <a-tag
            v-for="(tag, index) of formModel.keywords"
            :key="tag"
            size="large"
            color="arcoblue"
            closable
            @close="handleRemove(index)"
          >
            <span class="text-overflow">
              {{ tag.account_name || tag }}
            </span>
          </a-tag>
          <a-input-group v-if="showInput">
            <a-input
              ref="inputRef"
              v-model.trim="inputVal"
              class="w-300"
              allow-clear
              placeholder="支持一次添加多个，使用分号；分隔"
              @keyup.enter="handleAdd"
              @blur="handleAdd"
            />
            <a-button type="primary" @click="handleAdd">
              <icon-check />
            </a-button>
          </a-input-group>
          <a-tag
            v-else
            size="large"
            color="green"
            :style="{
              cursor: 'pointer',
            }"
            @click="handleEdit"
          >
            <template #icon>
              <icon-plus />
            </template>
            添加
          </a-tag>
        </a-space>
      </a-form-item>
      <a-form-item label="状态">
        <a-switch
          :model-value="formModel.state === 1"
          @change="(val:boolean) => (formModel.state = val ? 1 : -1)"
        >
          <template #checked> 有效 </template>
          <template #unchecked> 无效 </template>
        </a-switch>
      </a-form-item>
    </a-form>
    <clue-setting-account-save ref="accountRef" @save="handleAccountAdd" />
    <clue-setting-liveroom-save ref="liveroomRef" @save="handleAccountAdd" />
  </a-modal>
</template>

<script setup lang="ts">
  import { nextTick, PropType, ref } from 'vue';
  import { requiredRule } from '@/utils/util';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { cloneDeep, uniq } from 'lodash';
  import { replayTemplateTypeM } from '@/components/dict-select/dict-clue';
  import { getText } from '@/components/dict-select/dict-util';
  import ClueSettingAccountSave from '@/views/travel-clue/clue-setting/components/clue-setting-account-save.vue';
  import ClueSettingLiveroomSave from '@/views/travel-clue/clue-setting/components/clue-setting-liveroom-save.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const accountRef = ref();
  const liveroomRef = ref();
  const emits = defineEmits(['save']);
  const props = defineProps({
    sendParams: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  function defaultForm() {
    return {
      thread_setting_id: '',
      area: '',
      title: '',
      platform: '',
      keywords: [] as any[],
      state: 1,
      id: null,
    };
  }
  const formModel = ref(defaultForm());
  const inputRef = ref();
  const showInput = ref(false);
  const inputVal = ref('');

  const handleEdit = () => {
    switch (props.sendParams.type) {
      case 'tk_account':
        accountRef.value?.show();
        break;
      case 'live_room':
        liveroomRef.value?.show();
        break;
      default:
        showInput.value = true;
        nextTick(() => {
          inputRef.value?.focus();
        });
        break;
    }
  };
  const handleAdd = () => {
    if (inputVal.value) {
      let keywords = inputVal.value.replaceAll('；', ';').split(';');
      formModel.value.keywords = uniq([
        ...formModel.value.keywords,
        ...keywords,
      ]);
      inputVal.value = '';
    }
    showInput.value = false;
  };
  const handleAccountAdd = (item: any) => {
    formModel.value.keywords.push(cloneDeep(item));
  };

  const handleRemove = (index: number) => {
    formModel.value.keywords.splice(index, 1);
  };

  // 获取配置信息
  function show(data?: any) {
    formRef.value.clearValidate();
    formModel.value = defaultForm();
    let initForm = defaultForm();
    formModel.value = initForm;
    if (data) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          data[key] || initForm[key as keyof typeof initForm];
      });
    }

    visible.value = true;
  }
  function sendInfo() {
    formModel.value.keywords = uniq(formModel.value.keywords);
    if (!formModel.value.keywords.length) {
      Message.error('请填写关键词');
    } else {
      formRef.value.validate((err: any) => {
        if (!err) {
          loading.value = true;
          request('/api/thread/baseSettingKeywordsSave', {
            ...props.sendParams,
            ...formModel.value,
          })
            .then(() => {
              Message.success('操作成功');
              visible.value = false;
              emits('save');
            })
            .finally(() => {
              loading.value = false;
            });
        }
      });
    }
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .text-overflow {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>

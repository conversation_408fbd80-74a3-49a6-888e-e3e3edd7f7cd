<template>
  <div class="segmented-progress-bar">
    <!-- 进度条容器 -->
    <div
      class="progress-container"
      @mouseenter="showTooltip = true"
      @mouseleave="showTooltip = false"
    >
      <!-- 分段片段 -->
      <div
        v-for="(segment, index) in segments"
        :key="index"
        class="progress-segment"
        :class="getSegmentClass(segment.status)"
        :style="getSegmentStyle(segment)"
        :title="getSegmentTooltip(segment, index)"
      >
        <!-- 小任务数量过多时显示合并指示器 -->
        <div v-if="segment.count > 1" class="segment-indicator">
          {{ segment.count }}
        </div>
      </div>
    </div>

    <!-- 悬浮提示框 -->
    <a-tooltip
      v-if="showTooltip && segments.length > 0"
      :visible="showTooltip"
      position="top"
      :content-style="{ maxWidth: '300px' }"
    >
      <template #content>
        <div class="tooltip-content">
          <div class="tooltip-title">任务执行详情</div>
          <div class="tooltip-stats">
            <div class="stat-item">
              <span class="stat-label">总任务数:</span>
              <span class="stat-value">{{ totalNum }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">排队中:</span>
              <span class="stat-value stat-pending">{{
                statusStats.pending
              }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">进行中:</span>
              <span class="stat-value stat-running">{{
                statusStats.running
              }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成:</span>
              <span class="stat-value stat-success">{{
                statusStats.success
              }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">失败:</span>
              <span class="stat-value stat-failed">{{
                statusStats.failed
              }}</span>
            </div>
          </div>
          <div v-if="segments.length > maxVisibleSegments" class="tooltip-note">
            注: 相邻相同状态已合并显示
          </div>
        </div>
      </template>
      <div class="tooltip-trigger"></div>
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';

  // 组件 Props 接口定义
  interface Props {
    detailStatusList?: number[]; // 详细状态列表，每个元素代表一个任务的状态
    totalNum?: number; // 总任务数
    successNum?: number; // 成功任务数
    failNum?: number; // 失败任务数
    maxVisibleSegments?: number; // 最大可见分段数，超过时合并相邻相同状态
  }

  // 分段数据接口
  interface ProgressSegment {
    status: number; // 状态值: 0-排队中, 1-进行中, 2-已完成, 3-失败
    count: number; // 该状态的任务数量
    width: number; // 分段宽度百分比
    startIndex: number; // 起始索引
    endIndex: number; // 结束索引
  }

  // 状态统计接口
  interface StatusStats {
    pending: number; // 排队中数量
    running: number; // 进行中数量
    success: number; // 成功数量
    failed: number; // 失败数量
  }

  // 定义 Props 默认值
  const props = withDefaults(defineProps<Props>(), {
    detailStatusList: () => [],
    totalNum: 0,
    successNum: 0,
    failNum: 0,
    maxVisibleSegments: 100, // 默认最多显示100个分段
  });

  // 响应式数据
  const showTooltip = ref(false);

  // 状态配置映射 - 使用 Arco Design 色彩规范
  const statusConfig = {
    '-1': { name: '排队中', color: 'rgb(var(--gray-3))', class: 'pending' }, // 灰色
    1: { name: '进行中', color: 'rgb(var(--primary-5))', class: 'running' }, // 橙色
    2: { name: '已完成', color: 'rgb(var(--green-5))', class: 'success' }, // 绿色
    3: { name: '发布失败', color: 'rgb(var(--red-5))', class: 'failed' }, // 红色
    4: { name: '取消发布', color: 'rgb(var(--gray-5))', class: 'pending' }, // 灰色
  };

  // 计算状态统计
  const statusStats = computed<StatusStats>(() => {
    const stats = { pending: 0, running: 0, success: 0, failed: 0 };

    props.detailStatusList.forEach((status) => {
      switch (status) {
        case 0:
          stats.pending += 1;
          break;
        case 1:
          stats.running += 1;
          break;
        case 2:
          stats.success += 1;
          break;
        case 3:
          stats.failed += 1;
          break;
        default:
          break;
      }
    });

    return stats;
  });

  // 计算分段数据
  const segments = computed<ProgressSegment[]>(() => {
    const statusList = props.detailStatusList;
    if (!statusList.length) {
      // 如果没有详细状态数据，根据总数和成功/失败数生成简单分段
      const total = props.totalNum || 0;
      const success = props.successNum || 0;
      const failed = props.failNum || 0;
      const pending = Math.max(0, total - success - failed);

      const simpleSegments: ProgressSegment[] = [];
      if (success > 0) {
        simpleSegments.push({
          status: 2,
          count: success,
          width: (success / total) * 100,
          startIndex: 0,
          endIndex: success - 1,
        });
      }
      if (failed > 0) {
        simpleSegments.push({
          status: 3,
          count: failed,
          width: (failed / total) * 100,
          startIndex: success,
          endIndex: success + failed - 1,
        });
      }
      if (pending > 0) {
        simpleSegments.push({
          status: 0,
          count: pending,
          width: (pending / total) * 100,
          startIndex: success + failed,
          endIndex: total - 1,
        });
      }
      return simpleSegments;
    }

    // 如果任务数量较少，直接显示每个任务
    if (statusList.length <= props.maxVisibleSegments) {
      return statusList.map((status, index) => ({
        status,
        count: 1,
        width: 100 / statusList.length,
        startIndex: index,
        endIndex: index,
      }));
    }

    // 任务数量过多时，合并相邻相同状态的分段
    const mergedSegments: ProgressSegment[] = [];
    let currentStatus = statusList[0];
    let currentCount = 1;
    let startIndex = 0;

    for (let i = 1; i < statusList.length; i += 1) {
      if (statusList[i] === currentStatus) {
        currentCount += 1;
      } else {
        // 状态改变，保存当前分段
        mergedSegments.push({
          status: currentStatus,
          count: currentCount,
          width: (currentCount / statusList.length) * 100,
          startIndex,
          endIndex: i - 1,
        });

        // 开始新的分段
        currentStatus = statusList[i];
        currentCount = 1;
        startIndex = i;
      }
    }

    // 添加最后一个分段
    mergedSegments.push({
      status: currentStatus,
      count: currentCount,
      width: (currentCount / statusList.length) * 100,
      startIndex,
      endIndex: statusList.length - 1,
    });

    return mergedSegments;
  });

  // 获取分段样式类
  const getSegmentClass = (status: number): string => {
    return `segment-${
      statusConfig[status as keyof typeof statusConfig]?.class || 'pending'
    }`;
  };

  // 获取分段样式
  const getSegmentStyle = (segment: ProgressSegment) => {
    const config = statusConfig[segment.status as keyof typeof statusConfig];
    return {
      width: `${segment.width}%`,
      backgroundColor: config?.color || '#e5e7eb',
    };
  };

  // 获取分段提示文本
  const getSegmentTooltip = (
    segment: ProgressSegment,
    index: number
  ): string => {
    const config = statusConfig[segment.status as keyof typeof statusConfig];
    const statusName = config?.name || '未知状态';

    if (segment.count === 1) {
      return `任务 ${segment.startIndex + 1}: ${statusName}`;
    }
    return `任务 ${segment.startIndex + 1}-${
      segment.endIndex + 1
    }: ${statusName} (${segment.count}个)`;
  };
</script>

<style scoped lang="less">
  .segmented-progress-bar {
    position: relative;
    width: 100%;
    opacity: 1;
  }

  .progress-container {
    display: flex;
    width: 100%;
    height: 8px;
    background-color: var(--color-fill-2);
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s var(--motion-ease-in-out);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%
      );
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    &:hover::before {
      transform: translateX(100%);
    }
  }

  .progress-segment {
    position: relative;
    height: 100%;
    transition: all 0.4s var(--motion-ease-out-back);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 3px; // 确保即使很小的分段也可见
    transform-origin: bottom;
    opacity: 1;
    border-radius: 2px;

    &:not(:last-child) {
      // border-right: 1.5px solid rgba(255, 255, 255, 0.4);
      margin-right: 1.5px;
    }

    &:first-child {
      border-radius: 6px 2px 2px 6px;
    }

    &:last-child {
      border-radius: 2px 6px 6px 2px;
    }

    &:only-child {
      border-radius: 6px;
    }

    &:hover {
      filter: brightness(1.02) saturate(1.05);
      // transform: scaleY(1.2) scaleX(1.01);
      z-index: 2;
      border-radius: 3px;
    }

    // 为前几个分段添加延迟动画效果
    &:nth-child(1) {
      animation-delay: 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.05s;
    }
    &:nth-child(3) {
      animation-delay: 0.1s;
    }
    &:nth-child(4) {
      animation-delay: 0.15s;
    }
    &:nth-child(5) {
      animation-delay: 0.2s;
    }
    &:nth-child(6) {
      animation-delay: 0.25s;
    }
    &:nth-child(7) {
      animation-delay: 0.3s;
    }
    &:nth-child(8) {
      animation-delay: 0.35s;
    }
    &:nth-child(9) {
      animation-delay: 0.4s;
    }
    &:nth-child(10) {
      animation-delay: 0.45s;
    }
  }

  // 状态样式 - 使用 Arco Design 色彩变量
  .segment-pending {
    background-color: rgb(var(--gray-4));
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
      animation: pendingShimmer 1.5s ease-in-out;
    }
  }

  .segment-running {
    background-color: rgb(var(--primary-5));
    background-image: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.15) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      transparent 75%
    );
    background-size: 16px 16px;
    animation: progress-animation 2.5s linear infinite;
    position: relative;
    overflow: hidden;
    border-radius: 3px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
      );
      animation: shimmer 2.5s ease-in-out infinite;
    }
  }

  .segment-success {
    background-color: rgb(var(--green-5));
    position: relative;
    border-radius: 2px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.2) 0%,
        transparent 50%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 2px;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .segment-failed {
    background-color: rgb(var(--red-5));
    position: relative;
    border-radius: 2px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.15) 0%,
        transparent 50%
      );
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 2px;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  // 进行中状态的条纹动画
  @keyframes progress-animation {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 20px 0;
    }
  }

  // 脉冲发光效果 - 已移除阴影效果
  // @keyframes pulse-glow {
  //   0%,
  //   100% {
  //     box-shadow: 0 0 0 rgba(var(--orange-5), 0);
  //   }
  //   50% {
  //     box-shadow: 0 0 8px rgba(var(--orange-5), 0.4);
  //   }
  // }

  // 光泽扫过效果
  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  // 排队中状态的微光效果
  @keyframes pendingShimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .segment-indicator {
    font-size: 10px;
    color: white;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    min-width: 16px;
    text-align: center;
  }

  .tooltip-trigger {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .tooltip-content {
    padding: 8px 0;
  }

  .tooltip-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--color-text-1);
    border-bottom: 1px solid var(--color-border-2);
    padding-bottom: 4px;
  }

  .tooltip-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }

  .stat-label {
    color: var(--color-text-2);
  }

  .stat-value {
    font-weight: 500;
    color: var(--color-text-1);

    &.stat-pending {
      color: rgb(var(--gray-6));
    }

    &.stat-running {
      color: rgb(var(--orange-6));
    }

    &.stat-success {
      color: rgb(var(--green-6));
    }

    &.stat-failed {
      color: rgb(var(--red-6));
    }
  }

  .tooltip-note {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--color-border-2);
    font-size: 11px;
    color: var(--color-text-3);
    font-style: italic;
  }
</style>

<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <!-- 内容模糊搜索 -->
          <a-input-search
            v-model="formModel.title"
            placeholder="请输入内容ID、标题、正文"
            allow-clear
            style="width: 280px"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
          <!-- 发布账户搜索 -->
          <a-input-search
            v-model="formModel.account"
            placeholder="请输入发布账号名称或ID"
            allow-clear
            style="width: 220px"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
          <!-- 平台 -->
          <platform-select
            v-model="formModel.platform"
            placeholder="请选择平台"
            style="width: 120px"
            @change="handleSubmit()"
          ></platform-select>
          <!-- 状态 -->
          <dict-radio
            v-model="formModel.status"
            :data-list="[{ label: '全部', value: '' }, ...publishNoteStatusM]"
            @change="handleSubmit()"
          />
        </a-space>
        <a-space> </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="table-card"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <!-- 平台列 -->
        <template #platform="{ record }: TableColumnSlot">
          <div class="platform-cell">
            <img
              class="platform-icon"
              :src="`icons/platform/${record.platform}.png`"
              :alt="record.platform"
            />
            <span class="platform-name">{{ record.platform_str }}</span>
          </div>
        </template>
        <template #content="{ record }: TableColumnSlot">
          <xhs-post-inline-simple
            :video-info="record"
            :show-content-id="true"
            :show-count-bar="true"
          />
        </template>
        <template #account_id="{ record }: TableColumnSlot">
          <account-info-row
            :id="record.from_account_id"
            :avatar="record.avatar_url"
            :name="record.from_account_name"
          />
        </template>
        <template #status="{ record }: TableColumnSlot">
          <status-badge :list="publishNoteStatusM" :value="record.status" />
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space size="mini" class="action-btns" wrap>
            <!-- 取消任务按钮 - 仅在排队中状态显示 -->
            <a-popconfirm
              v-if="record.status === -1"
              content="确定要取消此任务吗？"
              position="left"
              @ok="cancelTaskAction(record)"
            >
              <a-link @click.stop>取消</a-link>
            </a-popconfirm>

            <a-link @click="showContent(record)">查看</a-link>

            <a-link v-if="record.status !== 4" @click="showCommentModal(record)"
              >评论</a-link
            >

            <!-- 置顶/取消置顶 -->
            <a-popconfirm
              v-if="record.is_top && record.status !== 4 && record.status !== 3"
              content="确定要取消置顶吗？"
              position="left"
              @ok="handleTopAction(record)"
            >
              <a-link @click.stop>取消置顶</a-link>
            </a-popconfirm>
            <a-popconfirm
              v-else-if="record.status !== 4 && record.status !== 3"
              content="确定要置顶该内容吗？"
              position="left"
              @ok="handleTopAction(record)"
            >
              <a-link @click.stop>置顶</a-link>
            </a-popconfirm>

            <!-- 隐藏/取消隐藏 -->
            <a-popconfirm
              v-if="record.is_hide && record.status !== 4"
              content="确定要取消隐藏该内容吗？"
              position="left"
              @ok="handleHideAction(record)"
            >
              <a-link @click.stop>取消隐藏</a-link>
            </a-popconfirm>
            <a-popconfirm
              v-else-if="!record.is_hide && record.status !== 4"
              content="确定要隐藏该内容吗？"
              position="left"
              @ok="handleHideAction(record)"
            >
              <a-link @click.stop>隐藏</a-link>
            </a-popconfirm>

            <!-- 删除 -->
            <a-popconfirm
              v-if="record.status !== 4"
              content="确定要删除该内容吗？"
              position="left"
              @ok="handleDeleteAction(record)"
            >
              <a-link @click.stop>删除</a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <account-content-modal ref="contentRef"></account-content-modal>
    <note-comment-modal ref="commentModalRef" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { publishNoteStatusM } from '@/components/dict-select/dict-account';
  import { TableColumnSlot } from '@/global';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import AccountContentModal from '@/views/account/devices/account-content-modal.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import AccountInfoRow from '@/components/account-info-row.vue';
  import {
    IconEye,
    IconThumbUp,
    IconMessage,
    IconStar,
    IconArrowUp,
    IconPushpin,
    IconEyeInvisible,
    IconDelete,
    IconClose,
  } from '@arco-design/web-vue/es/icon';
  import XhsPostInlineSimple from '@/components/xhs-post-inline-simple.vue';
  import SegmentedProgressBar from './components/segmented-progress-bar.vue';
  import NoteCommentModal from './note-comment-modal.vue';

  // 定义 emit 事件
  const emit = defineEmits<{
    viewTask: [taskId: string | number];
  }>();

  const generateFormModel = () => {
    return {
      account: '', // 发布账户搜索
      account_id: '', // 账户id(用于展示单个账户下的作品)
      platform: '', // 平台
      status: '', // 状态
      title: '', // 内容模糊搜索
    };
  };

  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/account/accountProductionList', {
      ...data,
    });
  };

  const detailRef = ref();

  // 取消请求控制器
  let cancelToken: AbortController;

  const columns = [
    // {
    //   title: '内容ID',
    //   dataIndex: 'media_video_id',
    //   align: 'center',
    //   ellipsis: true,
    //   tooltip: true,
    //   slotName: 'media_video_id',
    // },
    {
      title: '发布内容',
      dataIndex: 'content',
    },
    // 互动数据
    // 状态
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '更新时间',
      dataIndex: 'modify_time',
      align: 'center',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '发布时间',
      dataIndex: 'create_time',
      align: 'center',
      ellipsis: true,
      tooltip: true,
    },
    // 发布账号
    {
      title: '发布账号',
      dataIndex: 'account_id',
      ellipsis: true,
      tooltip: true,
      width: 240,
    },
    // 平台
    {
      title: '平台',
      dataIndex: 'platform_str',
      align: 'center',
      ellipsis: true,
      slotName: 'platform',
      tooltip: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 160,
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  const contentRef = ref();
  function showContent(item: any, content?: any) {
    contentRef.value?.show(
      {
        avatar_url: item.avatar_url,
        account_id: item.from_account_id,
        account_name: item.from_account_name,
        platform: item.platform,
      },
      {
        media_video_id: item.media_video_id,
      }
    );
  }

  // 处理查看按钮点击事件 - 通过 emit 传递任务ID给父组件
  const handleViewTask = (record: any) => {
    emit('viewTask', record.id);
  };

  // 取消任务操作
  const cancelTaskAction = (record: any) => {
    const loadingInstance = Message.loading('正在取消任务...', 0);

    // 取消之前的请求
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/cancelPublish',
      {
        type: 'task', // 任务级别取消
        task_ids: [record.id],
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success('取消任务成功');
        // 刷新任务列表
        theTable.value?.fetchData();
      })
      .catch((error) => {
        Message.error(error.message || '取消任务失败');
      })
      .finally(() => {
        loadingInstance.close();
      });
  };

  // 置顶/取消置顶操作
  const handleTopAction = async (record: any) => {
    const action_value = record.is_top ? 'canceltop' : 'settop';
    try {
      loading.value = true;
      await request('/api/posts/topNote', {
        action_value,
        from_account_id: record.from_account_id,
        post_id: record.media_video_id,
      });
      Message.success(record.is_top ? '取消置顶成功' : '置顶成功');
      theTable.value?.fetchData();
    } catch (e: any) {
      Message.error(e?.message || '操作失败');
    } finally {
      loading.value = false;
    }
  };

  // 隐藏/取消隐藏操作
  const handleHideAction = async (record: any) => {
    const action_value = record.is_hide ? 'public' : 'private';
    try {
      loading.value = true;
      await request('/api/posts/hiddenNote', {
        action_value,
        from_account_id: record.from_account_id,
        post_id: record.media_video_id,
      });
      Message.success(record.is_hide ? '取消隐藏成功' : '隐藏成功');
      theTable.value?.fetchData();
    } catch (e: any) {
      Message.error(e?.message || '操作失败');
    } finally {
      loading.value = false;
    }
  };

  // 删除操作
  const handleDeleteAction = async (record: any) => {
    try {
      loading.value = true;
      await request('/api/posts/deleteNote', {
        from_account_id: record.from_account_id,
        post_id: record.media_video_id,
      });
      Message.success('删除成功');
      theTable.value?.fetchData();
    } catch (e: any) {
      Message.error(e?.message || '删除失败');
    } finally {
      loading.value = false;
    }
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  // 获取详细状态列表 - 如果API没有提供则生成模拟数据
  const getDetailStatusList = (record: any): number[] => {
    // 如果API响应中包含detail_status_list字段，直接使用
    if (record.detail_status_list && Array.isArray(record.detail_status_list)) {
      return record.detail_status_list;
    }

    // 否则根据现有数据生成模拟的详细状态列表
    const totalNum = record.total_num || 0;
    const successNum = record.success_num || 0;
    const failNum = record.fail_num || 0;
    const runningNum = Math.max(0, totalNum - successNum - failNum);

    const statusList: number[] = [];

    // 生成状态列表：先成功，再失败，最后是进行中/排队中
    for (let i = 0; i < successNum; i += 1) {
      statusList.push(2); // 已完成
    }
    for (let i = 0; i < failNum; i += 1) {
      statusList.push(3); // 失败
    }
    for (let i = 0; i < runningNum; i += 1) {
      // 随机分配进行中和排队中状态，让进度条更真实
      statusList.push(Math.random() > 0.7 ? 1 : 0); // 30%进行中，70%排队中
    }

    return statusList;
  };

  const commentModalRef = ref();
  function showCommentModal(record: any) {
    commentModalRef.value?.show({
      post_id: record.media_video_id,
      from_account_id: record.from_account_id,
    });
  }
</script>

<style scoped lang="less">
  .progress-box {
    display: flex;
    flex-direction: column;
  }
  .progress-box-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;

    .progress-info-wrapper {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .progress-box-info-item {
      margin-right: 10px;
      flex-shrink: 0;

      &:last-child {
        margin-right: 0;
      }
    }
  }
  .progress-bar {
    cursor: pointer;
  }

  .account-display-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .accounts-avatars {
    display: flex;
    align-items: center;
    position: relative;
    height: 32px;
    flex-shrink: 0;

    .avatar-wrap {
      position: relative;
      width: 32px;
      height: 32px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--color-fill-3);
      transition: all 0.2s ease;
      margin-left: -8px;

      &:first-child {
        margin-left: 0;
      }

      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .platform-icon {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid var(--color-fill-2);
        background: var(--color-fill-2);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .more-count {
      position: relative;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: var(--color-fill-2);
      border: 1px solid var(--color-fill-3);
      margin-left: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-text-2);
      font-size: 12px;
      font-weight: 500;
    }
  }

  .accounts-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
    margin-left: 8px;

    .account-names,
    .account-ids {
      display: inline-block;
      align-items: center;
      gap: 4px;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1;
      margin-top: 4px;
      max-width: 250px;
    }

    .account-name {
      font-size: 13px;
      color: var(--color-text-1);
      font-weight: 500;
    }

    .account-id {
      font-size: 12px;
      color: var(--color-text-3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .separator {
      color: var(--color-text-3);
      font-size: 12px;
      flex-shrink: 0;
    }

    .more-text {
      font-size: 12px;
      color: var(--color-text-3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  :deep(.arco-table-tr-empty) {
    height: 75vh !important;
  }

  // 表格单元格省略号样式优化
  :deep(.arco-table-td) {
    .arco-table-cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // 确保表格内容不会超出容器
  :deep(.arco-table-container) {
    overflow: hidden;
  }

  // 新增内容正文最大宽度和换行
  .thread-content-wrapper {
    max-width: 320px;
    white-space: pre-wrap;
    word-break: break-all;
    overflow-wrap: break-word;
  }

  // 响应式优化 - 在小屏幕上调整列宽
  @media (max-width: 1200px) {
    .account-display-wrapper {
      .accounts-info {
        .account-name {
          max-width: 80px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .account-display-wrapper {
      .accounts-info {
        .account-name {
          max-width: 60px;
        }
      }
    }

    .progress-box-info {
      .progress-info-wrapper {
        .progress-box-info-item {
          font-size: 11px;
          margin-right: 6px;
        }
      }
    }
  }

  .action-btns {
    gap: 2px !important;
    .action-btn {
      border-radius: 4px;
      transition: background 0.2s, color 0.2s;
      padding: 0 8px;
      height: 28px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #444;
      background: none;
      border: none;
      &.view {
        color: #165dff;
      }
      &.comment {
        color: #444;
      }
      &.top {
        color: #444;
      }
      &.hide {
        color: #444;
      }
      &.delete {
        color: #f53f3f;
      }
      &.cancel {
        color: #ff9900;
      }
      &:hover {
        background: #f4f6fa;
        color: #165dff;
      }
      &.delete:hover {
        background: #fff1f0;
        color: #f53f3f;
      }
    }
  }
  // 表格单元格样式
  .platform-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    .platform-icon {
      width: 20px;
      height: 20px;
    }

    .platform-name {
      font-size: 13px;
      font-weight: 500;
      text-transform: capitalize;
    }
  }
</style>

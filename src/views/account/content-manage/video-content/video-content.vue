<template>
  <div class="content-box">
    <a-card size="small">
      <div class="df">
        <folder-tree-list
          v-model:dir-id="formModel.dir_id"
          class="w-200"
          :apis="dirApis"
          :send-params="dirParams"
          :show-pop-menu="(nodeData:any) => nodeData.parent_id"
          @change="dirChange"
        ></folder-tree-list>
        <a-divider :margin="10" direction="vertical" />

        <div style="flex: 1">
          <search-form-bar
            v-model="formModel"
            :config="searchConfig"
            :no-bottom-margin="!multipleAction && !isSelect"
            @search="queryAction()"
            @reset="resetSearch"
            @sort="sortAction"
          >
            <template #right>
              <a-space>
                <a-button
                  v-if="!multipleAction && !isSelect"
                  type="outline"
                  @click="
                    selectKeys = [];
                    multipleAction = true;
                  "
                >
                  <template #icon>
                    <icon-unordered-list />
                  </template>
                  批量操作
                </a-button>
                <a-button type="primary" @click="addRef?.show(formModel)">
                  <template #icon><icon-upload /></template>
                  批量添加
                </a-button>
              </a-space>
            </template>
          </search-form-bar>
          <div class="jc-sb mb-10">
            <a-space v-if="multipleAction || isSelect">
              <a-checkbox
                :model-value="
                  selectKeys.length === list.length && selectKeys.length !== 0
                "
                :indeterminate="
                  selectKeys.length !== 0 && selectKeys.length !== list.length
                "
                @change="
                  (val:boolean) => (selectKeys = val ? list.map((item:any) => item.id) : [])
                "
              >
                全选
              </a-checkbox>
              <span>已选择{{ selectKeys.length }}个内容</span>
              <a-button
                v-if="!isSelect"
                type="text"
                @click="multipleAction = false"
              >
                <template #icon>
                  <icon-close-circle />
                </template>
                取消
              </a-button>
            </a-space>
            <a-space>
              <template v-if="multipleAction">
                <a-button
                  type="primary"
                  @click="moveRef?.show({ ...formModel, file_ids: selectKeys })"
                >
                  <template #icon><icon-undo /></template>
                  批量转移
                </a-button>
                <a-popconfirm
                  :content="`确认删除${selectKeys.length}个内容吗？`"
                  @ok="delAction(selectKeys)"
                >
                  <a-button type="primary" status="danger">
                    <template #icon><icon-delete /></template>
                    批量删除
                  </a-button>
                </a-popconfirm>
              </template>
            </a-space>
          </div>
          <!-- <a-divider :margin="10" /> -->
          <a-spin
            :loading="loading"
            class="video-list-container"
            :class="{ 'scroll-box': isScroll }"
          >
            <template v-if="list.length">
              <div class="video-grid">
                <div
                  v-for="item in list"
                  :key="item.id"
                  @click="selectChange(item)"
                >
                  <video-content-item
                    :item="item"
                    :select-keys="selectKeys"
                    :multiple-action="multipleAction"
                    :can-select="multipleAction || canSelect"
                    @edit="editRef?.show(item)"
                    @del="delAction([item.id])"
                  />
                </div>
              </div>
              <div class="mt-10 jc-sb">
                <div></div>
                <a-pagination
                  :current="formModel.page"
                  :page-size="formModel.pageSize"
                  :total="formModel.total"
                  show-page-size
                  show-total
                  :page-size-options="[12, 24, 48, 96]"
                  @change="(page:number) => queryAction(page)"
                  @page-size-change="(pageSize:number) => queryAction(1, pageSize)"
                />
              </div>
            </template>
            <div v-else class="df ai-cen" style="min-height: 600px">
              <a-empty />
            </div>
          </a-spin>
        </div>
      </div>
    </a-card>
    <content-move
      ref="moveRef"
      :send-params="dirParams"
      @refresh="queryAction()"
    />
    <content-video-add
      ref="addRef"
      :send-params="dirParams"
      @refresh="queryAction()"
    />
    <content-video-edit ref="editRef" @save="queryAction()" />
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import FolderTreeList from '@/components/fold-tree/folder-tree-list.vue';
  import SearchFormBar from '@/components/search-form-bar/index.vue';
  import request from '@/api/request';
  import { downloadLinkFile } from '@/utils/table-utils/table-util';
  import { sortFieldM } from '@/components/dict-select/dict-account';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ContentVideoAdd from './content-video-add.vue';
  import ContentVideoEdit from './content-video-edit.vue';
  import ContentMove from '../components/content-move.vue';

  const props = defineProps({
    isSelect: {
      type: Boolean,
      default: false,
    },
    canSelect: {
      type: Boolean,
      default: false,
    },
    isScroll: {
      type: Boolean,
      default: false,
    },
  });

  function defaultForm() {
    return {
      dir_id: 0,
      product_id: null,
      page: 1,
      pageSize: 24,
      total: 0,
      type: 'video',
      publish_count_min: null,
      publish_count_max: null,
      order_field: 'add_time',
      order_type: 'desc',
      create_user_ids: [],
      add_time: [],
      keyword: '',
      publish_num_min: null,
      publish_num_max: null,
    };
  }
  const formModel = ref(defaultForm());

  const dirApis = {
    list: '/api/material/dirList',
    save: '/api/material/dirSave',
  };
  const dirParams = computed(() => ({
    type: 'content-video',
  }));

  const loading = ref(false);
  const multipleAction = ref(false); // 批量操作
  const addRef = ref();
  const editRef = ref();
  const moveRef = ref();
  const list = ref<any[]>([]);
  const selectKeys = ref<number[]>([]);
  let cancelToken: AbortController;
  function getList() {
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    selectKeys.value = [];
    loading.value = true;
    request(
      '/api/content/list',
      {
        ...formModel.value,
      },
      cancelToken.signal
    )
      .then((res) => {
        list.value = res.data.data;
        formModel.value.page = res.data.current_page;
        formModel.value.pageSize = res.data.per_page;
        formModel.value.total = res.data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  getList();

  function selectChange(item: any) {
    if (selectKeys.value.includes(item.id)) {
      selectKeys.value = selectKeys.value.filter((val) => val !== item.id);
    } else {
      selectKeys.value.push(item.id);
    }
  }

  function queryAction(page?: number, pageSize?: number) {
    formModel.value.page = page || 1;
    formModel.value.pageSize = pageSize || formModel.value.pageSize;
    getList();
  }

  const sortAction = (type: string) => {
    formModel.value.order_type = type;
    if (formModel.value.order_field) {
      queryAction();
    }
  };

  function dirChange(val: any, { node }: any) {
    formModel.value.product_id = node?.product_id;
    queryAction();
  }

  function delAction(file_ids: any) {
    loading.value = true;
    request('/api/content/del', {
      file_ids,
    }).then(() => {
      queryAction();
    });
  }

  const downloading = ref(false);
  function downloadAction() {
    downloading.value = true;
    let items = list.value.filter((item) => selectKeys.value.includes(item.id));
    Promise.all(
      items.map((item: any) => {
        return downloadLinkFile(item.url, item.file_name);
      })
    ).finally(() => {
      downloading.value = false;
    });
  }

  function getSelectItems() {
    return list.value.filter((item) => selectKeys.value.includes(item.id));
  }

  function clearSelect() {
    selectKeys.value = [];
  }

  const searchConfig = {
    basicSearch: {
      field: 'keyword',
      placeholder: '搜索文件名称、标题和正文',
    },
    advancedSearch: {
      items: [
        {
          type: 'rangePicker',
          label: '上传时间',
          field: 'add_time',
          props: {
            format: 'YYYY-MM-DD',
          },
        },
        {
          type: 'rangePopoverPair',
          label: '发布次数',
          props: {
            minField: 'publish_num_min',
            maxField: 'publish_num_max',
            ranges: [
              { label: '不限', min: undefined, max: undefined },
              { label: '10以下', min: undefined, max: 10 },
              { label: '10-50', min: 10, max: 50 },
              { label: '50-100', min: 50, max: 100 },
              { label: '100以上', min: 100, max: undefined },
            ],
          },
        },
        {
          type: 'UserSelectPopover',
          label: '上传人',
          field: 'create_user_ids',
          props: {
            placeholder: '请选择',
          },
        },
        {
          type: 'sort',
          label: '排序字段',
          componentType: 'select',
          props: {
            fields: sortFieldM,
          },
        },
      ],
    },
    defaultValues: {
      keyword: '',
      add_time: [],
      publish_num_min: null,
      publish_num_max: null,
      order_field: 'add_time',
      order_type: 'desc',
      create_user_ids: [],
    },
  };

  function resetSearch() {
    formModel.value = defaultForm();
    queryAction();
  }

  defineExpose({
    getSelectItems,
    clearSelect,
  });
</script>

<style scoped lang="less">
  .video-list-container {
    border-radius: 8px;
    width: 100%;
    min-height: calc(100vh - 172px);
    &.scroll-box {
      min-height: 65vh;
      max-height: 65vh;
      overflow-y: auto;
    }
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  @media (max-width: 768px) {
    .video-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 12px;
    }
  }
</style>

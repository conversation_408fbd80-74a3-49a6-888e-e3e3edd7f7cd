<template>
  <d-modal
    :visible="visible"
    width="800px"
    title="编辑内容"
    :ok-loading="loading"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item label="视频">
        <div class="img-card">
          <div class="img-card-preview">
            <div
              class="image-blur-bg"
              :style="{ backgroundImage: `url(${formModel.origin_cover})` }"
            ></div>
            <img
              :src="formModel.origin_cover"
              :alt="formModel.name"
              class="image-cover"
            />
            <div
              v-if="formModel.name"
              class="image-name"
              :title="formModel.name"
            >
              {{ formModel.name }}
            </div>
            <div
              class="image-preview-icon"
              @click.stop="
                $previewPlayer({
                  videoUrl: formModel.video_url,
                  title: formModel.name,
                  extra: formModel,
                })
              "
            >
              <icon-play-circle />
            </div>
            <div class="image-actions">
              <a-button
                type="primary"
                size="mini"
                @click.stop="videoRef?.show()"
              >
                替换
              </a-button>
            </div>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="封面">
        <div class="img-card">
          <div class="img-card-preview">
            <div
              class="image-blur-bg"
              :style="{ backgroundImage: `url(${formModel.cover_url})` }"
            ></div>
            <img
              :src="formModel.cover_url"
              :alt="formModel.name"
              class="image-cover"
            />
            <div
              v-if="formModel.name"
              class="image-name"
              :title="formModel.name"
            >
              {{ formModel.name }}
            </div>
            <div
              class="image-preview-icon"
              @click.stop="
                $previewPlayer({
                  mediaList: [
                    {
                      url: formModel.cover_url,
                      title:
                        formModel.name || formModel.cover_url.split('/').pop(),
                      extra: formModel,
                    },
                  ],
                })
              "
            >
              <icon-search />
            </div>
            <div class="image-actions">
              <a-button
                type="primary"
                size="mini"
                @click.stop="thumbRef?.show()"
              >
                替换
              </a-button>
            </div>
          </div>
        </div>
      </a-form-item>
      <a-form-item
        label="标题"
        field="title"
        :rules="[
          { required: true, message: '请填写标题' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 20) {
                callback('标题不能超过20个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-input
          v-model="formModel.title"
          placeholder="请输入"
          :max-length="20"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        >
        </a-input>
      </a-form-item>
      <a-form-item
        label="正文"
        field="describe"
        :rules="[
          { required: true, message: '请填写正文' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 900) {
                callback('正文不能超过900个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-textarea
          v-model="formModel.describe"
          :auto-size="{ minRows: 8, maxRows: 12 }"
          placeholder="请输入"
          :max-length="900"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        >
        </a-textarea>
      </a-form-item>
    </a-form>
    <!--选择视频-->
    <video-select
      ref="videoRef"
      :is-single="true"
      @save="setVideo"
    ></video-select>
    <!--选择封面-->
    <thumb-select
      ref="thumbRef"
      :is-single="true"
      @save="setThumb"
    ></thumb-select>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { ref } from 'vue';
  import { requiredRule, getCharacterLength, sliceString } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';
  import VideoSelect from '@/views/account/content-manage/components/video-select.vue';
  import ThumbSelect from '@/views/account/content-manage/components/thumb-select.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import VideoCardItem from '@/views/account/material-manage/components/video-card-item.vue';
  import ImageCardItem from '@/views/account/material-manage/components/image-card-item.vue';

  function defaultForm() {
    return {
      id: '',
      product_id: '',
      dir_id: '',
      type: 'video',
      name: '',
      title: '',
      describe: '',
      cover_url: '',
      video_url: '',
      origin_cover: '',
    };
  }

  const formModel = ref(defaultForm());
  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const videoRef = ref();
  const thumbRef = ref();
  const emits = defineEmits(['save', 'close']);

  function setVideo(items: any[]) {
    if (items?.length) {
      formModel.value.cover_url = items[0].thumb;
      formModel.value.origin_cover = items[0].thumb;
      formModel.value.video_url = items[0].url;
    }
  }
  function setThumb(items: any[]) {
    if (items?.length) {
      formModel.value.cover_url = items[0].url;
    }
  }

  function handleCancel() {
    visible.value = false;
    loading.value = false;
    emits('close');
    formRef.value?.clearValidate();
  }
  function handleBeforeOk() {
    formRef.value?.validate((err: boolean) => {
      if (!err) {
        loading.value = true;
        request('/api/content/update', formModel.value)
          .then(() => {
            Message.success('保存成功');
            emits('save');
            handleCancel();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  function show(dinfo: any) {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
    }
    visible.value = true;
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 160px;
    margin: 0 8px 8px 0;
    border: 1px solid var(--color-border-1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
      }

      .image-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .img-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      z-index: 2;
    }

    .image-actions {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 4px 8px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      display: flex;
      gap: 8px;
      opacity: 1;
      transform: translateY(100%);
      transition: all 0.3s ease;
      z-index: 2;

      .arco-btn {
        flex: 1;
        height: 20px;
        font-size: 10px;
        padding: 2px 8px;
      }
    }
  }
</style>

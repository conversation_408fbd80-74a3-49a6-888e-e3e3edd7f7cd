<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="添加视频内容"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    :width="formModel.step === 1 ? '800px' : '1300px'"
    @cancel="handleCancel"
  >
    <a-form
      v-if="formModel.step === 1"
      ref="formRef"
      :model="formModel"
      auto-label-width
    >
      <a-form-item label="产品" field="product_id">
        <request-select
          v-model="formModel.product_id"
          api="product"
          @change="formModel.dir_id = ''"
        />
      </a-form-item>
      <a-form-item label="文件夹" field="dir_id">
        <folder-tree-select
          v-model="formModel.dir_id"
          :send-params="dirParams"
          :disabled="!formModel.product_id"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/dirList"
          :format-data="(arr:any) => arr?.children || []"
          :allow-clear="false"
        ></folder-tree-select>
      </a-form-item>
      <a-form-item label="内容名称" field="name" :rules="requiredRule">
        <a-input
          ref="iptRef"
          v-model="formModel.name"
          allow-clear
          placeholder="请输入"
        />
        <template #extra>
          <div>
            <template v-for="item in nameMap" :key="item.value">
              <a-button
                type="text"
                class="mr-10 p-0"
                size="mini"
                @click="insertName(item.value)"
              >
                +{{ item.label }}
              </a-button>
            </template>
          </div>
        </template>
      </a-form-item>
      <a-form-item
        label="默认标题"
        field="title"
        :rules="[
          { required: true, message: '请填写标题' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 20) {
                callback('标题不能超过20个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-input
          v-model="formModel.title"
          placeholder="请输入"
          allow-clear
          :max-length="20"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        />
      </a-form-item>
      <a-form-item
        label="默认正文"
        field="describe"
        :rules="[
          { required: true, message: '请填写正文' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 900) {
                callback('正文不能超过900个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-textarea
          v-model="formModel.describe"
          :auto-size="{ maxRows: 4, minRows: 4 }"
          placeholder="请输入"
          allow-clear
          :max-length="900"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        />
      </a-form-item>
    </a-form>
    <template v-else>
      {{ contentList }}
      <a-card title="内容列表">
        <template #extra>
          <a-space>
            <a-button
              size="small"
              type="primary"
              @click="videoRef?.show(formModel)"
            >
              添加视频
            </a-button>
            <a-button
              size="small"
              type="primary"
              :disabled="!selectedKeys.length"
              @click="
                setCurIndex();
                thumbRef?.show(formModel);
              "
            >
              设置封面
            </a-button>
            <a-button
              size="small"
              type="primary"
              :disabled="!selectedKeys.length"
              @click="
                setCurIndex();
                titleRef?.show();
              "
            >
              修改标题
            </a-button>
            <a-button
              size="small"
              type="primary"
              :disabled="!selectedKeys.length"
              @click="
                setCurIndex();
                describeRef?.show();
              "
            >
              修改正文
            </a-button>
            <a-popconfirm
              :content="`确定删除这${selectedKeys.length}项吗？`"
              @ok="
                contentList = contentList.filter(
                  (item: any) => !selectedKeys.includes(item.key)
                );
                selectedKeys = [];
              "
            >
              <a-button
                size="small"
                status="danger"
                :disabled="!selectedKeys.length"
              >
                批量删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
        <a-table
          v-model:selected-keys="selectedKeys"
          :row-selection="{ type: 'checkbox', showCheckedAll: true }"
          :data="contentList"
          :pagination="false"
          :scroll="{ maxHeight: '60vh' }"
        >
          <template #columns>
            <a-table-column title="内容名称" data-index="name" align="center">
              <template #cell="{ record }: TableColumnSlot">
                <a-input v-model="record.name" placeholder="请输入内容名称" />
              </template>
            </a-table-column>
            <a-table-column
              title="视频"
              data-index="video_url"
              align="center"
              :width="180"
            >
              <template #cell="{ record, rowIndex }: TableColumnSlot">
                <div class="img-card">
                  <div class="img-card-preview">
                    <div
                      class="image-blur-bg"
                      :style="{
                        backgroundImage: `url(${record.origin_cover})`,
                      }"
                    ></div>
                    <img
                      :src="record.origin_cover"
                      :alt="record.file_name"
                      class="image-cover"
                    />
                    <div
                      v-if="record.file_name"
                      class="image-name"
                      :title="record.file_name"
                    >
                      {{ record.file_name }}
                    </div>
                    <div
                      class="image-preview-icon"
                      @click.stop="
                        $previewPlayer({
                          videoUrl: record.video_url,
                          title: record.file_name,
                          extra: record,
                        })
                      "
                    >
                      <icon-play-circle />
                    </div>
                    <div class="image-actions">
                      <a-button
                        type="primary"
                        size="mini"
                        @click.stop="
                          curIndex = [rowIndex];
                          videoRef?.show(formModel);
                        "
                      >
                        替换
                      </a-button>
                    </div>
                  </div>
                </div>
              </template>
            </a-table-column>
            <a-table-column
              title="封面"
              data-index="cover_url"
              align="center"
              :width="180"
            >
              <template #cell="{ record, rowIndex }: TableColumnSlot">
                <div class="img-card">
                  <div class="img-card-preview">
                    <div
                      class="image-blur-bg"
                      :style="{ backgroundImage: `url(${record.cover_url})` }"
                    ></div>
                    <img
                      :src="record.cover_url"
                      :alt="record.file_name"
                      class="image-cover"
                    />
                    <div
                      v-if="record.file_name"
                      class="image-name"
                      :title="record.file_name"
                    >
                      {{ record.file_name }}
                    </div>
                    <div
                      class="image-preview-icon"
                      @click.stop="
                        $previewPlayer({
                          mediaList: [
                            {
                              url: record.cover_url,
                              title: record.file_name,
                              extra: record,
                            },
                          ],
                        })
                      "
                    >
                      <icon-search />
                    </div>
                    <div class="image-actions">
                      <a-button
                        type="primary"
                        size="mini"
                        @click.stop="
                          curIndex = [rowIndex];
                          thumbRef?.show(formModel);
                        "
                      >
                        替换
                      </a-button>
                    </div>
                  </div>
                </div>
              </template>
            </a-table-column>
            <a-table-column title="标题" data-index="title" align="center">
              <template #cell="{ record }: TableColumnSlot">
                <a-textarea
                  v-model="record.title"
                  placeholder="请输入标题"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                  :max-length="20"
                  show-word-limit
                />
              </template>
            </a-table-column>
            <a-table-column
              title="正文"
              data-index="describe"
              :width="340"
              align="center"
            >
              <template #cell="{ record }: TableColumnSlot">
                <a-textarea
                  v-model="record.describe"
                  placeholder="请输入正文"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                  :max-length="900"
                  show-word-limit
                />
              </template>
            </a-table-column>
            <a-table-column
              title="操作"
              data-index="action"
              :width="120"
              align="center"
            >
              <template #cell="{ rowIndex, record }: TableColumnSlot">
                <a-space>
                  <a-popconfirm
                    :content="`确定删除此项吗？`"
                    @ok="
                      contentList.splice(rowIndex, 1);
                      selectedKeys = selectedKeys.filter(
                        (item: string) => item !== record.key
                      );
                    "
                  >
                    <a-link status="danger"> 删除 </a-link>
                  </a-popconfirm>
                  <a-link
                    @click="
                      contentList.splice(
                        rowIndex + 1,
                        0,
                        cloneDeep({ ...record, key: `${Date.now()}` })
                      )
                    "
                  >
                    复制
                  </a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-card>
    </template>
    <template #footer>
      <a-spin :loading="okLoading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button
            v-if="formModel.step === 1"
            type="primary"
            @click="nextStep(2)"
          >
            下一步
          </a-button>
          <template v-else>
            <a-button @click="formModel.step = 1"> 上一步 </a-button>
            <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
          </template>
        </a-space>
      </a-spin>
    </template>
    <!--选择视频-->
    <video-select
      ref="videoRef"
      :is-single="Boolean(curIndex.length)"
      @save="addContentItems"
      @close="curIndex = []"
    ></video-select>
    <!--选择封面-->
    <thumb-select
      ref="thumbRef"
      :is-single="Boolean(curIndex.length)"
      @save="setThumb"
      @close="curIndex = []"
    ></thumb-select>
    <!--修改标题-->
    <input-modal
      ref="titleRef"
      @save="setTitle"
      @close="curIndex = []"
    ></input-modal>
    <!--修改正文-->
    <input-modal
      ref="describeRef"
      is-area
      @save="setDescribe"
      @close="curIndex = []"
    ></input-modal>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, ref } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    requiredRule,
    setCaretPosition,
    getCharacterLength,
    sliceString,
  } from '@/utils/util';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import VideoSelect from '@/views/account/content-manage/components/video-select.vue';
  import { cloneDeep, isUndefined } from 'lodash';
  import ThumbSelect from '@/views/account/content-manage/components/thumb-select.vue';
  import InputModal from '@/components/input-modal/input-modal.vue';
  import dayjs from 'dayjs';
  import VideoCardItem from '@/views/account/material-manage/components/video-card-item.vue';
  import ImageCardItem from '@/views/account/material-manage/components/image-card-item.vue';

  const visible = ref(false);
  const okLoading = ref(false);
  const curIndex = ref<number[]>([]); // 当前正在修改的项
  const emits = defineEmits(['refresh']);
  const nameMap = [
    { label: '视频名称', value: '视频名称' },
    { label: '日期', value: '日期' },
    { label: '序号', value: '序号' },
  ];
  function defaultForm() {
    return {
      step: 1,
      dir_id: null,
      product_id: '',
      name: '{视频名称}_{日期}_{序号}',
      title: '',
      describe: '',
      type: 'video',
    };
  }
  const props = defineProps({
    sendParams: {
      type: [Object],
      default: null,
    },
  });
  const formModel = ref(defaultForm());
  const formRef = ref();
  const iptRef = ref();
  const videoRef = ref();
  const thumbRef = ref();
  const titleRef = ref();
  const describeRef = ref();
  const contentList = ref<any[]>([]);
  const selectedKeys = ref<string[]>([]);
  const dirParams = computed(() => ({
    ...props.sendParams,
    product_id: formModel.value.product_id,
  }));

  const insertName = (name: string) => {
    // if (name === '序号') {
    //  name = '起始序号-01';
    // }
    // @ts-ignore
    let iptDom = iptRef.value?.inputRef;
    let newName: string = iptDom.value;
    let index = iptDom.selectionStart || newName?.length || 0;
    if (!newName) {
      newName = `{${name}}`;
    } else {
      newName = `${newName.slice(0, index)}_{${name}}${newName.slice(index)}`;
    }
    formModel.value.name = newName;
    setCaretPosition(iptDom, index + name.length + 3);
  };

  function nextStep(step: number) {
    formRef.value?.validate((err: any) => {
      if (!err) {
        formModel.value.step = step;
      }
    });
  }

  // 根据表格选择的key设置index
  function setCurIndex() {
    curIndex.value = [];
    contentList.value.forEach((item, index) => {
      if (selectedKeys.value.includes(item.key)) {
        curIndex.value.push(index);
      }
    });
  }

  // 添加/修改视频
  function addContentItems(items: any[]) {
    if (items?.length) {
      // 单选就替换
      if (curIndex.value.length) {
        curIndex.value.forEach((index) => {
          contentList.value[index].cover_url = items[0].thumb;
          contentList.value[index].origin_cover = items[0].thumb;
          contentList.value[index].video_url = items[0].url;
          contentList.value[index].file_name = items[0].file_name;
        });
      } else {
        contentList.value = [
          ...contentList.value,
          ...items.map((item, index) => ({
            ...formModel.value,
            key: `${Date.now()}_${index}`,
            cover_url: item.thumb,
            origin_cover: item.thumb,
            video_url: item.url,
            file_name: item.file_name,
          })),
        ];
      }
    }
  }
  // 设置视频封面
  function setThumb(items: any[]) {
    if (items?.length && curIndex.value.length) {
      curIndex.value.forEach((index) => {
        contentList.value[index].cover_url = items[0].url;
      });
    }
  }
  // 设置标题
  function setTitle(title: string) {
    if (curIndex.value.length) {
      curIndex.value.forEach((index) => {
        contentList.value[index].title = title;
      });
    }
  }
  // 设置正文
  function setDescribe(title: string) {
    if (curIndex.value.length) {
      curIndex.value.forEach((index) => {
        contentList.value[index].describe = title;
      });
    }
  }

  function handleCancel() {
    visible.value = false;
    emits('refresh');
    formRef.value?.clearValidate();
  }

  function handleBeforeOk() {
    if (!contentList.value.length) {
      return Message.error('请添加内容');
    }
    contentList.value.forEach((item, index) => {
      if (item.name.includes('{视频名称}')) {
        item.name = item.name.replaceAll(
          '{视频名称}',
          item.file_name.split('.').slice(0, -1).join('.')
        );
      }
      if (item.name.includes('{日期}')) {
        item.name = item.name.replaceAll('{日期}', dayjs().format('YYYYMMDD'));
      }
      if (item.name.includes('{序号}')) {
        item.name = item.name.replaceAll('{序号}', index + 1);
      }
    });
    okLoading.value = true;
    request('/api/content/save', {
      content_list: contentList.value,
    })
      .then(() => {
        Message.success('操作成功');
        handleCancel();
      })
      .finally(() => {
        okLoading.value = false;
      });
  }

  function show(ddata: any) {
    formModel.value = defaultForm();
    formModel.value.product_id = ddata?.product_id;
    formModel.value.dir_id = ddata?.dir_id || null;
    contentList.value = [];
    visible.value = true;
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 160px;
    margin: 0 auto;
    border: 1px solid var(--color-border-1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
      }

      .image-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .img-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      z-index: 2;
    }

    .image-actions {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 8px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      display: flex;
      gap: 8px;
      opacity: 0;
      transform: translateY(100%);
      transition: all 0.3s ease;
      z-index: 2;

      .arco-btn {
        flex: 1;
        height: 16px;
        font-size: 10px;
        padding: 2px 8px;
      }
    }
  }
</style>

<template>
  <div
    class="video-card"
    :class="{
      'is-selected': selectKeys.includes(item.id) && canSelect,
      'is-mini': size === 'mini',
    }"
    @click="$emit('select', item)"
  >
    <div class="video-card-preview">
      <div
        class="video-blur-bg"
        :style="{ backgroundImage: `url(${item.cover_url})` }"
      ></div>
      <img :src="item.cover_url" class="video-cover" />
      <div class="video-name">{{ item.name || item.content_name }}</div>
      <div class="ai-icon" :class="{ mini: size === 'mini' }">
        <icon-code-sandbox
          v-if="item.media_content_id"
          size="18"
          style="color: #fff; margin-right: 5px"
        />
        <img
          v-else-if="item.ai_flag == 1"
          src="@/assets/images/ai-icon-3.png"
          class="mr-5"
          alt=""
        />
        <mix-flag-logo
          :size="size === 'mini' ? 'mini' : 'default'"
          type="video"
        />
      </div>
      <div v-if="item.publish_num > 0" class="publish-num-tag">
        <span class="publish-num-text">已发布{{ item.publish_num }}次</span>
      </div>
      <div v-if="isToday(item.add_time) && size !== 'mini'" class="new-tag">
        <img src="@/assets/images/new-tag.jpeg" alt="" />
      </div>
      <div
        class="video-play-icon"
        @click.stop="
          $previewPlayer({
            videoUrl: item.video_url,
            title: item.name,
            extra: item,
          })
        "
      >
        <icon-play-circle-fill />
      </div>
      <div v-if="canSelect" class="video-select-indicator">
        <icon-check-circle-fill />
      </div>
      <div v-if="openEdit" class="video-actions">
        <a-button size="mini" type="primary" @click.stop="$emit('edit')">
          <template #icon>
            <icon-edit size="14" />
          </template>
        </a-button>
        <!-- 下载按钮 -->
        <a-button size="mini" @click.stop="$emit('download')">
          <template #icon>
            <icon-download class="primary_color" size="14" />
          </template>
        </a-button>
        <a-popconfirm
          :content="`确定删除此项吗？`"
          @click.stop
          @ok="$emit('del')"
        >
          <a-button size="mini">
            <template #icon>
              <icon-delete size="14" class="del-red-color" />
            </template>
          </a-button>
        </a-popconfirm>
      </div>
    </div>
    <div class="video-card-info">
      <div class="video-title" :title="item.title">{{
        item.title || '标题'
      }}</div>
      <div class="video-meta">
        <span class="video-desc" :title="item.describe">{{
          item.describe || '无描述'
        }}</span>
      </div>
    </div>
    <div
      v-if="isRemove"
      class="video-card-remove"
      @click.stop="$emit('remove')"
    >
      <icon-close size="12" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import relativeTime from 'dayjs/plugin/relativeTime';
  import MixFlagLogo from '@/views/account/content-manage/components/mix-flag-logo.vue';
  import { isToday } from '@/utils/util';

  import 'dayjs/locale/zh-cn';

  dayjs.extend(relativeTime);
  dayjs.locale('zh-cn');

  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
    selectKeys: {
      type: Array,
      default: () => [],
    },
    isRemove: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'normal',
    },
    canSelect: {
      type: Boolean,
      default: true,
    },
    openEdit: {
      type: Boolean,
      default: false,
    },
  });

  defineEmits(['select', 'remove', 'edit', 'del', 'download']);

  function formatDuration(seconds: number) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  function formatFileSize(bytes: number) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / k ** i).toFixed(1)} ${sizes[i]}`;
  }

  function formatDate(date: string) {
    return dayjs(date).format('YYYY-MM-DD');
  }
</script>

<style scoped lang="less">
  .video-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 228px;
    border: 1px solid var(--color-border-1);
    .video-select-indicator {
      position: absolute;
      top: 0px;
      left: 2px;
      color: #fff;
      font-size: 20px;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 999;
    }

    &.is-mini {
      width: 130px;
      .video-play-icon {
        font-size: 32px;
      }
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);

      .video-play-icon {
        opacity: 1;
        &:hover {
          opacity: 0.95;
          scale: 1.2;
        }
      }
    }

    &.is-selected {
      position: relative;
      &::before {
        content: '已选择';
        position: absolute;
        left: -1px;
        top: 0;
        background: rgb(var(--primary-6));
        color: #fff;
        font-size: 10px;
        padding: 4px 8px;
        width: max-content;
        height: max-content;
        border-radius: var(--border-radius-large) 0 var(--border-radius-large) 0;
        z-index: 99;
      }
      .video-select-indicator {
        opacity: 0;
      }
    }
  }

  .video-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .video-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .video-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .video-name {
      position: absolute;
      right: 0px;
      bottom: 0px;
      padding: 0px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 11px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .ai-icon {
      position: absolute;
      right: 5px;
      bottom: 16px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        height: 100%;
        width: 18px;
        height: 18px;
      }
      &.mini {
        img {
          width: 16px;
          height: 16px;
        }
      }
    }
    .publish-num-tag {
      position: absolute;
      left: 5px;
      bottom: 20px;
      z-index: 2;
      .publish-num-text {
        font-size: 10px;
        color: #fff;
      }
    }
    .new-tag {
      position: absolute;
      left: 5px;
      bottom: 0px;
      z-index: 2;
      img {
        width: 28px;
        border-radius: 5px;
      }
    }

    .video-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      color: #fff;
      font-size: 40px;
      opacity: 0;
      translate: -50% -50%;
      transition: all 0.5s ease;
      z-index: 99;
    }

    .video-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 99;
    }
  }

  .video-card-info {
    padding: 6px 8px;
  }

  .video-title {
    font-size: 12px;
    color: var(--color-text-1);
    line-height: 1.2;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
  }

  .video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--color-text-3);

    .video-size {
      background: var(--color-fill-2);
      padding: 2px 6px;
      border-radius: 4px;
    }

    .video-desc {
      color: var(--color-text-2);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .video-card-remove {
    position: absolute;
    top: 4px;
    right: 8px;
    width: 16px;
    height: 16px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 99;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
    }
  }

  .video-card:hover .video-card-remove {
    opacity: 1;
  }

  .video-card:hover .video-actions {
    opacity: 1;
  }
</style>

<template>
  <d-modal
    :visible="visible"
    width="1250px"
    title="选择素材"
    :body-style="{ maxHeight: '80vh' }"
    unmount-on-close
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-tabs>
      <a-tab-pane key="image" :title="`图片`">
        <image-manage
          ref="imageListRef"
          :is-single="isSingle"
          :can-select="true"
          :is-select="true"
          :auto-request="!params.product_id"
        >
        </image-manage>
      </a-tab-pane>
      <a-tab-pane key="video" :title="`视频`">
        <video-manage
          ref="videoListRef"
          :is-single="isSingle"
          :can-select="true"
          :is-select="true"
          :auto-request="!params.product_id"
        >
        </video-manage>
      </a-tab-pane>
      <!-- 封面 -->
      <!-- <a-tab-pane key="thumb" :title="`封面`">
        <thumb-manage
          ref="thumbListRef"
          :is-single="isSingle"
          :can-select="true"
          :is-select="true"
          :auto-request="!params.product_id"
        >
        </thumb-manage>
      </a-tab-pane> -->
    </a-tabs>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { nextTick, ref, computed, watch } from 'vue';
  import ImageManage from '@/views/account/material-manage/image-manage/image-manage.vue';
  import VideoManage from '@/views/account/material-manage/video-manage/video-manage.vue';
  import ThumbManage from '@/views/account/material-manage/thumb-manage/thumb-manage.vue';

  const props = defineProps({
    // 是不是单选
    isSingle: {
      type: Boolean,
      default: false,
    },
  });

  const imageListRef = ref();
  const videoListRef = ref();
  const thumbListRef = ref();

  const visible = ref(false);
  const emits = defineEmits(['save', 'close']);
  const selectedImageCount = ref(0);
  const selectedVideoCount = ref(0);
  const selectedThumbCount = ref(0);

  function handleCancel() {
    visible.value = false;
    emits('close');
    nextTick(() => {
      imageListRef.value?.getSelectItems();
      videoListRef.value?.getSelectItems();
      thumbListRef.value?.getSelectItems();
    });
  }

  function handleBeforeOk() {
    const imageItems = imageListRef.value?.getSelectItems() || [];
    const videoItems = videoListRef.value?.getSelectItems() || [];
    const thumbItems = videoListRef.value?.getSelectItems() || [];

    // 合并并格式化选中的项目
    const selectedItems = [...imageItems, ...videoItems, ...thumbItems].map(
      (item) => ({
        ...item,
        type: item.url?.match(/\.(mp4|mov|avi|wmv)$/i) ? 'video' : 'image',
        file_name: item.file_name || item.name,
        url: item.url || item.file_url,
      })
    );

    emits('save', selectedItems);
    handleCancel();
  }

  const params = ref<any>({});
  function show(ddata: any) {
    imageListRef.value?.clearSelect();
    videoListRef.value?.clearSelect();
    thumbListRef.value?.clearSelect();
    visible.value = true;
    params.value = ddata || {};
    nextTick(() => {
      imageListRef.value?.setDirId(params.value.product_id);
      videoListRef.value?.setDirId(params.value.product_id);
      thumbListRef.value?.setDirId(params.value.product_id);
    });
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="批量转移"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="460px"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item label="文件夹" field="dir_id" :rules="requiredRule">
        <folder-tree-select
          v-model="formModel.dir_id"
          :send-params="dirParams"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/dirList"
          :format-data="(arr:any) => arr?.children || []"
          :allow-clear="false"
        ></folder-tree-select>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, ref } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule } from '@/utils/util';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';

  const visible = ref(false);
  const okLoading = ref(false);
  const emits = defineEmits(['refresh']);
  function defaultForm() {
    return {
      file_ids: [],
      dir_id: '',
    };
  }
  const props = defineProps({
    sendParams: {
      type: [Object],
      default: null,
    },
  });
  const formModel = ref(defaultForm());
  const formRef = ref();
  const dirParams = computed(() => ({
    ...props.sendParams,
  }));

  function handleCancel() {
    visible.value = false;
    formModel.value = defaultForm();
    emits('refresh');
    formRef.value?.clearValidate();
  }

  function handleBeforeOk() {
    formRef.value?.validate((err: any) => {
      if (!err) {
        okLoading.value = true;
        request('/api/content/move', {
          ...formModel.value,
        })
          .then(() => {
            Message.success('操作成功');
            handleCancel();
          })
          .finally(() => {
            okLoading.value = false;
          });
      }
    });
  }

  function show(params: any) {
    formModel.value.file_ids = params.file_ids;
    visible.value = true;
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

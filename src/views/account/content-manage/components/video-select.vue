<template>
  <d-modal
    :visible="visible"
    width="1250px"
    title="选择视频"
    :body-style="{ maxHeight: '80vh' }"
    unmount-on-close
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <video-manage
      ref="listRef"
      :is-single="isSingle"
      :can-select="true"
      :is-select="true"
      :auto-request="!params.product_id"
    >
    </video-manage>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import VideoManage from '@/views/account/material-manage/video-manage/video-manage.vue';
  import { nextTick, ref } from 'vue';

  const props = defineProps({
    // 是不是单选
    isSingle: {
      type: Boolean,
      default: false,
    },
  });
  const listRef = ref();
  const visible = ref(false);
  const emits = defineEmits(['save', 'close']);

  function handleCancel() {
    visible.value = false;
    emits('close');
    nextTick(() => {
      listRef.value?.getSelectItems();
    });
  }
  function handleBeforeOk() {
    emits('save', listRef.value?.getSelectItems());
    handleCancel();
  }
  const params = ref<any>({});
  function show(ddata: any) {
    listRef.value?.clearSelect();
    visible.value = true;
    params.value = ddata || {};
    nextTick(() => {
      listRef.value?.setDirId(params.value.product_id);
    });
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

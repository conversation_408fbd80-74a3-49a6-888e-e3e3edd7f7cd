<template>
  <a-card title="内容列表">
    <template #extra>
      <a-space>
        <a-button
          size="small"
          type="primary"
          @click="
            curIndex = [];
            videoRef?.show(formModel);
          "
        >
          添加视频
        </a-button>
        <a-button
          size="small"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="
            setCurIndex();
            thumbRef?.show(formModel);
          "
        >
          设置封面
        </a-button>
        <a-button
          size="small"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="
            setCurIndex();
            titleRef?.show();
          "
        >
          修改标题
        </a-button>
        <a-button
          size="small"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="
            setCurIndex();
            describeRef?.show();
          "
        >
          修改正文
        </a-button>
        <a-popconfirm
          :content="`确定删除这${selectedKeys.length}项吗？`"
          @ok="handleBatchDelete"
        >
          <a-button
            size="small"
            status="danger"
            :disabled="!selectedKeys.length"
          >
            批量删除
          </a-button>
        </a-popconfirm>
      </a-space>
    </template>
    <a-table
      v-model:selected-keys="selectedKeys"
      :row-selection="rowSelection"
      :data="modelValue"
      :pagination="false"
      :scroll="{ maxHeight: '60vh' }"
    >
      <template #columns>
        <a-table-column title="内容名称" data-index="name" align="center">
          <template #cell="{ record }: TableColumnSlot">
            <a-input v-model="record.name" placeholder="请输入内容名称" />
          </template>
        </a-table-column>
        <a-table-column
          title="视频"
          data-index="video_url"
          align="center"
          :width="180"
        >
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <div class="img-card">
              <div class="img-card-preview">
                <div
                  class="image-blur-bg"
                  :style="{
                    backgroundImage: `url(${record.origin_cover})`,
                  }"
                ></div>
                <img
                  :src="record.origin_cover"
                  :alt="record.file_name"
                  class="image-cover"
                />
                <div
                  v-if="record.file_name"
                  class="image-name"
                  :title="record.file_name"
                >
                  {{ record.file_name }}
                </div>
                <div
                  class="image-preview-icon"
                  @click.stop="
                    $previewPlayer({
                      videoUrl: record.video_url,
                      title: record.file_name,
                      extra: record,
                    })
                  "
                >
                  <icon-play-circle />
                </div>
                <div class="image-actions">
                  <a-button
                    type="primary"
                    size="mini"
                    @click.stop="
                      curIndex = [rowIndex];
                      videoRef?.show(formModel);
                    "
                  >
                    替换
                  </a-button>
                </div>
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column
          title="封面"
          data-index="cover_url"
          align="center"
          :width="180"
        >
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <div class="img-card">
              <div class="img-card-preview">
                <div
                  class="image-blur-bg"
                  :style="{ backgroundImage: `url(${record.cover_url})` }"
                ></div>
                <img
                  :src="record.cover_url"
                  :alt="record.file_name"
                  class="image-cover"
                />
                <div
                  v-if="record.file_name"
                  class="image-name"
                  :title="record.file_name"
                >
                  {{ record.file_name }}
                </div>
                <div
                  class="image-preview-icon"
                  @click.stop="
                    $previewPlayer({
                      mediaList: [
                        {
                          url: record.cover_url,
                          title: record.file_name,
                          extra: record,
                        },
                      ],
                    })
                  "
                >
                  <icon-search />
                </div>
                <div class="image-actions">
                  <a-button
                    type="primary"
                    size="mini"
                    @click.stop="
                      curIndex = [rowIndex];
                      thumbRef?.show(formModel);
                    "
                  >
                    替换
                  </a-button>
                </div>
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="标题" data-index="title" align="center">
          <template #cell="{ record }: TableColumnSlot">
            <a-textarea
              v-model="record.title"
              placeholder="请输入标题"
              :auto-size="{ minRows: 4, maxRows: 4 }"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="正文"
          data-index="describe"
          :width="340"
          align="center"
        >
          <template #cell="{ record }: TableColumnSlot">
            <a-textarea
              v-model="record.describe"
              placeholder="请输入正文"
              :auto-size="{ minRows: 4, maxRows: 4 }"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="操作"
          data-index="action"
          :width="120"
          align="center"
        >
          <template #cell="{ rowIndex, record }: TableColumnSlot">
            <a-space>
              <a-popconfirm
                :content="`确定删除此项吗？`"
                @ok="handleDelete(rowIndex, record)"
              >
                <a-link status="danger"> 删除 </a-link>
              </a-popconfirm>
              <a-link @click="handleCopy(rowIndex, record)"> 复制 </a-link>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <!--选择视频-->
    <video-select
      ref="videoRef"
      :is-single="Boolean(curIndex.length)"
      @save="addContentItems"
      @close="curIndex = []"
    ></video-select>
    <!--选择封面-->
    <thumb-select
      ref="thumbRef"
      :is-single="Boolean(curIndex.length)"
      @save="setThumb"
      @close="curIndex = []"
    ></thumb-select>
    <!--修改标题-->
    <input-modal
      ref="titleRef"
      @save="setTitle"
      @close="curIndex = []"
    ></input-modal>
    <!--修改正文-->
    <input-modal
      ref="describeRef"
      is-area
      @save="setDescribe"
      @close="curIndex = []"
    ></input-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { cloneDeep } from 'lodash';
  import InputModal from '@/components/input-modal/input-modal.vue';
  import VideoSelect from '@/views/account/content-manage/components/video-select.vue';
  import ThumbSelect from '@/views/account/content-manage/components/thumb-select.vue';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    formModel: {
      type: Object,
      required: true,
    },
    rowSelection: {
      type: Object,
      default: () => ({ type: 'checkbox', showCheckedAll: true }),
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const curIndex = ref<number[]>([]); // 当前正在修改的行
  const selectedKeys = ref<string[]>([]);
  const videoRef = ref();
  const thumbRef = ref();
  const titleRef = ref();
  const describeRef = ref();

  // 根据表格选择的key设置index
  function setCurIndex() {
    curIndex.value = [];
    props.modelValue.forEach((item, index) => {
      if (selectedKeys.value.includes(item.key)) {
        curIndex.value.push(index);
      }
    });
  }

  // 添加/修改视频
  function addContentItems(items: any[]) {
    if (items?.length) {
      const newContentList = [...props.modelValue];
      // 单选就替换
      if (curIndex.value.length) {
        curIndex.value.forEach((index) => {
          newContentList[index].cover_url = items[0].thumb;
          newContentList[index].origin_cover = items[0].thumb;
          newContentList[index].video_url = items[0].url;
          newContentList[index].file_name = items[0].file_name;
        });
      } else {
        // 新增视频内容
        newContentList.push(
          ...items.map((item, index) => ({
            ...props.formModel,
            key: `${Date.now()}_${index}`,
            cover_url: item.thumb,
            origin_cover: item.thumb,
            video_url: item.url,
            file_name: item.file_name,
          }))
        );
      }
      emit('update:modelValue', newContentList);
    }
  }

  // 设置视频封面
  function setThumb(items: any[]) {
    if (items?.length && curIndex.value.length) {
      const newContentList = [...props.modelValue];
      curIndex.value.forEach((index) => {
        newContentList[index].cover_url = items[0].url;
      });
      emit('update:modelValue', newContentList);
    }
  }

  // 设置标题
  function setTitle(title: string) {
    if (curIndex.value.length) {
      const newContentList = [...props.modelValue];
      curIndex.value.forEach((index) => {
        newContentList[index].title = title;
      });
      emit('update:modelValue', newContentList);
    }
  }

  // 设置正文
  function setDescribe(title: string) {
    if (curIndex.value.length) {
      const newContentList = [...props.modelValue];
      curIndex.value.forEach((index) => {
        newContentList[index].describe = title;
      });
      emit('update:modelValue', newContentList);
    }
  }

  // 处理删除
  function handleDelete(rowIndex: number, record: any) {
    const newContentList = [...props.modelValue];
    newContentList.splice(rowIndex, 1);
    selectedKeys.value = selectedKeys.value.filter(
      (item: string) => item !== record.key
    );
    emit('update:modelValue', newContentList);
  }

  // 处理复制
  function handleCopy(rowIndex: number, record: any) {
    const newContentList = [...props.modelValue];
    newContentList.splice(
      rowIndex + 1,
      0,
      cloneDeep({
        ...cloneDeep(record),
        key: `${Date.now()}`,
      })
    );
    emit('update:modelValue', newContentList);
  }

  // 处理批量删除
  function handleBatchDelete() {
    const newContentList = props.modelValue.filter(
      (item: any) => !selectedKeys.value.includes(item.key)
    );
    selectedKeys.value = [];
    emit('update:modelValue', newContentList);
  }
</script>

<style scoped lang="less">
  .img-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 160px;
    margin: 0 auto;
    border: 1px solid var(--color-border-1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
      }

      .image-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .img-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      z-index: 2;
    }

    .image-actions {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 4px 8px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      display: flex;
      gap: 8px;
      opacity: 1;
      transform: translateY(100%);
      transition: all 0.3s ease;
      z-index: 2;

      .arco-btn {
        flex: 1;
        height: 20px;
        font-size: 10px;
        padding: 2px 8px;
      }
    }
  }
</style>

<template>
  <a-card title="内容列表">
    <template #extra>
      <a-space>
        <a-button
          size="small"
          type="primary"
          @click="
            curIndex = [];
            curImgIndex = undefined;
            imageRef?.show(formModel);
          "
        >
          添加图片
        </a-button>
        <a-button
          size="small"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="
            setCurIndex();
            curImgIndex = undefined;
            imageRef?.show(formModel);
          "
        >
          追加图片
        </a-button>
        <a-button
          size="small"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="
            setCurIndex();
            titleRef?.show();
          "
        >
          修改标题
        </a-button>
        <a-button
          size="small"
          type="primary"
          :disabled="!selectedKeys.length"
          @click="
            setCurIndex();
            describeRef?.show();
          "
        >
          修改正文
        </a-button>
        <a-popconfirm
          :content="`确定删除这${selectedKeys.length}项吗？`"
          @ok="handleBatchDelete"
        >
          <a-button
            size="small"
            status="danger"
            :disabled="!selectedKeys.length"
          >
            批量删除
          </a-button>
        </a-popconfirm>
      </a-space>
    </template>
    <a-table
      v-model:selected-keys="selectedKeys"
      :row-selection="rowSelection"
      :data="modelValue"
      :pagination="false"
      :hoverable="false"
      :scroll="{ maxHeight: '60vh' }"
    >
      <template #columns>
        <a-table-column title="内容名称" data-index="name" align="center">
          <template #cell="{ record }: TableColumnSlot">
            <a-input v-model="record.name" placeholder="请输入内容名称" />
          </template>
        </a-table-column>
        <a-table-column
          title="图片"
          data-index="image_list"
          align="center"
          :width="400"
        >
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <div class="scroll-box">
              <vuedraggable
                v-model="record.image_list"
                class="df fw-wrap"
                item-key="key"
                @change="handleImageChange"
              >
                <template #item="{ element, index }">
                  <div class="img-card">
                    <div class="img-card-preview">
                      <div
                        class="image-blur-bg"
                        :style="{ backgroundImage: `url(${element.url})` }"
                      ></div>
                      <img
                        :src="element.url"
                        :alt="element.file_name"
                        class="image-cover"
                      />
                      <div class="image-name" :title="element.file_name">{{
                        element.file_name
                      }}</div>
                      <div
                        class="image-preview-icon"
                        @click.stop="
                          $previewPlayer({
                            mediaList: [
                              {
                                url: element.url,
                                title: element.file_name,
                                extra: record,
                              },
                            ],
                          })
                        "
                      >
                        <icon-search />
                      </div>
                      <div class="image-actions">
                        <a-button
                          type="primary"
                          size="mini"
                          status="danger"
                          @click.stop="record.image_list.splice(index, 1)"
                        >
                          移除
                        </a-button>
                        <a-button
                          type="primary"
                          size="mini"
                          @click.stop="editImage(rowIndex, index)"
                        >
                          替换
                        </a-button>
                      </div>
                    </div>
                  </div>
                </template>
              </vuedraggable>
            </div>
          </template>
        </a-table-column>
        <a-table-column
          title="封面"
          data-index="cover_url"
          align="center"
          :width="200"
        >
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <div v-if="record.image_list.length" class="img-card">
              <div class="img-card-preview">
                <div
                  class="image-blur-bg"
                  :style="{
                    backgroundImage: `url(${record.cover_url})`,
                  }"
                ></div>
                <img
                  :src="record.cover_url"
                  :alt="record.cover_url"
                  class="image-cover"
                />
                <div class="image-name" :title="record.cover_url">{{
                  record.cover_url.split('/').pop()
                }}</div>
                <div
                  class="image-preview-icon"
                  @click.stop="
                    $previewPlayer({
                      mediaList: [
                        {
                          url: record.cover_url,
                          title: record.cover_url.split('/').pop(),
                          extra: record,
                        },
                      ],
                    })
                  "
                >
                  <icon-search />
                </div>
                <div class="image-actions">
                  <a-button
                    type="primary"
                    size="mini"
                    @click.stop="
                      curIndex = [rowIndex];
                      curImgIndex = 0;
                      imageRef?.show(formModel);
                    "
                  >
                    替换
                  </a-button>
                </div>
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="标题" data-index="title" align="center">
          <template #cell="{ record }: TableColumnSlot">
            <a-textarea
              v-model="record.title"
              placeholder="请输入标题"
              :auto-size="{ minRows: 4, maxRows: 4 }"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="正文"
          data-index="describe"
          :width="200"
          align="center"
        >
          <template #cell="{ record }: TableColumnSlot">
            <a-textarea
              v-model="record.describe"
              placeholder="请输入正文"
              :auto-size="{ minRows: 4, maxRows: 4 }"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="操作"
          data-index="action"
          :width="120"
          align="center"
        >
          <template #cell="{ rowIndex, record }: TableColumnSlot">
            <a-space>
              <a-popconfirm
                :content="`确定删除此项吗？`"
                @ok="handleDelete(rowIndex, record)"
              >
                <a-link status="danger"> 删除 </a-link>
              </a-popconfirm>
              <a-link @click="handleCopy(rowIndex, record)"> 复制 </a-link>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <!--选择图片-->
    <image-select
      ref="imageRef"
      :is-single="!isUndefined(curImgIndex)"
      @save="addContentItems"
      @close="
        curIndex = [];
        curImgIndex = undefined;
      "
    ></image-select>
    <!--修改标题-->
    <input-modal
      ref="titleRef"
      @save="setTitle"
      @close="curIndex = []"
    ></input-modal>
    <!--修改正文-->
    <input-modal
      ref="describeRef"
      is-area
      @save="setDescribe"
      @close="curIndex = []"
    ></input-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { cloneDeep, isUndefined } from 'lodash';
  import vuedraggable from 'vuedraggable';
  import InputModal from '@/components/input-modal/input-modal.vue';
  import ImageSelect from '@/views/account/content-manage/components/image-select.vue';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    formModel: {
      type: Object,
      required: true,
    },
    rowSelection: {
      type: Object,
      default: () => ({ type: 'checkbox', showCheckedAll: true }),
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const curIndex = ref<number[]>([]); // 当前正在修改的行
  const curImgIndex = ref<undefined | number>(undefined); // 当前正在修改的图片
  const selectedKeys = ref<string[]>([]);
  const imageRef = ref();
  const titleRef = ref();
  const describeRef = ref();

  // 监听image_list变化，更新cover_url
  // watch(
  //   () => props.modelValue,
  //   (newValue) => {
  //     if (newValue?.length) {
  //       const newContentList = newValue.map((item) => {
  //         if (item.image_list?.length) {
  //           return {
  //             ...item,
  //             cover_url: item.image_list[0].url,
  //           };
  //         }
  //         return item;
  //       });
  //       emit('update:modelValue', newContentList);
  //     }
  //   },
  //   { deep: true }
  // );

  const editImage = (rowIndex: number, index: number) => {
    curIndex.value = [rowIndex];
    curImgIndex.value = index;
    imageRef.value?.show(props.formModel);
  };

  // 根据表格选择的key设置index
  function setCurIndex() {
    curIndex.value = [];
    props.modelValue.forEach((item, index) => {
      if (selectedKeys.value.includes(item.key)) {
        curIndex.value.push(index);
      }
    });
  }

  // 添加/修改图片
  function addContentItems(items: any[]) {
    if (items?.length) {
      try {
        const newContentList = [...props.modelValue];
        // 单选就替换
        if (!isUndefined(curImgIndex.value)) {
          if (newContentList[curIndex.value[0]]) {
            newContentList[curIndex.value[0]].image_list[
              curImgIndex.value
            ].url = items[0].url;
            newContentList[curIndex.value[0]].image_list[
              curImgIndex.value
            ].file_name = items[0].file_name;
            // 如果是第一张图片，更新cover_url
            if (curImgIndex.value === 0) {
              newContentList[curIndex.value[0]].cover_url = items[0].url;
            }
          }
        } else if (curIndex.value.length) {
          // 追加图片
          curIndex.value.forEach((index) => {
            const newImages = items.map((item, cindex) => ({
              key: `${Date.now()}_${cindex}`, // 拖动需要的key
              url: item.url,
              file_name: item.file_name,
            }));
            newContentList[index].image_list.push(...newImages);
            // 如果没有图片，设置第一张为封面
            if (!newContentList[index].image_list.length) {
              newContentList[index].cover_url = newImages[0].url;
            }
          });
        } else {
          // 新增图片内容
          const newImages = items.map((item, index) => ({
            key: `${Date.now()}_${index}`, // 拖动需要的key
            url: item.url,
            file_name: item.file_name,
          }));
          newContentList.push({
            key: `${Date.now()}_0`, // 表格行选择需要的key
            ...props.formModel,
            image_list: newImages,
            cover_url: newImages[0].url, // 设置第一张图片为封面
          });
        }
        emit('update:modelValue', newContentList);
      } catch (e) {
        console.log(
          'addContentItems error',
          e,
          curIndex.value,
          curImgIndex.value,
          items,
          props.modelValue
        );
      }
    }
  }

  // 设置标题
  function setTitle(title: string) {
    if (curIndex.value.length) {
      const newContentList = [...props.modelValue];
      curIndex.value.forEach((index) => {
        newContentList[index].title = title;
      });
      emit('update:modelValue', newContentList);
    }
  }

  // 设置正文
  function setDescribe(title: string) {
    if (curIndex.value.length) {
      const newContentList = [...props.modelValue];
      curIndex.value.forEach((index) => {
        newContentList[index].describe = title;
      });
      emit('update:modelValue', newContentList);
    }
  }

  // 处理删除
  function handleDelete(rowIndex: number, record: any) {
    const newContentList = [...props.modelValue];
    newContentList.splice(rowIndex, 1);
    selectedKeys.value = selectedKeys.value.filter(
      (item: string) => item !== record.key
    );
    emit('update:modelValue', newContentList);
  }

  // 处理复制
  function handleCopy(rowIndex: number, record: any) {
    const newContentList = [...props.modelValue];
    newContentList.splice(
      rowIndex + 1,
      0,
      cloneDeep({
        ...cloneDeep(record),
        key: `${Date.now()}`,
      })
    );
    emit('update:modelValue', newContentList);
  }

  // 处理批量删除
  function handleBatchDelete() {
    const newContentList = props.modelValue.filter(
      (item: any) => !selectedKeys.value.includes(item.key)
    );
    selectedKeys.value = [];
    emit('update:modelValue', newContentList);
  }

  // 处理图片变化
  function handleImageChange(data: any) {
    console.log('handleImageChange', data);
    if (data?.moved?.newIndex === 0) {
      //  更新封面
      const newContentList = [...props.modelValue];
      newContentList[data.moved.newIndex].cover_url = data.moved.element.url;
      emit('update:modelValue', newContentList);
    }
  }
</script>

<style scoped lang="less">
  .img-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 160px;
    margin: 0 8px 8px 0;
    border: 1px solid var(--color-border-1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
      }

      .image-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .img-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      z-index: 2;
    }

    .image-actions {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 4px 8px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      display: flex;
      gap: 8px;
      opacity: 1;
      transform: translateY(100%);
      transition: all 0.3s ease;
      z-index: 2;

      .arco-btn {
        flex: 1;
        height: 20px;
        font-size: 10px;
        padding: 2px 8px;
      }
    }
  }

  .scroll-box {
    overflow-y: auto;
    max-height: 300px;
    padding-right: 10px;
    background: var(--color-fill-1);
    padding: 10px;
    border-radius: var(--border-radius-large);
  }
</style>

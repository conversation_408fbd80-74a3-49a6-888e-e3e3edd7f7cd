<template>
  <div class="content-box">
    <a-card size="small" class="no-bottom-padding-card">
      <div class="df">
        <folder-tree-list
          ref="foldTreeRef"
          v-model:dir-id="formModel.dir_id"
          class="w-200"
          :apis="dirApis"
          :send-params="dirParams"
          :show-pop-menu="(nodeData:any) => nodeData.parent_id"
          :show-resource-count="true"
          @change="dirChange"
        ></folder-tree-list>
        <a-divider :margin="10" direction="vertical" class="divider-line" />
        <div style="flex: 1">
          <search-form-bar
            v-model="formModel"
            :config="searchConfig"
            :no-bottom-margin="!multipleAction && !isSelect"
            :show-content-type="true"
            @search="queryAction()"
            @reset="resetSearch"
            @sort="sortAction"
          >
            <template #right>
              <a-space>
                <a-button
                  v-if="!multipleAction && !isSelect"
                  type="outline"
                  @click="
                    selectKeys = [];
                    multipleAction = true;
                  "
                >
                  <template #icon>
                    <icon-unordered-list />
                  </template>
                  批量操作
                </a-button>
                <a-button type="primary" @click="addRef?.show(formModel)">
                  <template #icon><icon-upload /></template>
                  批量添加
                </a-button>
              </a-space>
            </template>
          </search-form-bar>
          <div class="jc-sb mb-10">
            <a-space v-if="multipleAction || isSelect">
              <a-checkbox
                :model-value="
                  selectKeys.length === list.length && selectKeys.length !== 0
                "
                :indeterminate="
                  selectKeys.length !== 0 && selectKeys.length !== list.length
                "
                @change="
                  (val:boolean) => (selectKeys = val ? list.map((item:any) => item.id) : [])
                "
              >
                全选
              </a-checkbox>
              <span>已选择{{ selectKeys.length }}个内容</span>
              <a-button
                v-if="!isSelect"
                type="text"
                @click="multipleAction = false"
              >
                <template #icon>
                  <icon-close-circle />
                </template>
                取消
              </a-button>
            </a-space>
            <a-space>
              <template v-if="multipleAction">
                <a-button
                  type="primary"
                  :disabled="!selectKeys.length"
                  @click="handlePublish()"
                >
                  <template #icon><icon-send /></template>
                  去批量发布
                </a-button>
                <a-button
                  type="primary"
                  :disabled="!selectKeys.length"
                  @click="handleDownload()"
                >
                  <template #icon><icon-download /></template>
                  批量下载
                </a-button>
                <a-button
                  type="primary"
                  :disabled="!selectKeys.length"
                  @click="moveRef?.show({ ...formModel, file_ids: selectKeys })"
                >
                  <template #icon><icon-undo /></template>
                  批量转移
                </a-button>
                <a-popconfirm
                  :content="`确认删除${selectKeys.length}个内容吗？`"
                  @ok="delAction(selectKeys)"
                >
                  <a-button
                    :disabled="!selectKeys.length"
                    type="primary"
                    status="danger"
                  >
                    <template #icon><icon-delete /></template>
                    批量删除
                  </a-button>
                </a-popconfirm>
              </template>
            </a-space>
          </div>
          <!-- <a-divider :margin="10" /> -->
          <a-spin
            :loading="loading"
            class="image-list-container"
            :class="{ 'scroll-box': isScroll }"
          >
            <template v-if="list.length">
              <div class="image-grid">
                <div
                  v-for="item in list"
                  :key="item.id"
                  @click="selectChange(item)"
                >
                  <image-content-item
                    v-if="item.type === 'image_text'"
                    :item="item"
                    :select-keys="selectKeys"
                    :multiple-action="multipleAction"
                    :can-select="multipleAction || canSelect"
                    :is-remove="isRemove"
                    :open-edit="openEdit"
                    @edit="imageEditRef?.show(item)"
                    @del="delAction([item.id])"
                    @download="handleDownload([item.id])"
                  />
                  <video-content-item
                    v-else-if="item.type === 'video'"
                    :item="item"
                    :select-keys="selectKeys"
                    :multiple-action="multipleAction"
                    :can-select="multipleAction || canSelect"
                    :is-remove="isRemove"
                    :open-edit="openEdit"
                    @edit="videoEditRef?.show(item)"
                    @del="delAction([item.id])"
                    @download="handleDownload([item.id])"
                  />
                  <image-content-item
                    v-else-if="item.type === 'video_image'"
                    :item="item"
                    :select-keys="selectKeys"
                    :multiple-action="multipleAction"
                    :can-select="multipleAction || canSelect"
                    :is-remove="isRemove"
                    :open-edit="openEdit"
                    type="mix"
                    @edit="editRef?.show(item)"
                    @del="delAction([item.id])"
                    @download="handleDownload([item.id])"
                  />
                </div>
              </div>
              <div class="mt-10 jc-sb">
                <div></div>
                <a-pagination
                  :current="formModel.page"
                  :page-size="formModel.pageSize"
                  :total="formModel.total"
                  show-page-size
                  show-total
                  :page-size-options="[12, 24, 48, 96]"
                  @change="(page:number) => queryAction(page)"
                  @page-size-change="(pageSize:number) => queryAction(1, pageSize)"
                />
              </div>
            </template>
            <div v-else class="df ai-cen" style="min-height: 600px">
              <a-empty />
            </div>
          </a-spin>
        </div>
      </div>
    </a-card>
    <content-move
      ref="moveRef"
      :send-params="dirParams"
      @refresh="
        queryAction();
        getTreeData();
      "
    />
    <content-mix-add
      ref="addRef"
      :send-params="dirParams"
      @refresh="queryAction()"
    />
    <content-mix-edit ref="editRef" @save="queryAction()" />
    <content-image-edit ref="imageEditRef" @save="queryAction()" />
    <content-video-edit ref="videoEditRef" @save="queryAction()" />
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
  import FolderTreeList from '@/components/fold-tree/folder-tree-list.vue';
  import request from '@/api/request';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import SearchFormBar from '@/components/search-form-bar/index.vue';
  import { sortFieldM } from '@/components/dict-select/dict-account';
  import ContentImageEdit from '@/views/account/content-manage/image-content/content-image-edit.vue';
  import ContentVideoEdit from '@/views/account/content-manage/video-content/content-video-edit.vue';
  import { Message, Modal, Notification } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import { cloneDeep } from 'lodash';
  import { createAndPollDownload } from '@/utils/download-utils';
  // import { downloadProgressFile } from '@/utils/table-utils/table-util';
  import ContentMove from '../components/content-move.vue';
  import ContentMixAdd from './content-mix-add.vue';
  import ContentMixEdit from './content-mix-edit.vue';

  const router = useRouter();

  // 响应式屏幕尺寸检测
  const isMobile = ref(false);

  const checkScreenSize = () => {
    isMobile.value = window.innerWidth <= 1440;
  };

  const props = defineProps({
    isSelect: {
      type: Boolean,
      default: false,
    },
    canSelect: {
      type: Boolean,
      default: false,
    },
    isRemove: {
      type: Boolean,
      default: false,
    },
    openEdit: {
      type: Boolean,
      default: true,
    },
    isScroll: {
      type: Boolean,
      default: false,
    },
  });

  // 计算默认页面大小
  const getDefaultPageSize = () => {
    if (props.isScroll) {
      return 12; // 滚动模式保持原有逻辑
    }
    return isMobile.value ? 12 : 24; // 非滚动模式：移动端12条，桌面端24条
  };

  function defaultForm() {
    return {
      dir_id: 0,
      product_id: null,
      page: 1,
      pageSize: getDefaultPageSize(),
      total: 0,
      type: '',
      publish_count_min: null,
      publish_count_max: null,
      order_field: 'add_time',
      order_type: 'desc',
      create_user_ids: [],
      add_time: [],
      keyword: '',
      publish_num_min: null,
      publish_num_max: null,
    };
  }
  const formModel = ref(defaultForm());

  const dirApis = {
    list: '/api/material/dirList',
    save: '/api/material/dirSave',
  };
  const dirParams = computed(() => ({
    type: 'content',
  }));

  const loading = ref(false);
  const multipleAction = ref(false);
  const addRef = ref();
  const editRef = ref();
  const imageEditRef = ref();
  const videoEditRef = ref();

  const moveRef = ref();
  const list = ref<any[]>([]);
  const selectKeys = ref<number[]>([]);
  let cancelToken: AbortController;

  const foldTreeRef = ref();
  const getTreeData = () => {
    foldTreeRef.value?.getDept();
  };

  function getList() {
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    selectKeys.value = [];
    loading.value = true;
    request(
      '/api/content/list',
      {
        ...formModel.value,
      },
      cancelToken.signal
    )
      .then((res) => {
        list.value = res.data.data;
        formModel.value.page = res.data.current_page;
        formModel.value.pageSize = res.data.per_page;
        formModel.value.total = res.data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  getList();

  function selectChange(item: any) {
    if (selectKeys.value.includes(item.id)) {
      selectKeys.value = selectKeys.value.filter((val) => val !== item.id);
    } else {
      selectKeys.value.push(item.id);
    }
  }

  function queryAction(page?: number, pageSize?: number) {
    formModel.value.page = page || 1;
    formModel.value.pageSize = pageSize || formModel.value.pageSize;
    getList();
  }

  // 监听屏幕尺寸变化
  onMounted(() => {
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', checkScreenSize);
  });

  // 监听屏幕尺寸变化，更新页面大小
  watch([isMobile, () => props.isScroll], () => {
    if (!props.isScroll) {
      const newPageSize = getDefaultPageSize();
      if (formModel.value.pageSize !== newPageSize) {
        formModel.value.pageSize = newPageSize;
        queryAction(1); // 重新查询第一页
      }
    }
  });

  const sortAction = (type: string, field: string) => {
    formModel.value.order_type = type;
    formModel.value.order_field = field;
    queryAction();
  };

  const searchConfig = {
    basicSearch: {
      field: 'keyword',
      placeholder: '搜索文件名称、标题和正文',
    },
    advancedSearch: {
      items: [
        {
          type: 'rangePicker',
          label: '上传时间',
          field: 'add_time',
          props: {
            format: 'YYYY-MM-DD',
          },
        },
        {
          type: 'rangePopoverPair',
          label: '发布次数',
          props: {
            minField: 'publish_num_min',
            maxField: 'publish_num_max',
            ranges: [
              { label: '不限', min: undefined, max: undefined },
              { label: '10以下', min: undefined, max: 10 },
              { label: '10-50', min: 10, max: 50 },
              { label: '50-100', min: 50, max: 100 },
              { label: '100以上', min: 100, max: undefined },
            ],
          },
        },
        {
          type: 'UserSelectPopover',
          label: '上传人',
          field: 'create_user_ids',
          props: {
            placeholder: '请选择',
          },
        },
        {
          type: 'sort',
          label: '排序字段',
          componentType: 'select',
          props: {
            fields: sortFieldM,
          },
        },
      ],
    },
    defaultValues: {
      keyword: '',
      add_time: [],
      publish_num_min: null,
      publish_num_max: null,
      order_field: 'add_time',
      order_type: 'desc',
      create_user_ids: [],
    },
  };

  function resetSearch() {
    formModel.value = defaultForm();
    queryAction();
  }

  function dirChange(val: any, { node }: any) {
    formModel.value.product_id = node?.product_id;
    queryAction();
  }

  function delAction(file_ids: any) {
    loading.value = true;
    request('/api/content/del', {
      file_ids,
    }).then(() => {
      queryAction();
    });
  }

  function getSelectItems() {
    return list.value.filter((item) => selectKeys.value.includes(item.id));
  }

  function clearSelect() {
    selectKeys.value = [];
  }

  const handleDownload = async (transIds: any) => {
    let ids = transIds || selectKeys.value;
    if (!ids.length) return;

    try {
      let sendData = {
        ids,
        // 暂不支持跨页全选
        is_across_select: false,
      };
      if (formModel.value.is_across_select) {
        delete sendData.content_ids;
      }

      await createAndPollDownload('/api/content/download', sendData, {
        pollingInterval: 1000,
        onError: (error) => {
          console.error('Failed to download content:', error);
        },
      });
    } catch (error) {
      console.error('Failed to download content:', error);
    }
  };

  // 去批量发布
  function handlePublish() {
    let ids = selectKeys.value;
    if (!ids.length) return;
    router.push({
      name: 'content-publish',
      query: {
        ids: ids.join(','),
      },
    });
  }

  defineExpose({
    getSelectItems,
    clearSelect,
  });
</script>

<style scoped lang="less">
  .image-list-container {
    // padding: 12px;
    border-radius: 8px;
    width: 100%;
    min-height: calc(100vh - 172px);
    &.scroll-box {
      min-height: 65vh;
      max-height: calc(100vh - 200px);
      overflow-y: auto;
    }
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  @media (max-width: 768px) {
    .image-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 12px;
    }
  }
</style>

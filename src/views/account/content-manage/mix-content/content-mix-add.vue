<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="添加内容"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    :width="formModel.step === 1 ? '800px' : '1400px'"
    @cancel="handleCancel"
  >
    <a-form
      v-if="formModel.step === 1"
      ref="formRef"
      :model="formModel"
      auto-label-width
    >
      <!-- 内容类型 -->
      <a-form-item label="内容类型" field="type" :rules="requiredRule">
        <a-radio-group v-model="formModel.type" type="button">
          <a-radio value="video">视频内容</a-radio>
          <a-radio value="image_text">图文内容</a-radio>
          <a-radio value="video_image">混合内容</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="文件夹" field="dir_id" :rules="requiredRule">
        <folder-tree-select
          v-model="formModel.dir_id"
          :send-params="dirParams"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/dirList"
          :format-data="(arr:any) => arr?.children || []"
          :allow-clear="false"
        ></folder-tree-select>
      </a-form-item>
      <a-form-item label="内容名称" field="name" :rules="requiredRule">
        <a-input ref="iptRef" v-model="formModel.name" allow-clear />
        <template #extra>
          <div>
            <template v-for="item in nameMap" :key="item.value">
              <a-button
                type="text"
                class="mr-10 p-0"
                size="mini"
                @click="insertName(item.value)"
              >
                +{{ item.label }}
              </a-button>
            </template>
          </div>
        </template>
      </a-form-item>
      <a-form-item
        label="默认标题"
        field="title"
        :rules="[
          { required: true, message: '请填写标题' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 20) {
                callback('标题不能超过20个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-input
          v-model="formModel.title"
          allow-clear
          placeholder="请输入"
          :max-length="20"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        />
      </a-form-item>
      <a-form-item
        label="默认正文"
        field="describe"
        :rules="[
          { required: true, message: '请填写正文' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 900) {
                callback('标题不能超过900个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-textarea
          v-model="formModel.describe"
          :auto-size="{ maxRows: 8, minRows: 12 }"
          allow-clear
          placeholder="请输入"
          :max-length="900"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        />
      </a-form-item>
    </a-form>
    <template v-else>
      <image-content-list
        v-if="formModel.type === 'image_text'"
        v-model="contentList"
        :form-model="formModel"
      />
      <video-content-list
        v-if="formModel.type === 'video'"
        v-model="contentList"
        :form-model="formModel"
      />
      <mix-content-list
        v-if="formModel.type === 'video_image'"
        v-model="contentList"
        :form-model="formModel"
      />
    </template>
    <template #footer>
      <a-spin :loading="okLoading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button
            v-if="formModel.step === 1"
            type="primary"
            @click="nextStep(2)"
          >
            下一步
          </a-button>
          <template v-else>
            <a-button @click="formModel.step = 1"> 上一步 </a-button>
            <a-button
              v-if="formModel.type === 'image_text'"
              type="primary"
              @click="handleBeforeOkImage()"
            >
              确定
            </a-button>
            <a-button
              v-else-if="formModel.type === 'video'"
              type="primary"
              @click="handleBeforeOkVideo()"
            >
              确定
            </a-button>
            <a-button
              v-else-if="formModel.type === 'video_image'"
              type="primary"
              @click="handleBeforeOkImage()"
            >
              确定
            </a-button>
          </template>
        </a-space>
      </a-spin>
    </template>
    <!--选择图片-->
    <image-select
      ref="imageRef"
      :is-single="!isUndefined(curImgIndex)"
      @save="addContentItems"
      @close="
        curIndex = [];
        curImgIndex = undefined;
      "
    ></image-select>
    <!--修改标题-->
    <input-modal
      ref="titleRef"
      @save="setTitle"
      @close="curIndex = []"
    ></input-modal>
    <!--修改正文-->
    <input-modal
      ref="describeRef"
      is-area
      @save="setDescribe"
      @close="curIndex = []"
    ></input-modal>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, ref } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    requiredRule,
    setCaretPosition,
    getCharacterLength,
    sliceString,
  } from '@/utils/util';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { cloneDeep, isUndefined } from 'lodash';
  import vuedraggable from 'vuedraggable';
  import InputModal from '@/components/input-modal/input-modal.vue';
  import dayjs from 'dayjs';
  import ImageSelect from '@/views/account/content-manage/components/image-select.vue';
  import ImageContentList from './components/image-content-list.vue';
  import VideoContentList from './components/video-content-list.vue';
  import MixContentList from './components/mix-content-list.vue';

  const visible = ref(false);
  const okLoading = ref(false);
  const curIndex = ref<number[]>([]); // 当前正在修改的行
  const curImgIndex = ref<undefined | number>(undefined); // 当前正在修改的图片
  const emits = defineEmits(['refresh']);
  const nameMap = [
    { label: '首图名称', value: '首图名称' },
    { label: '日期', value: '日期' },
    { label: '序号', value: '序号' },
  ];
  function defaultForm() {
    return {
      step: 1,
      dir_id: '',
      product_id: '',
      name: '{首图名称}_{日期}_{序号}',
      title: '',
      describe: '',
      cover_url: '',
      video_url: '',
      origin_cover: '',
      type: 'image_text',
    };
  }
  const props = defineProps({
    sendParams: {
      type: [Object],
      default: null,
    },
  });
  const formModel = ref(defaultForm());
  const formRef = ref();
  const iptRef = ref();
  const imageRef = ref();
  const titleRef = ref();
  const describeRef = ref();
  const contentList = ref<any[]>([]);
  const selectedKeys = ref<string[]>([]);
  const dirParams = computed(() => ({
    ...props.sendParams,
    product_id: formModel.value.product_id,
  }));

  const insertName = (name: string) => {
    // if (name === '序号') {
    //  name = '起始序号-01';
    // }
    // @ts-ignore
    let iptDom = iptRef.value?.inputRef;
    let newName: string = iptDom.value;
    let index = iptDom.selectionStart || newName?.length || 0;
    if (!newName) {
      newName = `{${name}}`;
    } else {
      newName = `${newName.slice(0, index)}_{${name}}${newName.slice(index)}`;
    }
    formModel.value.name = newName;
    setCaretPosition(iptDom, index + name.length + 3);
  };

  function nextStep(step: number) {
    formRef.value?.validate((err: any) => {
      if (!err) {
        formModel.value.step = step;
      }
    });
  }

  // 根据表格选择的key设置index
  function setCurIndex() {
    curIndex.value = [];
    contentList.value.forEach((item, index) => {
      if (selectedKeys.value.includes(item.key)) {
        curIndex.value.push(index);
      }
    });
  }

  // 添加/修改图片
  function addContentItems(items: any[]) {
    if (items?.length) {
      try {
        // 单选就替换
        if (!isUndefined(curImgIndex.value)) {
          if (contentList.value[curIndex.value[0]]) {
            contentList.value[curIndex.value[0]].image_list[
              curImgIndex.value
            ].url = items[0].url;
            contentList.value[curIndex.value[0]].image_list[
              curImgIndex.value
            ].file_name = items[0].file_name;
          }
        } else if (curIndex.value.length) {
          // 追加图片
          curIndex.value.forEach((index) => {
            contentList.value[index].image_list.push(
              ...items.map((item, cindex) => ({
                key: `${Date.now()}_${cindex}`, // 拖动需要的key
                url: item.url,
                file_name: item.file_name,
              }))
            );
          });
        } else {
          // 新增图片内容
          contentList.value.push({
            key: `${Date.now()}_0`, // 表格行选择需要的key
            ...formModel.value,
            image_list: items.map((item, index) => ({
              key: `${Date.now()}_${index}`, // 拖动需要的key
              url: item.url,
              file_name: item.file_name,
            })),
          });
        }
      } catch (e) {
        console.log(
          'addContentItems error',
          e,
          curIndex.value,
          curImgIndex.value,
          items,
          contentList.value
        );
      }
    }
  }
  // 设置视频封面
  function setThumb(items: any[]) {
    if (items?.length && curIndex.value.length) {
      curIndex.value.forEach((index) => {
        contentList.value[index].url = items[0].url;
      });
    }
  }
  // 设置标题
  function setTitle(title: string) {
    if (curIndex.value.length) {
      curIndex.value.forEach((index) => {
        contentList.value[index].title = title;
      });
    }
  }
  // 设置正文
  function setDescribe(title: string) {
    if (curIndex.value.length) {
      curIndex.value.forEach((index) => {
        contentList.value[index].describe = title;
      });
    }
  }

  function handleCancel() {
    visible.value = false;
    emits('refresh');
    formRef.value?.clearValidate();
  }

  function handleBeforeOkImage() {
    contentList.value = contentList.value.filter(
      (item) => item.image_list?.length
    );
    contentList.value.forEach((item, index) => {
      if (item.name.includes('{首图名称}')) {
        item.name = item.name.replaceAll(
          '{首图名称}',
          item.image_list[0].file_name.split('.').slice(0, -1).join('.')
        );
      }
      if (item.name.includes('{日期}')) {
        item.name = item.name.replaceAll('{日期}', dayjs().format('YYYYMMDD'));
      }
      if (item.name.includes('{序号}')) {
        item.name = item.name.replaceAll('{序号}', index + 1);
      }
    });
    okLoading.value = true;
    request('/api/content/save', {
      content_list: contentList.value.map((item) => ({
        ...item,
        image_list: item.image_list.map((img: any) => img.url),
      })),
    })
      .then(() => {
        Message.success('操作成功');
        handleCancel();
      })
      .finally(() => {
        okLoading.value = false;
      });
  }

  function handleBeforeOkVideo() {
    if (!contentList.value.length) {
      return Message.error('请添加内容');
    }
    contentList.value.forEach((item, index) => {
      if (item.name.includes('{视频名称}')) {
        item.name = item.name.replaceAll(
          '{视频名称}',
          item.file_name.split('.').slice(0, -1).join('.')
        );
      }
      if (item.name.includes('{日期}')) {
        item.name = item.name.replaceAll('{日期}', dayjs().format('YYYYMMDD'));
      }
      if (item.name.includes('{序号}')) {
        item.name = item.name.replaceAll('{序号}', index + 1);
      }
    });
    okLoading.value = true;
    request('/api/content/save', {
      content_list: contentList.value,
    })
      .then(() => {
        Message.success('操作成功');
        handleCancel();
      })
      .finally(() => {
        okLoading.value = false;
      });
  }

  function show(ddata: any) {
    formModel.value = defaultForm();
    formModel.value.product_id = ddata?.product_id;
    formModel.value.dir_id = ddata?.dir_id || null;
    formModel.value.type = ddata?.type || 'image_text';
    contentList.value = [];
    visible.value = true;
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 160px;
    margin: 0 8px 8px 0;
    border: 1px solid var(--color-border-1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
      }

      .image-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .img-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      z-index: 2;
    }

    .image-actions {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 8px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      display: flex;
      gap: 8px;
      opacity: 1;
      transform: translateY(100%);
      transition: all 0.3s ease;
      z-index: 2;

      .arco-btn {
        flex: 1;
        height: 16px;
        font-size: 10px;
        padding: 2px 8px;
      }
    }
  }

  .scroll-box {
    overflow-y: auto;
    max-height: 300px;
    padding-right: 10px;
    background: var(--color-fill-1);
    padding: 10px;
    border-radius: var(--border-radius-large);
  }
</style>

<template>
  <d-modal
    :visible="visible"
    width="800px"
    title="编辑内容"
    :ok-loading="loading"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item label="图片" field="image_list" :rules="requiredRuleArr">
        <div>
          <a-space class="mb-10">
            <a-button
              class="change-btn"
              type="primary"
              @click="
                curIndex = undefined;
                imageRef?.show();
              "
            >
              <template #icon>
                <icon-plus />
              </template>
              添加
            </a-button>
            <a-button
              class="change-btn"
              type="text"
              status="danger"
              @click="formModel.image_list = []"
            >
              <template #icon>
                <icon-delete />
              </template>
              清空
            </a-button>
            <a-typography-text type="secondary" :underline="true">
              <icon-info-circle />
              拖动图片可以调整顺序
            </a-typography-text>
          </a-space>
          <vuedraggable
            v-model="formModel.image_list"
            class="df fw-wrap"
            item-key="key"
          >
            <template #item="{ element, index }">
              <div class="img-card">
                <div class="img-card-preview">
                  <div
                    class="image-blur-bg"
                    :style="{ backgroundImage: `url(${element.url})` }"
                  ></div>
                  <img
                    :src="element.url"
                    :alt="element.file_name"
                    class="image-cover"
                  />
                  <div
                    v-if="element.file_name"
                    class="image-name"
                    :title="element.file_name"
                    >{{ element.file_name }}</div
                  >
                  <div
                    class="image-preview-icon"
                    @click.stop="
                      $previewPlayer({
                        mediaList: [
                          {
                            url: element.url,
                            title:
                              element.file_name || element.url.split('/').pop(),
                            extra: formModel,
                          },
                        ],
                      })
                    "
                  >
                    <icon-search />
                  </div>
                  <div class="image-actions">
                    <a-button
                      type="primary"
                      size="mini"
                      status="danger"
                      @click.stop="formModel.image_list.splice(index, 1)"
                    >
                      移除
                    </a-button>
                    <a-button
                      type="primary"
                      size="mini"
                      @click.stop="
                        curIndex = index;
                        imageRef?.show();
                      "
                    >
                      替换
                    </a-button>
                  </div>
                </div>
              </div>
            </template>
          </vuedraggable>
        </div>
      </a-form-item>
      <a-form-item label="封面">
        <div v-if="formModel.image_list[0]" class="img-card">
          <div class="img-card-preview">
            <div
              class="image-blur-bg"
              :style="{
                backgroundImage: `url(${formModel.image_list[0]?.url})`,
              }"
            ></div>
            <img
              :src="formModel.image_list[0]?.url"
              :alt="formModel.image_list[0]?.file_name"
              class="image-cover"
            />
            <div
              v-if="formModel.image_list[0]?.file_name"
              class="image-name"
              :title="formModel.image_list[0]?.file_name"
            >
              {{ formModel.image_list[0]?.file_name }}
            </div>
            <div
              class="image-preview-icon"
              @click.stop="
                $previewPlayer({
                  mediaList: [
                    {
                      url: formModel.image_list[0]?.url,
                      title:
                        formModel.image_list[0]?.file_name ||
                        formModel.image_list[0]?.url.split('/').pop(),
                      extra: formModel,
                    },
                  ],
                })
              "
            >
              <icon-search />
            </div>
            <div class="image-actions">
              <a-button
                type="primary"
                size="mini"
                @click.stop="thumbRef?.show()"
              >
                替换
              </a-button>
            </div>
          </div>
        </div>
      </a-form-item>
      <a-form-item
        label="标题"
        field="title"
        :rules="[
          { required: true, message: '请填写标题' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 20) {
                callback('标题不能超过20个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-input
          v-model="formModel.title"
          placeholder="请输入"
          :max-length="20"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        >
        </a-input>
      </a-form-item>
      <a-form-item
        label="正文"
        field="describe"
        :rules="[
          { required: true, message: '请填写正文' },
          {
            validator: (value, callback) => {
              const trimmedLength = getCharacterLength(value);
              if (trimmedLength > 900) {
                callback('标题不能超过900个字符');
              } else {
                callback();
              }
            },
          },
        ]"
      >
        <a-textarea
          v-model="formModel.describe"
          :auto-size="{ minRows: 8, maxRows: 12 }"
          placeholder="请输入"
          :max-length="900"
          :word-length="getCharacterLength"
          :word-slice="sliceString"
          show-word-limit
        >
        </a-textarea>
      </a-form-item>
    </a-form>
    <!--选择图片-->
    <image-select
      ref="imageRef"
      :is-single="!isUndefined(curIndex)"
      @save="setImage"
      @close="curIndex = undefined"
    ></image-select>
    <!--选择封面-->
    <image-select
      ref="thumbRef"
      :is-single="true"
      @save="setThumb"
    ></image-select>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { ref } from 'vue';
  import {
    requiredRule,
    requiredRuleArr,
    getCharacterLength,
    sliceString,
  } from '@/utils/util';
  import RequestSelect from '@/components/select/request-select.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import ImageSelect from '@/views/account/content-manage/components/image-select.vue';
  import { cloneDeep, isUndefined } from 'lodash';
  import vuedraggable from 'vuedraggable';

  function defaultForm() {
    return {
      id: '',
      product_id: '',
      dir_id: '',
      type: 'image_text',
      name: '',
      title: '',
      describe: '',
      image_list: [] as any[],
    };
  }

  const formModel = ref(defaultForm());
  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const imageRef = ref();
  const thumbRef = ref();
  const curIndex = ref<undefined | number>(undefined);
  const emits = defineEmits(['save', 'close']);

  function setImage(items: any[]) {
    if (items?.length) {
      if (isUndefined(curIndex.value)) {
        formModel.value.image_list.push(
          ...items.map((item: any, index: number) => ({
            key: `${Date.now()}_${index}`,
            url: item.url,
          }))
        );
      } else {
        formModel.value.image_list[curIndex.value].url = items[0].url;
      }
    }
  }
  function setThumb(items: any[]) {
    if (items?.length) {
      formModel.value.image_list[0].url = items[0].url;
    }
  }

  function handleCancel() {
    visible.value = false;
    loading.value = false;
    emits('close');
    formRef.value?.clearValidate();
  }
  function handleBeforeOk() {
    formRef.value?.validate((err: boolean) => {
      if (!err) {
        loading.value = true;
        request('/api/content/update', {
          ...formModel.value,
          image_list: formModel.value.image_list.map((item: any) => item.url),
          cover_url: formModel.value.image_list[0]?.url,
        })
          .then(() => {
            Message.success('保存成功');
            emits('save');
            handleCancel();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  function show(dinfo: any) {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
      formModel.value.image_list = dinfo.image_list.map(
        (item: any, index: number) => ({
          url: item,
          key: `${Date.now()}_${index}`,
        })
      );
    }
    visible.value = true;
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    width: 160px;
    margin: 0 8px 8px 0;
    border: 1px solid var(--color-border-1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
      }

      .image-actions {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .img-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      z-index: 2;
    }

    .image-actions {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      padding: 4px 8px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      display: flex;
      gap: 8px;
      opacity: 1;
      transform: translateY(100%);
      transition: all 0.3s ease;
      z-index: 2;

      .arco-btn {
        flex: 1;
        height: 20px;
        font-size: 10px;
        padding: 2px 8px;
      }
    }
  }
</style>

<template>
  <div
    class="image-card"
    :class="{
      'is-selected': selectKeys.includes(item.id) && canSelect,
      'is-mini': size === 'mini',
      'can-select': canSelect,
    }"
    @click="$emit('select', item)"
  >
    <div class="image-card-preview">
      <div
        class="image-blur-bg"
        :style="{
          backgroundImage: `url(${
            type === 'image' ? item.image_list?.[0] : item.cover_url
          })`,
        }"
      ></div>
      <img v-if="type === 'image'" :src="item.cover_url" class="image-cover" />
      <img v-if="type === 'mix'" :src="item.cover_url" class="image-cover" />
      <div class="image-name"> {{ item.name || item.content_name }}</div>
      <div
        class="ai-icon"
        :class="{
          mini: size === 'mini',
        }"
      >
        <icon-code-sandbox
          v-if="item.media_content_id"
          size="18"
          style="color: #fff; margin-right: 5px"
        />
        <img
          v-else-if="item.ai_flag == 1"
          src="@/assets/images/ai-icon-3.png"
          class="mr-5"
          alt=""
        />
        <mix-flag-logo
          :type="type"
          :size="size === 'mini' ? 'mini' : 'default'"
        />
      </div>
      <div v-if="item.publish_num > 0" class="publish-num-tag">
        <span class="publish-num-text">已发布{{ item.publish_num }}次</span>
      </div>
      <div v-if="isToday(item.add_time) && size !== 'mini'" class="new-tag">
        <img src="@/assets/images/new-tag.jpeg" alt="" />
      </div>
      <div
        class="image-preview-icon"
        @click.stop="
          $previewPlayer({
            mediaList: item.image_list.map((current) => ({
              url: current,
              type: type,
              title:
                current.file_name || current.name || current.split('/').pop(),
              extra: item,
            })),
            activeUrl: item.image_list?.[0],
          })
        "
      >
        <icon-search />
      </div>
      <div v-if="canSelect" class="image-select-indicator">
        <icon-check-circle-fill size="18" />
      </div>
      <div v-if="openEdit" class="image-actions">
        <a-button size="mini" type="primary" @click.stop="$emit('edit')">
          <template #icon>
            <icon-edit size="14" />
          </template>
        </a-button>
        <!-- 下载按钮 -->
        <a-button size="mini" @click.stop="$emit('download')">
          <template #icon>
            <icon-download class="primary_color" size="14" />
          </template>
        </a-button>
        <a-popconfirm
          :content="`确定删除此项吗？`"
          @click.stop
          @ok="$emit('del')"
        >
          <a-button size="mini">
            <template #icon>
              <icon-delete size="14" class="del-red-color" />
            </template>
          </a-button>
        </a-popconfirm>
      </div>
    </div>
    <div class="image-card-info">
      <div class="image-title" :title="item.title">{{
        item.title || '标题'
      }}</div>
      <div class="image-meta">
        <span class="image-desc" :title="item.describe">{{
          item.describe || '无描述'
        }}</span>
        <span v-if="type === 'image'" class="image-count">
          {{ item.image_list?.length || 0 }}张图片
        </span>
        <span v-if="type === 'mix'" class="image-count">
          {{ item.image_list?.length || 0 }}个素材
        </span>
      </div>
    </div>
    <div
      v-if="isRemove"
      class="image-card-remove"
      @click.stop="$emit('remove')"
    >
      <icon-close size="12" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import MixFlagLogo from '@/views/account/content-manage/components/mix-flag-logo.vue';
  import { isToday } from '@/utils/util';

  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
    selectKeys: {
      type: Array,
      default: () => [],
    },
    multipleAction: {
      type: Boolean,
      default: false,
    },
    isRemove: {
      type: Boolean,
      default: false,
    },
    openEdit: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'default',
    },
    canSelect: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: 'image',
    },
  });

  defineEmits(['select', 'remove', 'edit', 'del', 'download']);
</script>

<style scoped lang="less">
  .image-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 228px;
    border: 1px solid var(--color-border-1);
    .image-select-indicator {
      position: absolute;
      top: 2px;
      left: 2px;
      color: #fff;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 999;
    }
    &.is-mini {
      width: 130px;
      .image-preview-icon {
        font-size: 32px;
      }
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
        &:hover {
          opacity: 0.95;
          scale: 1.2;
        }
      }
    }

    &.is-selected {
      position: relative;
      // border: 1px solid rgb(var(--primary-6));
      &::before {
        content: '已选择';
        position: absolute;
        left: -1px;
        top: 0;
        background: rgb(var(--primary-6));
        color: #fff;
        font-size: 10px;
        padding: 2px 8px;
        width: max-content;
        height: max-content;
        border-radius: var(--border-radius-large) 0 var(--border-radius-large) 0;
        z-index: 2;
      }
      .image-select-indicator {
        opacity: 0;
      }
    }

    .image-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: var(--color-text-3);

      .image-count {
        position: absolute;
        left: 0;
        top: 5px;
        background: var(--color-fill-2);
        padding: 2px 6px;
        border-radius: 0 var(--border-radius-large) var(--border-radius-large) 0;
        font-size: 10px;
        color: var(--color-text-2);
        z-index: 99;
      }

      .image-desc {
        color: var(--color-text-3);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &.can-select {
      .image-meta {
        .image-count {
          top: 25px;
        }
      }
    }
  }

  .image-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0px;
      bottom: 0px;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }
    .ai-icon {
      position: absolute;
      right: 5px;
      bottom: 16px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 18px;
        height: 18px;
      }
      &.mini {
        bottom: 18px;
        img {
          width: 16px;
          height: 16px;
        }
      }
    }
    .publish-num-tag {
      position: absolute;
      left: 5px;
      bottom: 20px;
      z-index: 2;
      .publish-num-text {
        font-size: 10px;
        color: #fff;
      }
    }
    .new-tag {
      position: absolute;
      left: 5px;
      bottom: 0px;
      z-index: 2;
      img {
        width: 28px;
        border-radius: 5px;
      }
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      color: #fff;
      font-size: 40px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.5s ease;
      translate: -50% -50%;
    }

    .image-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 99;
    }

    .action-icon {
      width: 24px;
      height: 24px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
      }
    }

    &:hover {
      .image-actions {
        opacity: 1;
      }
    }
  }

  .image-card-info {
    padding: 4px 8px;
  }

  .image-title {
    font-size: 12px;
    color: var(--color-text-1);
    line-height: 1.2;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
  }

  .image-card-remove {
    position: absolute;
    top: 4px;
    right: 8px;
    width: 16px;
    height: 16px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 99;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
    }
  }

  .image-card-edit {
    position: absolute;
    top: 4px;
    right: 32px;
    width: 16px;
    height: 16px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 99;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
    }
  }

  .image-card:hover .image-card-remove,
  .image-card:hover .image-card-edit {
    opacity: 1;
  }
</style>

<template>
  <div class="list-card">
    <div class="list-header">
      <a-tabs v-model:active-key="activeKey" :hide-content="true">
        <a-tab-pane key="1">
          <template #title>
            <icon-check-circle /> 已选择
            <span v-if="selectedItems.length > 0" class="select-count"
              >{{ selectedItems.length }}个</span
            >
          </template>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #title><icon-unordered-list /> 全部 </template>
        </a-tab-pane>
      </a-tabs>
      <div v-if="activeKey === '2'" class="select-all-box">
        <a-checkbox
          v-model="selectAll"
          :indeterminate="indeterminate"
          @change="handleSelectAllChange"
        >
          全选
        </a-checkbox>
      </div>
    </div>

    <!-- 快速筛选区域 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="filter-row">
          <div class="filter-group">
            <span class="filter-label">平台</span>
            <platform-select
              v-model="formModel.platform"
              size="small"
              style="width: 95px"
              @change="handleSubmit({})"
            />
          </div>
          <div class="filter-group">
            <span class="filter-label">分组</span>
            <!-- <request-select
              v-model="formModel.product_id"
              size="small"
              style="width: 120px"
              api="product"
              @change="handleSubmit({})"
            /> -->
            <folder-tree-select
              v-model="formModel.dir_id"
              style="width: 120px"
              :send-params="{
                type: 'account',
              }"
              value-key="id"
              label-key="dir_name"
              request-url="/api/material/dirList"
              :format-data="(arr:any) => arr?.children || []"
              :allow-clear="true"
              @change="handleSubmit({})"
            ></folder-tree-select>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-group">
            <span class="filter-label">账号</span>
            <a-input-search
              v-model="formModel.account_name"
              placeholder="搜索账号名称或ID"
              style="width: 100%"
              size="small"
              @search="handleSubmit({})"
              @keydown.enter="handleSubmit({})"
            />
          </div>
        </div>
      </div>
    </div>

    <a-list
      :loading="loading"
      :data="showListData"
      :grid="{ gutter: 12, column: 4 }"
      :bordered="false"
      class="account-list"
    >
      <template #item="{ item }">
        <a-list-item>
          <div
            :class="[
              'account-card',
              { 'account-card-selected': isSelected(item.id) },
            ]"
            hoverable
            @click="handleCardClick(item)"
          >
            <div class="card-content">
              <div class="checkbox-wrapper">
                <a-checkbox
                  :checked="isSelected(item.id)"
                  @click.stop
                  @change="(e) => handleItemSelect(e, item)"
                />
              </div>
              <a-image
                :src="item.avatar_url"
                :width="48"
                :height="48"
                :fit="'fill'"
                class="avatar"
              />
              <div class="account-info">
                <div class="account-name">{{ item.account_name }}</div>
                <div class="account-id">ID: {{ item.account_id }}</div>
              </div>
              <div class="platform-icon">
                <img
                  :src="getPlatformIcon(item.platform)"
                  :alt="item.platform"
                />
              </div>
            </div>
          </div>
        </a-list-item>
      </template>
    </a-list>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, reactive, ref, computed, watch, watchEffect } from 'vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import { cloneDeep, uniqBy } from 'lodash';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
  });

  const emits = defineEmits(['update:modelValue', 'save', 'close']);

  const visible = ref(false);
  const loading = ref(false);
  const listData = ref([]);
  const selectedItems = ref<any[]>([]);
  const selectedCacheItems = ref<any[]>([]);
  const activeKey = ref('2');

  const generateFormModel = () => {
    return {
      account_name: '',
      platform: '',
      product_id: '',
      dir_id: '',
    };
  };
  const formModel = reactive(generateFormModel());

  // 平台选项
  const platforms = [
    { label: '全部', value: '' },
    { label: '抖音', value: '抖音' },
    { label: '小红书', value: '小红书' },
  ];

  // 产品选项
  const products = [
    { label: '全部', value: '' },
    { label: '产品A', value: 'product_a' },
    { label: '产品B', value: 'product_b' },
  ];

  // 根据activeKey和筛选条件切换列表
  const showListData = computed(() => {
    if (activeKey.value === '1') {
      return selectedItems.value;
    }
    return listData.value;
  });

  // Watch for external modelValue changes
  watch(
    () => props.modelValue,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(selectedItems.value)) {
        selectedItems.value = cloneDeep(newVal);
      }
    },
    { deep: true }
  );

  // Watch for internal selectedItems changes
  watch(
    selectedItems,
    (newVal) => {
      emits('update:modelValue', cloneDeep(newVal));
    },
    { deep: true }
  );

  const selectAll = ref(false);
  const indeterminate = computed(() => {
    return (
      selectedItems.value.length > 0 &&
      selectedItems.value.length < listData.value.length
    );
  });

  const isSelected = (id: string) => {
    return selectedItems.value.some((item) => item.id === id);
  };

  const handleSelectAllChange = (checked: boolean) => {
    if (checked) {
      // 如果全选，则选中所有账号 追加
      selectedItems.value = uniqBy(
        [...selectedItems.value, ...listData.value],
        'id'
      );
      selectAll.value = true;
    } else {
      // 如果取消全选，则取消当前所有的listData账号
      selectedItems.value = selectedItems.value.filter((i) => {
        return !listData.value.some((j) => j.id === i.id);
      });
      selectAll.value = false;
    }
  };

  const handleItemSelect = (e: any, item: any) => {
    if (e.target.checked) {
      selectedItems.value = uniqBy([...selectedItems.value, item], 'id');
    } else {
      selectedItems.value = selectedItems.value.filter((i) => i.id !== item.id);
    }
  };

  const handleCardClick = (item: any) => {
    const index = selectedItems.value.findIndex((i) => i.id === item.id);
    if (index > -1) {
      selectedItems.value.splice(index, 1);
    } else {
      selectedItems.value = uniqBy([...selectedItems.value, item], 'id');
    }
    // 如果选中数量等于列表数量，则全选
    if (selectedItems.value.length === listData.value.length) {
      selectAll.value = true;
    } else {
      selectAll.value = false;
    }
  };

  const getList = async (data: any) => {
    selectedCacheItems.value = selectedItems.value;
    const res = await request('/api/devices/accountList', {
      ...formModel,
      ...data,
      ws_status: 'connected',
    });
    listData.value = res.data.data || [];
    return res;
  };

  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    getList(resData).finally(() => {
      loading.value = false;
      // 判断每一个账号是否选中
      listData.value.forEach((item) => {
        if (selectedItems.value.some((i) => i.id === item.id)) {
          item.checked = true;
        }
      });
      // 如果选中数量等于列表数量，则全选
    });
  };

  const getPlatformIcon = (platform: string) => {
    const platformIcons: Record<string, string> = {
      抖音: '/icons/platform/抖音.png',
      小红书: '/icons/platform/小红书.png',
    };
    return (
      platformIcons[platform.toLowerCase()] || '/platform-icons/default.png'
    );
  };

  handleSubmit();
</script>

<style scoped lang="less">
  .list-card {
    .list-header {
      position: relative;
      margin-top: 5px;
      .select-all-box {
        position: absolute;
        right: 10px;
        top: 5px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin: 10px 10px 0;
      }
    }

    .filter-section {
      padding: 6px 6px 6px 12px;
      border-radius: var(--border-radius-medium);
      margin: 8px 0 0 0;

      .filter-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .filter-row {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        .filter-label {
          color: var(--color-text-2);
          font-size: 13px;
          white-space: nowrap;
        }
      }
    }

    .account-list {
      padding: 0px 12px 12px;
      .account-card {
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        font-size: 12px;
        border-radius: var(--border-radius-large);
        border: 1px solid var(--color-border-1);
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 0px 8px rgba(0, 0, 0, 0.09);

          .checkbox-wrapper {
            opacity: 1;
          }
        }

        &.account-card-selected {
          position: relative;
          // border: 1px solid rgb(var(--primary-6));
          color: rgb(var(--primary-6));

          .checkbox-wrapper {
            opacity: 1;
          }
          &::before {
            content: '已选择';
            position: absolute;
            left: -1px;
            top: 0;
            background: rgb(var(--primary-6));
            color: #fff;
            font-size: 8px;
            padding: 1px 8px;
            width: max-content;
            height: max-content;
            border-radius: var(--border-radius-large) 0
              var(--border-radius-large) 0;
            z-index: 1;
          }
        }

        .card-content {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          position: relative;

          .checkbox-wrapper {
            position: absolute;
            top: 4px;
            left: 4px;
            z-index: 1;
            opacity: 0;
            transition: opacity 0.2s ease;
            // background: rgba(255, 255, 255, 0.9);
            border-radius: 4px;
            padding: 2px;
          }

          .avatar {
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
            background-color: #eee;
          }

          .account-info {
            flex: 1;
            overflow: hidden;
            min-width: 0;
            margin-right: 8px;

            .account-name {
              font-size: 14px;
              font-weight: 500;
              margin-bottom: 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .account-id {
              color: #666;
              font-size: 12px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .platform-icon {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }
    }
  }
</style>

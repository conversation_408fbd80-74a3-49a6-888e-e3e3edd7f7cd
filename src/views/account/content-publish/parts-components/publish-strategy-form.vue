<template>
  <a-form
    class="publish-strategy-form"
    :model="formModel"
    auto-label-width
    layout="vertical"
  >
    <a-form-item label="内容分配规则：">
      <dict-radio v-model="formModel.sort" :data-list="sortMap"></dict-radio>
    </a-form-item>
    <a-form-item label="单账号内容数量：">
      <a-input-number
        v-model="formModel.num"
        :min="1"
        :precision="0"
        class="w-200"
        allow-clear
      ></a-input-number>
    </a-form-item>
    <a-form-item label="发布方式：">
      <template #help>
        <div class="publish-strategy-form-help">
          <icon-info-circle />
          从创意灵感发布内容时，会从已选的灵感词中顺序查找，直到匹配到合适的灵感词进行发布
        </div>
      </template>
      <dict-radio
        v-model="formModel.publish_entrance"
        :data-list="contentPublishEntryM"
        @change="
          formModel.inspiration_detail_douyin = [];
          formModel.inspiration_detail_xiaohongshu = [];
        "
      ></dict-radio>
    </a-form-item>
    <template v-if="formModel.publish_entrance === '创意灵感'">
      <template v-if="hasDouyin">
        <div class="word-list-wrapper">
          <div class="label">抖音创意灵感词：</div>
          <div class="word-list-header">
            <span
              v-if="formModel.inspiration_detail_douyin.length"
              class="mr-10"
            >
              已选{{ formModel.inspiration_detail_douyin.length }}个
            </span>
            <a-button
              type="text"
              @click="
                selectRef?.show(formModel.inspiration_detail_douyin, 'dy')
              "
            >
              <template #icon>
                <icon-plus />
              </template>
              添加
            </a-button>
          </div>
        </div>
      </template>
      <template v-if="hasXiaohongshu">
        <div class="word-list-wrapper">
          <div class="label">小红书创意灵感词：</div>
          <div class="word-list-header">
            <span v-if="formModel.inspiration_detail_xiaohongshu.length">
              已选{{ formModel.inspiration_detail_xiaohongshu.length }}个
            </span>
            <a-button
              type="text"
              @click="
                selectRef?.show(formModel.inspiration_detail_xiaohongshu, 'xhs')
              "
            >
              <template #icon>
                <icon-plus />
              </template>
              添加
            </a-button>
          </div>
        </div>
      </template>

      <div class="word-list">
        <div
          v-for="(item, index) in [
            ...formModel.inspiration_detail_douyin,
            ...formModel.inspiration_detail_xiaohongshu,
          ]"
          :key="index + 'word'"
          class="word-item"
        >
          <div class="ml-5">
            <div style="font-weight: bold">{{ item.name }}</div>
            <div class="mt-5">
              <a-space>
                <a-tag
                  v-for="(tag, indexTag) in item.tags"
                  :key="indexTag + 'tag'"
                  color="arcoblue"
                >
                  {{ tag }}
                </a-tag>
              </a-space>
            </div>
            <div class="mt-5">
              {{ numToYiOrWan(item.popularity || 0).text }}个作品
            </div>
          </div>
        </div>
      </div>
    </template>

    <inspiration-select-modal
      ref="selectRef"
      :send-params="inspirationParams"
      @save="setInspiration"
    />
  </a-form>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { contentPublishEntryM } from '@/components/dict-select/dict-account';
  import { numToYiOrWan } from '@/utils/util';
  import { numberFormat } from '@/utils/table-utils/table-util';
  import InspirationSelectModal from '@/views/account/content-publish/parts-components/inspiration-select-modal.vue';

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    accountList: {
      type: Array,
      default: () => [],
    },
    contentList: {
      type: Array,
      default: () => [],
    },
  });

  const emits = defineEmits(['update:modelValue']);

  const formModel = ref<any>({});
  const selectRef = ref();
  const sortMap = [
    { label: '顺序', value: 'order' },
    { label: '随机', value: 'random' },
  ];
  const hasDouyin = computed(() =>
    props.accountList.some((item: any) => item.platform === '抖音')
  );
  const hasXiaohongshu = computed(() =>
    props.accountList.some((item: any) => item.platform === '小红书')
  );
  const inspirationParams = computed(() => ({
    account_ids: props.accountList.map((item: any) => item.account_id),
  }));

  // Watch for external modelValue changes
  watch(
    () => props.modelValue,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(formModel.value)) {
        formModel.value = cloneDeep(newVal);
      }
    },
    { deep: true }
  );

  // Watch for internal formModel changes
  watch(
    formModel,
    (newVal) => {
      emits('update:modelValue', cloneDeep(newVal));
    },
    { deep: true }
  );

  // 监听内容列表
  watch(
    () => props.contentList,
    (newVal) => {
      formModel.value.num = newVal.length;
    }
  );

  function setInspiration(items: any[], platform: string) {
    switch (platform) {
      case 'dy':
        formModel.value.inspiration_detail_douyin = items;
        break;
      case 'xhs':
        formModel.value.inspiration_detail_xiaohongshu = items;
        break;
      default:
        break;
    }
  }

  // Initialize formModel with modelValue
  formModel.value = cloneDeep(props.modelValue);

  defineExpose({
    formModel,
  });
</script>

<style scoped lang="less">
  .word-list-wrapper {
    border-top: 1px solid var(--color-border-1);
    padding-top: 10px;
    margin-top: 10px;
    .label {
      font-weight: bold;
    }
    .word-list-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;
    }
  }

  .word-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
    padding-top: 10px;
    border-top: 1px solid var(--color-border-1);
    .word-item {
      border: 1px solid var(--color-neutral-3);
      padding: 5px;
      display: flex;
      width: 100%;
      border-radius: var(--border-radius-large);
      padding: 10px;
    }
  }

  .publish-strategy-form {
    margin-top: 10px;
    padding: 5px 10px 10px;

    .publish-strategy-form-help {
      margin-top: 5px;
    }
  }
</style>

<template>
  <d-modal
    :visible="visible"
    width="1265px"
    title="选择内容"
    class="no-left-right-padding-card"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <mix-content
      ref="listRef"
      :can-select="true"
      :is-select="true"
      :is-scroll="true"
    />
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { ref } from 'vue';
  import MixContent from '@/views/account/content-manage/mix-content/mix-content.vue';

  const props = defineProps({
    // 是不是单选
    isSingle: {
      type: Boolean,
      default: false,
    },
  });
  const listRef = ref();
  const visible = ref(false);
  const emits = defineEmits(['save', 'close']);
  function handleCancel() {
    visible.value = false;
    emits('close');
  }
  function handleBeforeOk() {
    emits('save', listRef.value?.getSelectItems());
    handleCancel();
  }
  function show() {
    listRef.value?.clearSelect();
    visible.value = true;
  }

  defineExpose({
    show,
  });
</script>

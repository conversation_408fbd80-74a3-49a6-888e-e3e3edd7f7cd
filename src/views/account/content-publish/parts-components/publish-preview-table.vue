<template>
  <div class="publish-preview-box">
    <div class="table-header">
      <div>
        <span v-if="rowSelection.selectedRowKeys.length"
          >已选择{{ rowSelection.selectedRowKeys.length }}个账号</span
        >
      </div>
      <a-space>
        <a-button
          type="text"
          :disabled="!rowSelection.selectedRowKeys.length"
          @click="mixRef?.show(tableData)"
        >
          <template #icon>
            <icon-plus />
          </template>
          添加内容
        </a-button>
      </a-space>
    </div>
    <a-table
      :data="tableData"
      :pagination="false"
      row-key="account_id"
      size="small"
      :hoverable="false"
      :row-selection="rowSelection"
      @selection-change="selectionChange"
    >
      <template #columns>
        <a-table-column
          title="账号"
          data-index="account_id"
          align="center"
          :width="260"
        >
          <template #cell="{ record }: TableColumnSlot">
            <div class="df ai-cen">
              <a-avatar>
                <img :src="record.avatar_url" />
              </a-avatar>
              <div class="ml-10 df fd-cl" style="text-align: left; flex: 1">
                <a-typography-text ellipsis class="mb-5">
                  {{ record.account_name }}
                </a-typography-text>
                <a-typography-text
                  type="secondary"
                  ellipsis
                  copyable
                  :copy-text="record.account_id"
                  style="margin-bottom: 0"
                >
                  ID: {{ record.account_id }}
                </a-typography-text>
              </div>
            </div>
          </template>
        </a-table-column>

        <a-table-column title="内容" data-index="content_detail" align="center">
          <template #cell="{ record }: TableColumnSlot">
            <div
              class="df fw-wrap content-scroll-box"
              style="display: inline-flex !important"
            >
              <div
                v-for="(item, index) in record.content_detail"
                :key="index"
                style="border-color: transparent; margin: 3px"
              >
                <image-content-item
                  v-if="item.type === 'image_text'"
                  :item="item"
                  is-remove
                  size="mini"
                  :can-select="false"
                  @remove="record.content_detail.splice(index, 1)"
                />
                <video-content-item
                  v-else
                  :item="item"
                  is-remove
                  size="mini"
                  :can-select="false"
                  :open-edit="false"
                  @remove="record.content_detail.splice(index, 1)"
                />
                <!-- 添加按钮 -->
                <!-- <a-button type="text">
                  <template #icon>
                    <icon-plus />
                  </template>
                  添加视频
                </a-button> -->
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column
          title="发布方式"
          data-index="action"
          align="center"
          :width="320"
        >
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <div>
              <div>
                <dict-select
                  v-model="record.publish_entrance"
                  style="width: 140px"
                  :allow-clear="false"
                  :data-list="contentPublishEntryM"
                />
              </div>
              <template v-if="record.publish_entrance === '创意灵感'">
                <a-popover content="点击编辑灵感词">
                  <template #content>
                    <div class="tag-list">
                      <div
                        v-for="(word, index) in record.inspiration_detail"
                        :key="index"
                        class="mt-5"
                      >
                        <span
                          >#{{ word.name
                          }}{{
                            index < record.inspiration_detail.length - 1
                              ? '，'
                              : ''
                          }}</span
                        >
                      </div>
                    </div>
                  </template>
                  <div
                    class="inspiration_detail-box"
                    @click="
                      curIndex = rowIndex;
                      selectRef.show(
                        record.inspiration_detail || [],
                        record.platform === '小红书' ? 'xhs' : 'dy'
                      );
                    "
                  >
                    <div>
                      已选{{ record.inspiration_detail?.length }}个灵感词
                      <icon-edit class="primary_color" />
                    </div>
                  </div>
                </a-popover>
              </template>
            </div>
          </template>
        </a-table-column>
        <a-table-column
          title="操作"
          data-index="action"
          align="center"
          :width="100"
        >
          <template #cell="{ record, rowIndex }: TableColumnSlot">
            <a-space direction="vertical">
              <a-link
                type="text"
                @click="
                  selectionChange([record.account_id]);
                  mixRef?.show(tableData);
                "
              >
                <template #icon>
                  <icon-plus />
                </template>
                添加
              </a-link>
              <a-link
                status="danger"
                type="text"
                @click="tableData.splice(rowIndex, 1)"
              >
                <template #icon>
                  <icon-delete />
                </template>
                移除
              </a-link>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <inspiration-select-modal ref="selectRef" @save="setInspiration" />
    <mix-content-select ref="mixRef" @save="setContent" />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, PropType, reactive, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import { contentPublishEntryM } from '@/components/dict-select/dict-account';
  import { allowMixPlatform, getFileType } from '@/utils/util';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import InspirationSelectModal from '@/views/account/content-publish/parts-components/inspiration-select-modal.vue';
  import MixContentSelect from '@/views/account/content-publish/parts-components/mix-content-select.vue';

  const props = defineProps({
    list: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:list']);

  const tableData = ref<any[]>([]);
  const selectRef = ref();
  // const contentRef = ref();
  const mixRef = ref();
  const curIndex = ref(0);
  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: false,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
  });
  const selectionChange = (selectedRowKeys: any[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys;
  };

  function setInspiration(items: any[]) {
    tableData.value[curIndex.value].inspiration_detail = items;
    emit('update:list', cloneDeep(tableData.value));
  }
  function setContent(items: any[]) {
    tableData.value.forEach((item) => {
      if (rowSelection.selectedRowKeys.includes(item.account_id)) {
        // 如果是混合类型且不被允许使用混合类型的平台，则需要将素材列表根据文件名 拆分成两部分、视频和图片
        items.forEach((curInfo: any) => {
          if (curInfo.type === 'video_image') {
            const videoList = curInfo.image_list.filter(
              (j: any) => getFileType(j) === 'video'
            );
            // 视频需要拆分成多个
            if (videoList.length) {
              videoList.forEach((k: any) => {
                item.content_detail.push({
                  ...curInfo,
                  content_id: curInfo.id,
                  type: 'video',
                  content_type: 'video',
                  video_url: k,
                  cover_url: curInfo.cover_url,
                  image_list: null,
                });
              });
            }
            const imageList = curInfo.image_list.filter(
              (j: any) => getFileType(j) === 'image'
            );
            if (imageList.length) {
              item.content_detail.push({
                ...curInfo,
                content_id: curInfo.id,
                type: 'image_text',
                content_type: 'image_text',
                video_url: null,
                image_list: imageList,
                cover_url: imageList[0],
              });
            }
          } else {
            item.content_detail.push({
              ...curInfo,
              content_id: curInfo.id,
              type: curInfo.type,
              content_type: curInfo.type,
              video_url: curInfo.video_url,
              image_list: curInfo.image_list,
              cover_url: curInfo.cover_url,
            });
          }
        });
      }
    });
    emit('update:list', cloneDeep(tableData.value));
  }

  onMounted(() => {
    tableData.value = cloneDeep(props.list);
  });

  // Watch for list prop changes
  watch(
    () => props.list,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(tableData.value)) {
        tableData.value = cloneDeep(newVal);
      }
    },
    { deep: true }
  );

  // Watch for table data changes
  watch(
    tableData,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(props.list)) {
        emit('update:list', cloneDeep(newVal));
      }
    },
    { deep: true }
  );

  defineExpose({
    tableData,
  });
</script>

<style scoped lang="less">
  .publish-preview-box {
    margin-top: 10px;
  }
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  .tag-list {
    overflow: auto;
    max-height: 100px;
  }
  .inspiration_detail-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5px;
    cursor: pointer;
  }

  .content-scroll-box {
    overflow-y: auto;
    max-height: 200px;
    padding: 10px;
    background-color: var(--color-fill-1);
    margin: 10px 0;
    border-radius: var(--border-radius-large);
  }
</style>

<template>
  <div class="content-box">
    <div class="content-container">
      <div class="content-section">
        <div class="content-section-header">
          <div class="content-section-title">
            已选择内容
            <span v-if="dataList.length" class="number-text">
              ({{ dataList.length }}个)
            </span>
          </div>
          <div v-if="dataList.length" class="content-section-extra">
            <a-button type="text" size="mini" @click="resetContent">
              <template #icon><icon-refresh /></template>
              重置
            </a-button>
          </div>
          <!-- 清空 -->
        </div>
        <div class="content-grid">
          <template v-for="(item, index) in dataList" :key="item.id">
            <div @click="selectChange(item)">
              <video-content-item
                v-if="item.type === 'video'"
                :multiple-action="multipleAction"
                :select-keys="selectKeys"
                :item="item"
                :can-select="false"
                is-remove
                :open-edit="false"
                size="mini"
                @remove="dataList.splice(index, 1)"
              />
              <image-content-item
                v-else-if="item.type === 'image_text'"
                :multiple-action="multipleAction"
                :select-keys="selectKeys"
                :item="item"
                :open-edit="false"
                is-remove
                size="mini"
                :can-select="false"
                @remove="dataList.splice(index, 1)"
              />
              <image-content-item
                v-else-if="item.type === 'video_image'"
                :multiple-action="multipleAction"
                :select-keys="selectKeys"
                :item="item"
                :open-edit="false"
                is-remove
                size="mini"
                :can-select="false"
                type="mix"
                @remove="dataList.splice(index, 1)"
              />
            </div>
          </template>
          <div class="empty-state">
            <div class="empty-state-content" @click="mixRef?.show()">
              <icon-plus size="40" />
              <span class="empty-state-text">点击添加内容</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <mix-content-select ref="mixRef" @save="setContent" />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import MixContentSelect from '@/views/account/content-publish/parts-components/mix-content-select.vue';
  import { IconRefresh } from '@arco-design/web-vue/es/icon';
  import { useRoute } from 'vue-router';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';

  const route = useRoute();

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const emits = defineEmits(['update:modelValue']);

  const dataList = ref<any[]>([]);
  const selectKeys = ref<number[]>([]);
  const multipleAction = ref(false);
  const mixRef = ref();

  // Watch for external modelValue changes
  watch(
    () => props.modelValue,
    (newVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(dataList.value)) {
        dataList.value = cloneDeep(newVal);
      }
    },
    { deep: true, immediate: true }
  );

  // Watch for internal dataList changes
  watch(
    dataList,
    (newVal) => {
      emits('update:modelValue', cloneDeep(newVal));
    },
    { deep: true }
  );

  // Watch for selection changes
  watch(
    selectKeys,
    (newVal) => {
      // 当选择变化时，更新预览
      emits('update:modelValue', cloneDeep(dataList.value));
    },
    { deep: true }
  );

  function selectChange(item: any) {
    if (selectKeys.value.includes(item.id)) {
      selectKeys.value = selectKeys.value.filter((val) => val !== item.id);
    } else {
      selectKeys.value.push(item.id);
    }
  }

  function delAction() {
    dataList.value = dataList.value.filter(
      (item) => !selectKeys.value.includes(item.id)
    );
    selectKeys.value = [];
  }

  function setContent(items: any[]) {
    console.log(items);
    dataList.value.push(
      ...items.map((item, index) => ({
        ...item,
        key: `${Date.now()}_${index}`,
      }))
    );
  }

  function resetContent() {
    dataList.value = [];
    selectKeys.value = [];
  }

  // Initialize dataList with modelValue
  dataList.value = cloneDeep(props.modelValue);

  // 初始化的时候要查询路由的ids
  onMounted(() => {
    const { ids } = route.query;
    if (ids) {
      // 请求接口
      request('/api/content/list', {
        ids: ids.split(','),
      })
        .then((res) => {
          if (res.code === 0) {
            dataList.value = res.data.data;
          } else {
            Message.error(res.msg || '获取勾选的发布内容失败，请重新选择');
          }
        })
        .catch((error) => {
          Message.error('获取勾选的发布内容失败，请重新选择');
          console.error('Failed to fetch content:', error);
        });
    }
  });

  defineExpose({
    dataList,
  });
</script>

<style scoped lang="less">
  .content-box {
    padding: 16px;
    background: var(--color-bg-2);
    border-radius: 4px;
  }

  .content-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .content-section {
    .content-section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    padding: 8px 0;
  }

  .empty-state {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    // margin-top: 10px;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .empty-state-text {
    margin-top: 12px;
    color: var(--color-text-3);
    font-size: 12px;
  }
  .empty-state-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 160px;
    padding: 20px 0;
    border: 2px dashed var(--color-neutral-3);
    border-radius: 8px;
    transition: all 0.3s ease;
    height: 99%;

    &:hover {
      border-color: rgb(var(--primary-6));
      color: rgb(var(--primary-6));
      .empty-state-text {
        color: rgb(var(--primary-6));
      }
    }
  }

  .number-text {
    color: rgb(var(--primary-6));
    margin-left: 5px;
    font-weight: 500;
  }
</style>

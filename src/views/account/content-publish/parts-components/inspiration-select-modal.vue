<template>
  <d-modal
    :visible="visible"
    width="1100px"
    title="选择灵感词"
    @cancel="handleCancel"
  >
    <!--<search-form-fold
      :form-data="formModel"
      :get-default-form-data="generateFormModel"
      :continue-key="['platform']"
      @search="handleSubmit()"
    >
      <template #formItemGroup>
        <a-form-item label="类型">
          <dict-select
            v-model="formModel.source"
            :data-list="contentPublishEntryTypeM"
          />
        </a-form-item>
      </template>
    </search-form-fold>-->
    <a-input-search
      v-model="formModel.name"
      placeholder="请输入关键词"
      class="w-300"
      allow-clear
      @keydown.enter="handleSubmit()"
      @search="handleSubmit()"
    />

    <a-card size="small" class="table-card">
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParams"
        :row-selection="rowSelection"
        :scroll-percent="{ x: 1000, y: '60vh' }"
        :auto-request="false"
        :data-handle="dataHandle"
        :sorter-config="{
          field: 'order_field',
          value: 'order_type',
        }"
        @select-change="selectionChange"
      >
        <template #tags="{ record }: TableColumnSlot">
          <a-space v-if="record.tags?.length">
            <a-tag v-for="(item, index) in record.tags" :key="index">
              {{ item }}
            </a-tag>
          </a-space>
          <span v-else>-</span>
        </template>
      </base-table>
    </a-card>
    <template #footer>
      <a-space>
        <a-button
          status="danger"
          @click="
            rowSelection.selectedRows = [];
            rowSelection.selectedRowKeys = [];
            rowSelection.selectedCacheRows = [];
          "
        >
          清空
        </a-button>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleBeforeOk()">
          确定（已选{{ rowSelection.selectedRows.length }}个灵感词）
        </a-button>
      </a-space>
    </template>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, PropType, reactive, ref } from 'vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import { cloneDeep, uniqBy } from 'lodash';
  import { contentPublishEntryTypeM } from '@/components/dict-select/dict-account';
  import {
    getDictTxtRender,
    numberFormatShow,
  } from '@/utils/table-utils/columns-config';

  const visible = ref(false);
  const emits = defineEmits(['save', 'close']);
  const props = defineProps({
    sendParams: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  const generateFormModel = () => {
    return {
      name: '',
      source: '',
      platform: '',
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());
  const tableParams = computed(() => ({
    ...props.sendParams,
    ...formModel,
  }));

  const columns = [
    {
      title: '灵感词',
      dataIndex: 'name',
    },
    {
      title: '作品数量',
      dataIndex: 'popularity',
      render: numberFormatShow(),
      sortable: {
        sortDirections: ['ascend', 'descend'],
        sorter: true,
      },
    },
    {
      title: '类型',
      dataIndex: 'source',
      render: getDictTxtRender(contentPublishEntryTypeM),
    },
    {
      title: '标签',
      dataIndex: 'tags',
    },
  ];

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: false,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
    selectedCacheRows: [] as any[],
  });

  function dataHandle(data: any[]) {
    // data.forEach((item: any) => {
    //  try {
    //    item.tags = JSON.parse(item.tags) || [];
    //  } catch (e) {
    //    item.tags = [];
    //  }
    // });
    return data;
  }

  const selectionChange = (selectedRowKeys: any, selectedRows: any) => {
    rowSelection.selectedRows = uniqBy(
      [
        ...selectedRows,
        ...rowSelection.selectedCacheRows.filter((item) =>
          selectedRowKeys.includes(item.id)
        ),
      ],
      'id'
    );
    rowSelection.selectedRowKeys = rowSelection.selectedRows.map(
      (item: any) => item.id
    );
  };

  const getList = async (data: any) => {
    rowSelection.selectedCacheRows = rowSelection.selectedRows;
    return request('/api/inspiration/list', {
      ...data,
    });
  };
  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    setTimeout(() => {
      theTable.value?.search();
    }, 200);
  };

  function handleCancel() {
    visible.value = false;
    emits('close');
  }
  function handleBeforeOk() {
    emits('save', rowSelection.selectedRows, formModel.platform);
    handleCancel();
  }
  function show(items: any[], platform: string) {
    formModel.platform = platform;
    rowSelection.selectedCacheRows = cloneDeep(items);
    rowSelection.selectedRows = cloneDeep(items);
    rowSelection.selectedRowKeys = rowSelection.selectedRows.map(
      (item: any) => item.id
    );
    visible.value = true;
    nextTick(() => {
      handleSubmit();
    });
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

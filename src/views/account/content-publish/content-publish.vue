<template>
  <div>
    <a-spin :loading="loading" tip="拼命加载中..." style="width: 100%">
      <a-card size="mini">
        <div class="content">
          <div class="area-content">
            <div class="area-left">
              <div class="vertical-title"> 选择账号 </div>
              <account-select-list
                ref="accountRef"
                v-model="formModel.account_list"
              ></account-select-list>
            </div>
            <div class="area-center">
              <div class="vertical-title"> 选择内容 </div>
              <content-select-show
                ref="contentRef"
                v-model="formModel.content_list"
              ></content-select-show>
            </div>
            <div class="area-right">
              <div class="vertical-title"> 发布策略 </div>
              <publish-strategy-form
                ref="strategyRef"
                v-model="formModel.strategy"
                :account-list="formModel.account_list"
                :content-list="formModel.content_list"
              ></publish-strategy-form>
            </div>
          </div>
          <div class="area-preview-box">
            <div class="preview-header">
              <div class="vertical-title"> 预览并发布 </div>
              <!-- <div class="refresh-button">
                <a-button type="text" @click="refreshAction">
                  <template #icon>
                    <icon-sync />
                  </template>
                  刷新预览
                </a-button>
              </div> -->
            </div>
            <div class="preview-table-box">
              <publish-preview-table
                ref="previewRef"
                v-model:list="tableData"
              ></publish-preview-table>
              <div class="area-footer">
                <div class="footer-button">
                  <a-popconfirm
                    content="请检查配置，是否确认发布？"
                    @ok="submitAction()"
                  >
                    <a-button
                      type="primary"
                      :loading="loading"
                      :disabled="!tableData.length"
                    >
                      <template #icon>
                        <icon-send />
                      </template>
                      一键发布
                    </a-button>
                  </a-popconfirm>
                </div>
                <div class="footer-button-group">
                  <a-button type="outline" @click="refreshAction">
                    <template #icon>
                      <icon-eye />
                    </template>
                    预览刷新
                  </a-button>
                  <a-popconfirm
                    content="确认重置全部信息吗？"
                    @ok="resetInfo()"
                  >
                    <a-button type="text" status="danger">
                      <template #icon>
                        <icon-undo />
                      </template>
                      全部重置
                    </a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, ref, watch, h } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { isEmpty, shuffle } from 'lodash';
  import request from '@/api/request';
  import { useRouter } from 'vue-router';
  import AccountSelectList from '@/views/account/content-publish/parts-components/account-select-list.vue';
  import ContentSelectShow from '@/views/account/content-publish/parts-components/content-select-show.vue';
  import PublishStrategyForm from '@/views/account/content-publish/parts-components/publish-strategy-form.vue';
  import PublishPreviewTable from '@/views/account/content-publish/parts-components/publish-preview-table.vue';
  import {
    getFileType,
    allowMixPlatform,
    getCharacterLength,
  } from '@/utils/util';

  const router = useRouter();

  const defaultForm = () => ({
    step: 1,
    account_list: [],
    content_list: [],
    strategy: {
      sort: 'order', // order 顺序 random 随机
      num: 1, // 单账户内容数量
      publish_entrance: '普通入口', // 发布入口：普通入口、创意灵感
      entrance_type_douyin: 'for_you', // 入口类型： 抖音(为你推荐、实时追热、大家在搜)
      entrance_type_xiaohongshu: 'recommended', // 入口类型： 小红书(推荐)
      inspiration_detail_douyin: [],
      inspiration_detail_xiaohongshu: [],
    },
  });
  const formModel = ref(defaultForm());
  const loading = ref(false);
  const accountRef = ref();
  const contentRef = ref();
  const strategyRef = ref();
  const previewRef = ref();
  const tableData = ref<any[]>([]);

  // 填充内容
  function fillContent(content_list: any[], list: any[], num = 1) {
    content_list.forEach((item: any) => {
      let targetItem = list.find((i: any) => i.content_detail.length < num);
      if (targetItem && !allowMixPlatform.includes(targetItem.platform)) {
        // 如果是混合类型且不被允许使用混合类型的平台，则需要将素材列表根据文件名 拆分成两部分、视频和图片
        if (item.type === 'video_image') {
          const videoList = item.image_list.filter(
            (j: any) => getFileType(j) === 'video'
          );
          // 视频需要拆分成多个
          if (videoList.length) {
            videoList.forEach((k: any) => {
              targetItem.content_detail.push({
                ...item,
                content_id: item.id,
                type: 'video',
                content_type: 'video',
                video_url: k,
                cover_url: item.cover_url,
                image_list: null,
              });
            });
          }
          const imageList = item.image_list.filter(
            (j: any) => getFileType(j) === 'image'
          );
          if (imageList.length) {
            targetItem.content_detail.push({
              ...item,
              content_id: item.id,
              type: 'image_text',
              content_type: 'image_text',
              video_url: null,
              image_list: imageList,
              cover_url: imageList[0],
            });
          }
        } else {
          targetItem.content_detail.push({
            ...item,
            content_id: item.id,
            content_type: item.type,
            video_url: item.video_url,
            image_list: item.image_list,
            cover_url: item.cover_url,
          });
        }
      }
    });
    if (list.some((i: any) => i.content_detail.length < num)) {
      fillContent(content_list, list, num);
    }
  }

  // 组装预览报文
  function makeTableData() {
    tableData.value = [];
    let { account_list, strategy, content_list } = formModel.value;
    if (strategy.sort === 'random') {
      content_list = shuffle(content_list);
    }

    // 根据账号组装基础数据
    tableData.value = account_list.map((account: any) => ({
      account_id: account.account_id,
      account_name: account.account_name,
      avatar_url: account.avatar_url,
      platform: account.platform,
      contents: [],
      content_detail: [],
      publish_entrance: isEmpty(
        account.platform === '小红书'
          ? strategy.inspiration_detail_xiaohongshu
          : strategy.inspiration_detail_douyin
      )
        ? '普通入口'
        : strategy.publish_entrance,
      entrance_type:
        account.platform === '小红书'
          ? strategy.entrance_type_xiaohongshu
          : strategy.entrance_type_douyin,
      inspiration_detail:
        account.platform === '小红书'
          ? strategy.inspiration_detail_xiaohongshu
          : strategy.inspiration_detail_douyin,
    }));
    fillContent(content_list, tableData.value, strategy.num);
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  // 提交发布请求
  function submitAction() {
    if (
      !tableData.value.length ||
      tableData.value.some((item) => !item.content_detail.length)
    ) {
      if (!tableData.value.length) {
        Message.error(`请选择要发布内容的账号,并完善配置后发布！`);
      } else {
        Message.error(`每个账号至少需要一个内容`);
      }
    } else {
      loading.value = true;
      request('/api/contentPublish/release', {
        data: tableData.value.map((item: any) => ({
          ...item,
          content_ids: item.content_detail.map((i: any) => i.id),
          contents: undefined,
          // content_detail: undefined,
          inspiration_detail:
            item.publish_entrance === '创意灵感' ? item.inspiration_detail : [],
        })),
      })
        .then(() => {
          Modal.success({
            title: '发布任务提交成功!',
            content: '',
            okText: '前往任务列表',
            cancelText: '继续发布',
            hideCancel: false,
            onOk: () => {
              router.push({ name: 'publish-task' });
            },
            onCancel: () => {
              formModel.value = defaultForm();
            },
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  }

  function nextAction() {
    switch (formModel.value.step) {
      case 1:
        formModel.value.account_list = accountRef.value.account_list || [];
        if (!formModel.value.account_list.length) {
          Message.error('请选择发布账号');
          return;
        }
        formModel.value.step += 1;
        break;
      case 2:
        formModel.value.content_list = contentRef.value.dataList || [];
        if (!formModel.value.content_list.length) {
          Message.error('请选择发布内容');
          return;
        }
        formModel.value.step += 1;
        formModel.value.strategy.num = formModel.value.content_list.length;
        break;
      case 3:
        formModel.value.strategy = strategyRef.value.formModel;
        makeTableData();
        formModel.value.step += 1;
        break;
      default:
        break;
    }
  }

  function resetInfo() {
    formModel.value = defaultForm();
    formModel.value.step = 0;
    makeTableData();
    nextTick(() => {
      formModel.value.step = 1;
    });
  }

  /**
   * 验证发布内容的字数限制
   * @returns {boolean} 验证是否通过
   */
  function validateContentLimits() {
    const titleLimit = 20; // 标题字数限制
    const describeLimit = 900; // 正文字数限制
    const violations: string[] = []; // 违规内容列表

    // 检查素材数量超限的内容
    const beyondItems = formModel.value.content_list.filter((item: any) => {
      return item.image_list && item.image_list.length > 12;
    });

    if (beyondItems.length > 0) {
      Modal.warning({
        title: '素材数量超限提醒',
        content: () =>
          h('div', { style: 'line-height: 1.6;' }, [
            h(
              'p',
              {
                style: 'margin-bottom: 16px; color: var(--color-text-2);',
              },
              '您提交的内容内素材数量超出最大限制，请将发布的内容包含的素材调整至12个以内（包含12个）后发布。'
            ),

            h('div', {}, [
              h(
                'div',
                {
                  style:
                    'font-weight: 500; margin-bottom: 8px; color: var(--color-text-1);text-align: left;',
                },
                '问题内容：'
              ),
              h(
                'ul',
                {
                  style:
                    'margin: 0; padding-left: 16px; list-style: none;text-align: left;max-height: 300px;overflow-y: auto;',
                },
                beyondItems.map((item: any, index: number) =>
                  h(
                    'li',
                    {
                      key: index,
                      style: 'margin-bottom: 4px; color: var(--color-text-2);',
                    },
                    `${item.name || `内容${index + 1}`}：素材数量超限`
                  )
                )
              ),
            ]),
          ]),
        okText: '确认',
        width: 620,
        onOk: () => {
          return true;
        },
      });
      return false;
    }

    // 检查每个内容的标题和正文
    formModel.value.content_list.forEach((content: any, index: number) => {
      const contentName = content.name || `内容${index + 1}`;

      // 检查标题长度
      if (content.title && getCharacterLength(content.title) > titleLimit) {
        // content.title.length > titleLimit) {
        violations.push(`${contentName}: 标题字数超限`);
      }

      // 检查正文长度
      if (
        content.describe &&
        getCharacterLength(content.describe) > describeLimit
      ) {
        // content.describe.length > describeLimit) {
        violations.push(`${contentName}: 正文字数超限`);
      }
    });

    // 如果有违规内容，显示警告弹窗
    if (violations.length > 0) {
      const violationDetails = violations.map((violation) => {
        const [name, type] = violation.split(': ');
        const content = formModel.value.content_list.find(
          (c: any) =>
            (c.name || `内容${formModel.value.content_list.indexOf(c) + 1}`) ===
            name
        );

        if (type === '标题字数超限') {
          return `${name}: 标题字数超限（当前${getCharacterLength(
            content.title
          )}字，限制${titleLimit}字）`;
        }
        return `${name}: 正文字数超限（当前${getCharacterLength(
          content.describe
        )}字，限制${describeLimit}字）`;
      });

      Modal.warning({
        title: '字数超限提醒',
        content: () =>
          h('div', { style: 'line-height: 1.6;' }, [
            h(
              'p',
              {
                style: 'margin-bottom: 16px; color: var(--color-text-2);',
              },
              '您提交的内容，超出最大输入字数限制，请调整后发布。'
            ),

            h(
              'div',
              {
                style:
                  'margin-bottom: 16px; padding: 12px; background: var(--color-fill-1); border-radius: 6px;',
              },
              [
                h(
                  'div',
                  {
                    style:
                      'color: var(--color-danger); font-weight: 500; margin-bottom: 4px;',
                  },
                  `标题字数限制：${titleLimit}个字以内`
                ),
                h(
                  'div',
                  {
                    style: 'color: var(--color-danger); font-weight: 500;',
                  },
                  `正文字数限制：${describeLimit}个字以内`
                ),
              ]
            ),

            h('div', {}, [
              h(
                'div',
                {
                  style:
                    'font-weight: 500; margin-bottom: 8px; color: var(--color-text-1);text-align: left;',
                },
                '问题内容：'
              ),
              h(
                'ul',
                {
                  style:
                    'margin: 0; padding-left: 16px; list-style: none;text-align: left;max-height: 300px;overflow-y: auto;',
                },
                violationDetails.map((detail, index) =>
                  h(
                    'li',
                    {
                      key: index,
                      style: 'margin-bottom: 4px; color: var(--color-text-2);',
                    },
                    `• ${detail}`
                  )
                )
              ),
            ]),
          ]),
        okText: '确认',
        width: 620,
      });

      return false;
    }

    return true;
  }

  function refreshAction() {
    // 没有勾选账号
    if (!formModel.value.account_list.length) {
      Message.error('请选择发布账号');
      return;
    }
    // 没有勾选内容
    if (!formModel.value.content_list.length) {
      Message.error('请选择发布内容');
      return;
    }

    // 验证内容字数限制
    if (!validateContentLimits()) {
      return;
    }

    loading.value = true;
    makeTableData();
  }
</script>

<style scoped lang="less">
  .content {
    .area-content {
      display: flex;
      justify-content: space-between;
      min-height: 500px;
      .area-left {
        width: 320px;
        padding-right: 10px;
        border-right: 1px solid var(--color-border-1);
      }
      .area-center {
        flex: 1;
        margin: 0 10px;
        border-right: 1px solid var(--color-border-1);
      }
      .area-right {
        width: 260px;
        overflow: auto;
      }
    }
    .area-preview-box {
      border-top: 1px solid var(--color-border-1);
      padding-top: 10px;
    }
    .preview-header {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      .refresh-button {
        margin-left: 10px;
      }
    }
  }

  .preview-table-box {
    position: relative;
    padding-bottom: 60px;

    .area-footer {
      position: fixed;
      bottom: 0;
      // left: 0;
      right: 30px;
      display: flex;
      justify-content: center;
      width: 100%;
      background-color: var(--color-bg-1);
      padding: 10px 0;
      z-index: 100;
      width: calc(100% - 220px);
      right: 20px;
      @media (max-width: 1600px) {
        width: calc(100% - 230px);
        right: 29px;
      }
      @media (max-width: 1200px) {
        width: calc(100% - 30px);
        right: 29px;
      }
      .footer-button {
        margin-right: 10px;
      }
      .footer-button-group {
        width: 200px;
        display: flex;
        justify-content: space-between;
        gap: 10px;
      }
    }
  }
</style>

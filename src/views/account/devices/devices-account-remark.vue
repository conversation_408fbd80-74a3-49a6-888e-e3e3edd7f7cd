<template>
  <d-modal
    v-model:visible="visible"
    width="400px"
    :mask-closable="false"
    :esc-to-close="false"
    unmount-on-close
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    class="account-remark-modal"
    :title="`【${formModel.account_name || '-'}】账号备注`"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      class="mt-10"
      :model="formModel"
      :auto-label-width="true"
    >
      <!-- 账号基本信息展示 -->
      <!-- <div class="account-info-display">
        <div class="account-avatar-section">
          <img
            v-if="formModel.avatar_url"
            class="account-avatar"
            :src="formModel.avatar_url"
            alt="账号头像"
          />
          <img
            v-else
            class="account-avatar"
            src="@/assets/images/avatar.png"
            alt="默认头像"
          />
        </div>
        <div class="account-details-section">
          <div class="account-name-row">
            <span class="account-name">{{ formModel.account_name }}</span>
            <img
              v-if="formModel.is_company_account == 1"
              class="blue-v-icon"
              src="@/assets/images/blue-v.png"
              alt="蓝V认证"
            />
          </div>
          <div class="account-id">ID: {{ formModel.account_id }}</div>
          <div class="account-platform">
            <img
              class="platform-icon"
              :src="`icons/platform/${formModel.platform}.png`"
              :alt="formModel.platform"
            />
            <span class="platform-name">{{ formModel.platform }}</span>
          </div>
        </div>
      </div> -->

      <!-- 备注输入区域 -->
      <a-form-item label="账号备注" hide-label field="remark">
        <a-textarea
          v-model="formModel.remark"
          :max-length="500"
          :auto-size="{ minRows: 6, maxRows: 12 }"
          show-word-limit
          placeholder="请输入账号备注信息"
          allow-clear
        />
      </a-form-item>

      <!-- 备注提示信息 -->
      <!-- <div class="remark-tips">
        <icon-info-circle class="tip-icon" />
        <span class="tip-text">备注信息将帮助团队更好地了解和管理该账号</span>
      </div> -->
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()">保存</a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { IconInfoCircle } from '@arco-design/web-vue/es/icon';

  // 默认表单数据
  const defaultForm = () => ({
    id: null,
    account_id: '',
    account_name: '',
    avatar_url: '',
    platform: '',
    is_company_account: 0,
    remark: '',
  });

  // 组件事件定义
  const emit = defineEmits(['refresh', 'success']);

  // 响应式数据
  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());

  // 表单验证方法
  const validate = () => {
    return formRef.value?.validate();
  };

  // 清除验证
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  /**
   * 显示备注弹窗
   * @param accountInfo 账号信息
   */
  const show = (accountInfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;

    if (accountInfo) {
      // 复制账号基本信息
      Object.assign(formModel.value, {
        id: accountInfo.id,
        account_id: accountInfo.account_id,
        account_name: accountInfo.account_name,
        avatar_url: accountInfo.avatar_url,
        platform: accountInfo.platform,
        is_company_account: accountInfo.is_company_account,
        remark: accountInfo.remark || '', // 现有备注信息
      });
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  /**
   * 取消操作
   */
  const handleCancel = () => {
    visible.value = false;
  };

  /**
   * 保存备注
   */
  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;

      try {
        await request('/api/devices/accountSave', {
          id: formModel.value.id,
          account_id: formModel.value.account_id,
          remark: formModel.value.remark,
        });

        Message.success('备注保存成功');
        handleCancel();
        emit('refresh');
        emit('success', {
          account_id: formModel.value.account_id,
          remark: formModel.value.remark,
        });
      } catch (error) {
        console.error('保存备注失败:', error);
        Message.error('保存备注失败，请重试');
      } finally {
        loading.value = false;
      }
    }
  };

  // 暴露方法给父组件
  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .account-remark-modal {
    :deep(.arco-modal-header) {
      border-bottom: 1px solid var(--color-border-2);
      padding-bottom: 16px;
    }

    :deep(.arco-modal-body) {
      padding-top: 20px;
    }
  }

  // 账号信息展示区域
  .account-info-display {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--color-fill-1);
    border-radius: var(--border-radius-medium);
    margin-bottom: 20px;

    .account-avatar-section {
      flex-shrink: 0;

      .account-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 3px solid var(--color-border-1);
        object-fit: cover;
      }
    }

    .account-details-section {
      flex: 1;
      min-width: 0;

      .account-name-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .account-name {
          font-size: 16px;
          font-weight: 600;
          color: var(--color-text-1);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .blue-v-icon {
          width: 18px;
          height: 18px;
          flex-shrink: 0;
        }
      }

      .account-id {
        font-size: 13px;
        color: var(--color-text-3);
        margin-bottom: 8px;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
          monospace;
      }

      .account-platform {
        display: flex;
        align-items: center;
        gap: 6px;

        .platform-icon {
          width: 20px;
          height: 20px;
          border-radius: 4px;
        }

        .platform-name {
          font-size: 13px;
          color: var(--color-text-2);
          text-transform: capitalize;
        }
      }
    }
  }

  // 备注提示信息
  .remark-tips {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(var(--primary-1), 0.5);
    border-radius: var(--border-radius-medium);
    margin-top: 16px;

    .tip-icon {
      color: rgb(var(--primary-6));
      font-size: 16px;
      flex-shrink: 0;
    }

    .tip-text {
      font-size: 13px;
      color: var(--color-text-2);
      line-height: 1.4;
    }
  }
</style>

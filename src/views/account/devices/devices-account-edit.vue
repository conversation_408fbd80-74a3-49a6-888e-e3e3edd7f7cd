<template>
  <d-modal
    v-model:visible="visible"
    width="400px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    unmount-on-close
    class="account-edit-modal"
    :title="`【${formModel.account_name || '-'}】账号配置`"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      class="mt-10"
      :model="formModel"
      :auto-label-width="true"
    >
      <a-form-item
        v-if="!!formModel.id"
        label="所属分组"
        :rules="{ required: true, message: '请选择', trigger: 'blur' }"
        field="dir_id"
      >
        <!-- <request-select
          v-model="formModel.product_ids"
          api="product"
          :multiple="false"
        /> -->
        <folder-tree-select
          v-model="formModel.dir_id"
          :send-params="{
            type: 'account',
          }"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/dirList"
          :format-data="(arr:any) => arr?.children || []"
          :allow-clear="false"
        ></folder-tree-select>
      </a-form-item>
      <a-form-item
        v-if="!!formModel.id"
        label="运营人员"
        field="assign_user_ids"
        :rules="{ required: true, message: '请选择', trigger: 'blur' }"
      >
        <request-select
          v-model="formModel.assign_user_ids"
          multiple
          api="allUser"
        />
      </a-form-item>
      <!-- 账号备注 -->
      <a-form-item label="账号备注" field="remark">
        <a-textarea
          v-model="formModel.remark"
          :auto-size="{ minRows: 4, maxRows: 8 }"
          :max-length="100"
          show-word-limit
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule } from '@/utils/util';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import RequestSelect from '@/components/select/request-select.vue';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';

  const defaultForm = () => ({
    id: null,
    product_ids: '',
    assign_user_ids: [],
    dir_id: '',
  });

  const emit = defineEmits(['refresh']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      // Object.keys(initForm).forEach((key) => {
      //   // @ts-ignore
      //   formModel.value[key] =
      //     dinfo[key] || initForm[key as keyof typeof initForm];
      // });
      Object.assign(formModel.value, dinfo);
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/devices/accountSave', {
        ...formModel.value,
      })
        .then(() => {
          request('/api/account/accountBindUser', {
            account_id: formModel.value.account_id,
            assign_user_ids: formModel.value.assign_user_ids,
          }).then(() => {
            Message.success('保存成功');
            handleCancel();
            emit('refresh');
          });
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

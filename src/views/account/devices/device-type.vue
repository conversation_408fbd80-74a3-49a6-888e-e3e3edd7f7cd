<template>
  <div class="phone-box">
    <!-- <img class="phone-bg" src="/icons/phone/phone.png" alt="" /> -->
    <img v-if="iconPath" class="phone-icon" :src="iconPath" alt="" />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  const typeList = {
    honor: 'icons/phone/honor.png',
    huwei: 'icons/phone/huwei.png',
    iqoo: 'icons/phone/iqoo.png',
    oneplus: 'icons/phone/oneplus.png',
    oppo: 'icons/phone/oppo.png',
    realme: 'icons/phone/realme.png',
    redmi: 'icons/phone/redmi.png',
    vivo: 'icons/phone/vivo.png',
    xiaomi: 'icons/phone/xiaomi.png',
    samsung: 'icons/phone/sanxing.png',
  };
  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });
  const iconPath = computed(() => {
    if (!props.type) return '';
    return typeList[props.type.toLowerCase() as keyof typeof typeList];
  });
</script>

<style scoped lang="less">
  .phone-box {
    position: relative;
    .phone-bg {
      width: 100%;
    }
    .phone-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 36px;
    }
  }
</style>

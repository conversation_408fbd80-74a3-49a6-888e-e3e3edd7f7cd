<template>
  <div>
    <search-form-fold
      :form-data="formModel"
      :get-default-form-data="generateFormModel"
      :continue-key="['device_id']"
      @search="handleSubmit()"
    >
      <template #formItemGroup>
        <a-form-item label="账号">
          <a-input v-model="formModel.account_name" allow-clear />
        </a-form-item>
        <a-form-item label="平台">
          <platform-select v-model="formModel.platform" />
        </a-form-item>
        <a-form-item label="产品">
          <request-select v-model="formModel.product_id" api="product" />
        </a-form-item>
      </template>
    </search-form-fold>

    <!-- <div class="table-card-header">
       <div> </div>
       <a-space>

       </a-space>
     </div>-->
    <!--table 区域-->
    <base-table
      ref="theTable"
      v-model:loading="loading"
      class="mt-10"
      :columns-config="columns"
      :data-config="getDataList"
      :data-handle="dataHandle"
      :send-params="formModel"
      :auto-request="false"
      :scroll-percent="{ x: 1400, maxHeight: '50vh' }"
    >
      <template #account_id="{ record }: TableColumnSlot">
        <div class="df ai-cen">
          <a-image
            :width="40"
            :height="40"
            :fit="'contain'"
            :src="record.avatar_url"
          />
          <div class="ml-10 df fd-cl" style="text-align: left; flex: 1">
            <a-typography-text ellipsis class="mb-5">
              {{ record.account_name }}
            </a-typography-text>
            <a-typography-text
              type="secondary"
              ellipsis
              copyable
              :copy-text="record.account_id"
              style="margin-bottom: 0"
            >
              ID: {{ record.account_id }}
            </a-typography-text>
          </div>
        </div>
      </template>
      <template #banner_url="{ record }: TableColumnSlot">
        <a-image
          width="100%"
          height="90px"
          fit="contain"
          :src="record.banner_url"
        />
      </template>
      <template #introduction="{ record }: TableColumnSlot">
        <span style="white-space: pre-line">
          {{ record.introduction }}
        </span>
      </template>
      <template #action="{ record }: TableColumnSlot">
        <a-space direction="vertical">
          <a-link @click="decorationRef?.show(record)">
            <icon-tool />
            {{ record.fit_up_status === 1 ? '装修中' : '账号装修' }}
          </a-link>
          <a-popconfirm
            :content="`确认删除账号【${record.account_name}】吗？`"
            :ok-loading="!!record.okLoading"
            @ok="delAccountAction(record)"
          >
            <a-link status="danger"> <icon-delete />删除 </a-link>
          </a-popconfirm>
        </a-space>
      </template>
      <template #product_names="{ record }: TableColumnSlot">
        <a-space>
          {{ record.product_names?.join('，') || '-' }}
          <a-link @click="editRef?.show(record)(record)">
            <icon-edit />
          </a-link>
        </a-space>
      </template>
      <template #online_status="{ record }: TableColumnSlot">
        <a-tag :color="getColor(accountStatusM, record.online_status)">
          {{ record.status_str }}
        </a-tag>
      </template>
    </base-table>
    <devices-account-edit
      ref="editRef"
      @refresh="handleSubmit()"
    ></devices-account-edit>
    <devices-account-decoration
      ref="decorationRef"
    ></devices-account-decoration>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import request from '@/api/request';
  import { accountStatusM } from '@/components/dict-select/dict-account';
  import { Message } from '@arco-design/web-vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import DevicesAccountEdit from '@/views/account/devices/devices-account-edit.vue';
  import DevicesAccountDecoration from '@/views/account/devices/devices-account-decoration.vue';
  import { getColor } from '@/components/dict-select/dict-util';
  import RequestSelect from '@/components/select/request-select.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';

  const props = defineProps({
    deviceId: {
      type: [Number, String],
      default: '',
    },
  });
  const loading = ref(false);
  const columns = [
    { title: '账号', dataIndex: 'account_id', width: 260 },
    { title: '平台', dataIndex: 'platform' },
    { title: '产品', dataIndex: 'product_names' },
    { title: '头图Banner', dataIndex: 'banner_url', width: 160 },
    { title: '简介', dataIndex: 'introduction' },
    { title: '在线状态', dataIndex: 'online_status' },
    { title: '添加时间', dataIndex: 'add_time' },
    { title: '操作', dataIndex: 'action', width: 120 },
  ];
  const generateFormModel = () => {
    return {
      account_name: '',
      platform: '',
      product_id: '',
    };
  };
  const theTable = ref();
  const editRef = ref();
  const decorationRef = ref();
  const formModel = reactive(generateFormModel());

  const getDataList = async (data: any) => {
    return request('/api/devices/accountList', {
      ...data,
      device_id: props.deviceId,
    });
  };
  function dataHandle(data: any[]) {
    data.forEach((item: any) => {
      try {
        [item.banner_url] = JSON.parse(item.banner_url) || [];
      } catch (e) {
        item.banner_url = '';
      }
      try {
        [item.last_fit_up_info.banner_url] =
          JSON.parse(item.last_fit_up_info.banner_url) || [];
      } catch (e) {
        item.last_fit_up_info.banner_url = '';
      }
      // try {
      //  [item.last_fit_up_info.banner] =
      //      JSON.parse(item.last_fit_up_info.banner) || [];
      // } catch (e) {
      //  item.last_fit_up_info.banner = '';
      // }
    });
    return data;
  }

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  // 删除账号
  function delAccountAction(record: any) {
    record.okLoading = true;
    request('/api/devices/accountSave', {
      id: record.id,
      online_status: 'disable',
    })
      .then((res) => {
        Message.success('操作成功');
        handleSubmit();
      })
      .finally(() => {
        record.okLoading = false;
      });
  }
  let isLoad = false;
  function firstLoad() {
    if (!isLoad) {
      handleSubmit();
      isLoad = true;
    }
  }
  defineExpose({
    firstLoad,
  });
</script>

<style scoped lang="less"></style>

<!-- eslint-disable no-use-before-define -->
<template>
  <div class="content-box">
    <a-card class="view-box">
      <div class="df">
        <!-- 设备列表 -->
        <folder-tree-list
          ref="foldTreeRef"
          v-model:dir-id="formModel.dir_id"
          class="w-200"
          :apis="dirApis"
          :send-params="dirParams"
          :show-pop-menu="(nodeData:any) => nodeData.parent_id"
          :show-resource-count="true"
          whole-title="账号分组"
          @change="dirChange"
        ></folder-tree-list>
        <a-divider :margin="5" direction="vertical" class="divider-line" />
        <!-- 账号列表 -->
        <div style="flex: 1; min-width: 0">
          <search-form-fold
            :form-data="formModel"
            :get-default-form-data="generateFormModel"
            :continue-key="['device_ids']"
            class="no-top-padding-card"
            @search="handleSubmit()"
          >
            <template #formItemGroup>
              <a-form-item label="账号">
                <a-input
                  v-model="formModel.account_name"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
              <a-form-item label="平台">
                <platform-select v-model="formModel.platform"></platform-select>
              </a-form-item>
              <a-form-item label="产品">
                <request-select v-model="formModel.product_id" api="product" />
              </a-form-item>
              <a-form-item label="状态">
                <dict-select
                  v-model="formModel.online_status"
                  :data-list="accountStatusLastM"
                />
              </a-form-item>
              <!-- 运营人员 -->
              <a-form-item label="运营">
                <request-select
                  v-model="formModel.assign_user_ids"
                  multiple
                  api="allUser"
                />
              </a-form-item>
              <!-- 备注 -->
              <a-form-item label="备注">
                <a-input
                  v-model="formModel.remark"
                  placeholder="请输入"
                  allow-clear
                />
              </a-form-item>
            </template>
          </search-form-fold>

          <!-- 视图切换按钮组 -->
          <div class="view-switch-container">
            <a-radio-group
              v-model="currentView"
              type="button"
              class="view-switch-group"
              @change="handleViewChange"
            >
              <a-radio value="card">
                <icon-apps class="view-icon" />卡片
              </a-radio>
              <a-radio value="table">
                <icon-list class="view-icon" />列表
              </a-radio>
            </a-radio-group>
          </div>

          <!--内容区域-->
          <template v-if="pagination.total > 0">
            <!-- 卡片视图 -->
            <div v-if="currentView === 'card'" class="account-list-box">
              <div
                v-for="item in accountList"
                :key="item.id"
                class="account-item"
              >
                <div class="account-top">
                  <img
                    v-if="item.banner_url"
                    class="account-bg"
                    :src="item.banner_url || ''"
                    alt=""
                  />
                  <img
                    v-else
                    class="account-bg"
                    src="@/assets/images/logo-h.png"
                    alt=""
                  />

                  <div class="df jc-sb account-top-info">
                    <img
                      class="account-platform"
                      :src="`icons/platform/${item.platform}.png`"
                      alt=""
                    />

                    <icon-settings
                      class="account-setting"
                      @click="showSettingsOverlay(item)"
                    />

                    <div class="online-status">
                      <!-- 正常在线状态 -->
                      <a-badge
                        :style="{
                          color: getColor(
                            accountStatusLastM,
                            item.online_status
                          ),
                        }"
                        :color="
                          getColor(accountStatusLastM, item.online_status)
                        "
                        :text="getText(accountStatusLastM, item.online_status)"
                      >
                      </a-badge>
                    </div>
                  </div>
                  <div class="df ai-cen account-info">
                    <img
                      v-if="item.avatar_url"
                      class="account-avatar"
                      :src="item.avatar_url"
                      alt=""
                    />
                    <img
                      v-else
                      class="account-avatar"
                      src="@/assets/images/avatar.png"
                      alt=""
                    />

                    <div class="account-info-content">
                      <div class="df ai-cen account-info-content-box">
                        <template
                          v-if="
                            item.account_name && item.account_name.length > 8
                          "
                        >
                          <a-tooltip :content="item.account_name">
                            <div class="ellipsis-1-line">
                              {{ item.account_name }}
                            </div>
                          </a-tooltip>
                        </template>
                        <template v-else>
                          <div class="ellipsis-1-line">
                            {{ item.account_name }}
                          </div>
                        </template>
                        <!-- 是否为蓝V -->
                        <img
                          v-if="item.is_company_account == 1"
                          class="blue-v-icon"
                          src="@/assets/images/blue-v.png"
                          alt=""
                        />
                      </div>

                      <a-typography-paragraph>
                        ID: {{ item.account_id }}
                      </a-typography-paragraph>
                      <!-- 授权 -->
                      <!-- 授权状态按钮 -->
                      <!-- 去授权按钮：企业号且未授权时显示 -->
                      <div class="auth-status-btn-box">
                        <a-link
                          v-if="
                            item.is_company_account == 1 &&
                            item.auth_status == 0
                          "
                          size="mini"
                          type="text"
                          status="warning"
                          :loading="item.isAuthorizing"
                          @click="handleAuthorization(item)"
                        >
                          <icon-safe v-if="!item.isAuthorizing" />
                          {{ item.isAuthorizing ? '授权中...' : '去授权' }}
                        </a-link>

                        <!-- 绑定聚光账号按钮：企业号已授权但聚光未授权时显示 -->
                        <a-link
                          v-if="
                            item.is_company_account == 1 &&
                            item.auth_status == 1 &&
                            item.app_auth_status == 0
                          "
                          size="mini"
                          type="text"
                          status="success"
                          :loading="item.isAuthorizing"
                          @click="handleBindSpotlight(item)"
                        >
                          <icon-link v-if="!item.isAuthorizing" />
                          {{
                            item.isAuthorizing ? '绑定中...' : '绑定聚光账号'
                          }}
                        </a-link>

                        <!-- 已授权按钮：企业号全部授权完成或个人号聚光已授权时显示 -->
                        <a-link
                          v-if="
                            (item.is_company_account == 1 &&
                              item.auth_status == 1 &&
                              item.app_auth_status == 1) ||
                            (item.is_company_account != 1 &&
                              item.app_auth_status == 1)
                          "
                          type="text"
                          status="success"
                          size="mini"
                          class="auth-button"
                          disabled
                        >
                          <icon-check-circle />
                          已授权
                        </a-link>
                      </div>
                    </div>
                  </div>
                  <div class="account-info-box mb-10">
                    <a-space direction="vertical" align="center" :size="4">
                      <div class="account-no">{{ item.like_count || '0' }}</div>
                      <div class="account-no-text">获赞</div>
                    </a-space>
                    <!-- <a-space direction="vertical" align="center" :size="4">
                      <div class="account-no">
                        {{ item.mutual_follower_count || '0' }}
                      </div>
                      <div class="account-no-text">互关</div>
                    </a-space> -->
                    <a-space direction="vertical" align="center" :size="4">
                      <div class="account-no">
                        {{ item.following_count || '0' }}
                      </div>
                      <div class="account-no-text">关注</div>
                    </a-space>
                    <a-space direction="vertical" align="center" :size="4">
                      <div class="account-no">
                        {{ item.follower_count || '0' }}
                      </div>
                      <div class="account-no-text">粉丝</div>
                    </a-space>
                  </div>
                </div>
                <div class="account-bot">
                  <!-- 内容展示区域 -->
                  <div class="account-content">
                    <!-- 已发布内容数量 -->
                    <div class="content-header">
                      <span class="content-count"
                        >已发布: {{ item.posts_num || 0 }}</span
                      >
                    </div>

                    <!-- 内容列表 -->
                    <div
                      v-if="item.top_posts && item.top_posts.length > 0"
                      class="content-list"
                    >
                      <div
                        v-for="content in item.top_posts.slice(0, 2)"
                        :key="content.id"
                        class="content-item"
                        @click="toContent(item, content)"
                      >
                        <div class="content-preview">
                          <img
                            v-if="content.cover_url"
                            :src="content.cover_url"
                            class="content-cover"
                            alt=""
                          />
                          <div v-else class="content-placeholder">
                            <icon-image />
                          </div>
                        </div>
                        <div class="content-info">
                          <div class="content-title">{{
                            content.title || '无标题'
                          }}</div>
                          <div class="content-stats">
                            <span class="stat-icon">
                              <icon-heart />
                              {{ content.like_count || 0 }}
                            </span>
                            <span class="stat-icon">
                              <icon-message />
                              {{ content.comment_count || 0 }}
                            </span>
                            <span class="stat-icon">
                              <icon-star />
                              {{ content.favorite_count || 0 }}
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- 查看更多 -->
                      <div class="view-more">
                        <span class="view-more-text" @click="toContent(item)">
                          <span v-if="item.posts_num > 2">
                            其余{{ item.posts_num - 2 }}篇
                          </span>
                          <span v-else>查看全部</span>
                          <icon-down />
                        </span>
                      </div>
                    </div>

                    <!-- 无内容状态 -->
                    <div v-else class="no-content">
                      <div class="no-content-icon">
                        <icon-file />
                      </div>
                      <div class="no-content-text">账号还未发布内容</div>
                      <div class="no-content-action">
                        <span class="action-text">快去发布第1篇内容吧~</span>
                      </div>
                    </div>
                  </div>
                  <div class="account-action">
                    <a-divider :margin="10" />
                    <div class="df ai-cen mb-5">
                      <span style="white-space: nowrap">
                        <icon-storage class="mr-5" />分组：
                      </span>
                      <a-tooltip v-if="item.dir_name" :content="item.dir_name">
                        <span class="ellipsis" style="flex: 1">
                          {{ item.dir_name || '-' }}
                        </span>
                      </a-tooltip>
                      <span v-else class="ellipsis" style="flex: 1"> - </span>
                      <!-- <a-button
                        type="text"
                        size="small"
                        @click="editRef?.show(item)"
                      >
                        <icon-edit />
                      </a-button> -->
                    </div>
                    <div class="df ai-cen mb-5">
                      <span style="white-space: nowrap">
                        <icon-user-group class="mr-5" />运营：
                      </span>
                      <a-tooltip
                        v-if="item.assign_user_names?.length"
                        :content="item.assign_user_names?.join('，')"
                      >
                        <span class="ellipsis" style="flex: 1">
                          {{ item.assign_user_names?.join('，') || '-' }}
                        </span>
                      </a-tooltip>
                      <span v-else class="ellipsis" style="flex: 1"> - </span>
                    </div>
                    <div class="df ai-cen">
                      <span style="white-space: nowrap">
                        <icon-tag class="mr-5" />备注：
                      </span>
                      <a-tooltip v-if="item.remark" :content="item.remark">
                        <span class="ellipsis" style="flex: 1">
                          {{ item.remark || '-' }}
                        </span>
                      </a-tooltip>
                      <span v-else class="ellipsis" style="flex: 1">{{
                        item.remark || '-'
                      }}</span>
                    </div>
                    <!-- 产品配置提示蒙层 -->
                    <div
                      v-if="!item.product_names?.length"
                      class="product-config-overlay"
                    >
                      <div class="overlay-content">
                        <icon-tool class="overlay-icon" />
                        <div class="overlay-text">
                          <div class="overlay-title">请配置所属分组后使用</div>
                        </div>
                        <a-button
                          type="primary"
                          size="mini"
                          @click="editRef?.show(item)"
                        >
                          立即配置
                        </a-button>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 异常状态提示遮罩层 -->
                <div v-if="item.account_errors" class="exception-overlay">
                  <div class="exception-content">
                    <icon-exclamation-circle size="40" class="exception-icon" />
                    <div class="exception-text">
                      <div class="exception-title">当前账号异常！</div>
                      <div class="exception-desc">
                        {{ item.account_errors }}
                      </div>
                    </div>
                    <a-button
                      type="primary"
                      size="mini"
                      :loading="item.resolving_exception"
                      @click="resolveException(item)"
                    >
                      已处理
                    </a-button>
                  </div>
                </div>

                <!-- 设置蒙层 -->
                <div
                  v-if="item.showSettingsOverlay"
                  class="settings-overlay"
                  @click.self="hideSettingsOverlay(item)"
                >
                  <div class="settings-content">
                    <!-- 返回按钮 -->
                    <div class="settings-header">
                      <a-button
                        type="text"
                        size="small"
                        class="back-button"
                        @click="hideSettingsOverlay(item)"
                      >
                        <template #icon>
                          <icon-left />
                        </template>
                        返回
                      </a-button>
                    </div>

                    <!-- 设置选项 -->
                    <div class="settings-options">
                      <a-button
                        class="setting-option-btn"
                        @click="handleAccountDecoration(item)"
                      >
                        账号装修
                      </a-button>

                      <a-button
                        class="setting-option-btn"
                        @click="handleGroupConfig(item)"
                      >
                        配置分组
                      </a-button>

                      <a-button
                        class="setting-option-btn"
                        @click="handleAccountBackup(item)"
                      >
                        账号备注
                      </a-button>

                      <!-- 执行托管任务开关 -->
                      <div class="setting-switch-item">
                        <span class="switch-label">执行托管任务</span>
                        <a-switch
                          :loading="item.loading"
                          :model-value="item.usage_switch === 'active'"
                          size="small"
                          @change="handleManagementToggle(item)"
                        />
                      </div>

                      <!-- 执行自动陌拜开关 -->
                      <div class="setting-switch-item">
                        <div class="label-box">
                          <span class="switch-label">执行自动陌拜</span>
                          <!-- <span class="label-desc"> 今日陌拜：7/10 </span> -->
                        </div>
                        <a-switch
                          :loading="item.loading"
                          :model-value="item.auto_send_msg_switch === 1"
                          size="small"
                          @change="handleAutoSendMsgToggle(item)"
                        />
                      </div>

                      <!-- 移除账号按钮 -->
                      <a-popconfirm
                        content="确定要移除此账号吗？此操作不可撤销。"
                        type="warning"
                        @ok="handleRemoveAccount(item)"
                      >
                        <a-button
                          class="setting-option-btn"
                          type="outline"
                          status="danger"
                        >
                          移除账号
                        </a-button>
                      </a-popconfirm>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表格视图 -->
            <div
              v-else-if="currentView === 'table'"
              class="account-table-container"
            >
              <base-table
                ref="tableRef"
                v-model:loading="loading"
                :columns-config="tableColumns"
                :data-config="getTableDataList"
                :send-params="formModel"
                :auto-request="false"
                class="account-table"
              >
                <!-- 平台列 -->
                <template #platform="{ record }: TableColumnSlot">
                  <div class="platform-cell">
                    <img
                      class="platform-icon"
                      :src="`icons/platform/${record.platform}.png`"
                      :alt="record.platform"
                    />
                    <span class="platform-name">{{ record.platform }}</span>
                  </div>
                </template>

                <!-- 账号信息列 -->
                <template #account_info="{ record }: TableColumnSlot">
                  <div class="account-info-cell">
                    <div class="account-avatar-wrapper">
                      <img
                        v-if="record.avatar_url"
                        class="account-avatar-small"
                        :src="record.avatar_url"
                        alt="头像"
                      />
                      <img
                        v-else
                        class="account-avatar-small"
                        src="@/assets/images/avatar.png"
                        alt="默认头像"
                      />
                    </div>
                    <div class="account-details">
                      <div class="account-name-row">
                        <span class="account-name">{{
                          record.account_name
                        }}</span>
                        <!-- 蓝V认证标识 -->
                        <img
                          v-if="record.is_company_account == 1"
                          class="blue-v-icon-small"
                          src="@/assets/images/blue-v.png"
                          alt="蓝V认证"
                        />
                      </div>
                      <div class="account-id">ID: {{ record.account_id }}</div>
                    </div>
                  </div>
                </template>

                <!-- 账号类型列 -->
                <template #account_type="{ record }: TableColumnSlot">
                  <a-tag
                    :color="record.is_company_account == 1 ? 'blue' : 'gray'"
                    size="small"
                  >
                    {{ record.is_company_account == 1 ? '企业号' : '个人号' }}
                  </a-tag>
                </template>

                <!-- 托管状态列 -->
                <template #online_status="{ record }: TableColumnSlot">
                  <a-badge
                    :color="getColor(accountStatusLastM, record.online_status)"
                    :text="getText(accountStatusLastM, record.online_status)"
                  />
                </template>

                <!-- 发布内容列 -->
                <template #content_stats="{ record }: TableColumnSlot">
                  <div class="content-stats-cell">
                    <a-button
                      type="text"
                      size="small"
                      class="content-count-btn"
                      @click="toContent(record)"
                    >
                      <icon-file />
                      {{ record.posts_num || 0 }} 篇
                    </a-button>
                  </div>
                </template>

                <!-- 执行托管任务开关列 -->
                <template #management_switch="{ record }: TableColumnSlot">
                  <a-switch
                    :loading="record.loading"
                    :model-value="record.usage_switch === 'active'"
                    size="small"
                    @change="handleManagementToggle(record)"
                  />
                </template>

                <!-- 账号分组列 -->
                <template #group_info="{ record }: TableColumnSlot">
                  <div class="group-info-cell">
                    <a-tooltip
                      v-if="record.dir_name"
                      :content="record.dir_name"
                    >
                      <span class="group-name">{{ record.dir_name }}</span>
                    </a-tooltip>
                    <span v-else class="no-group">未分组</span>
                  </div>
                </template>

                <!-- 运营人员列 -->
                <template #operators="{ record }: TableColumnSlot">
                  <div class="operators-cell">
                    <a-tooltip
                      v-if="record.assign_user_names?.length"
                      :content="record.assign_user_names?.join('，')"
                    >
                      <span class="operators-text">
                        {{ record.assign_user_names?.join('，') }}
                      </span>
                    </a-tooltip>
                    <span v-else class="no-operators">未分配</span>
                  </div>
                </template>

                <!-- 备注信息列 -->
                <template #remarks="{ record }: TableColumnSlot">
                  <div class="remarks-cell">
                    <a-button
                      type="text"
                      size="small"
                      class="remarks-btn"
                      @click="handleAccountBackup(record)"
                    >
                      <template #icon>
                        <icon-edit />
                      </template>
                      <a-tooltip v-if="record.remark" :content="record.remark">
                        <span class="remarks-text">
                          {{ record.remark }}
                        </span>
                      </a-tooltip>
                      <span v-else class="no-remarks">添加备注</span>
                    </a-button>
                  </div>
                </template>

                <!-- 操作列 -->
                <template #actions="{ record }: TableColumnSlot">
                  <div class="actions-cell">
                    <a-space size="mini">
                      <a-tooltip content="装修">
                        <a-link
                          type="text"
                          size="small"
                          @click="decorationRef?.show(record)"
                        >
                          <icon-skin />
                        </a-link>
                      </a-tooltip>
                      <a-tooltip content="配置">
                        <a-link
                          type="text"
                          size="mini"
                          @click="editRef?.show(record)"
                        >
                          <icon-settings />
                        </a-link>
                      </a-tooltip>
                      <!-- 去授权 -->
                      <a-tooltip
                        v-if="
                          record.is_company_account == 1 &&
                          record.auth_status == 0
                        "
                        :content="record.isAuthorizing ? '授权中...' : '去授权'"
                      >
                        <a-link
                          status="warning"
                          :loading="record.isAuthorizing"
                          @click="handleAuthorization(record)"
                        >
                          <icon-safe />
                        </a-link>
                      </a-tooltip>

                      <!-- 绑定聚光账号按钮：企业号已授权但聚光未授权时显示 -->
                      <a-tooltip
                        v-if="
                          record.is_company_account == 1 &&
                          record.auth_status == 1 &&
                          record.app_auth_status == 0
                        "
                        :content="
                          record.isAuthorizing ? '绑定中...' : '绑定聚光账号'
                        "
                      >
                        <a-link
                          type="text"
                          :loading="record.isAuthorizing"
                          @click="handleBindSpotlight(record)"
                        >
                          <icon-safe />
                        </a-link>
                      </a-tooltip>
                      <!-- 备注 -->
                      <a-tooltip content="备注">
                        <a-link
                          type="text"
                          size="mini"
                          @click="handleAccountBackup(record)"
                        >
                          <icon-edit />
                        </a-link>
                      </a-tooltip>
                      <a-popconfirm
                        content="确定要移除此账号吗？此操作不可撤销。"
                        type="warning"
                        position="left"
                        @ok="handleRemoveAccount(record)"
                      >
                        <a-tooltip content="移除">
                          <a-link type="text" size="small" status="danger">
                            <icon-delete />
                          </a-link>
                        </a-tooltip>
                      </a-popconfirm>
                    </a-space>
                  </div>
                </template>
              </base-table>
            </div>

            <!-- 分页器（仅在卡片视图中显示） -->
            <div
              v-if="pagination.total > 0 && currentView === 'card'"
              class="df jc-sb mt-10"
            >
              <span></span>
              <a-pagination
                :current="pagination.current"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                show-total
                @change="onPageChange"
                @page-size-change="onPageSizeChange"
              />
            </div>
          </template>
          <a-empty
            v-else-if="!loading"
            style="
              height: 60vh;
              display: flex;
              align-items: center;
              justify-content: center;
            "
          />
        </div>
      </div>
    </a-card>
    <devices-edit
      ref="editRef"
      @refresh="
        handleSubmit();
        getTreeData();
      "
    ></devices-edit>
    <devices-add-modal ref="addRef"></devices-add-modal>
    <devices-account-decoration
      ref="decorationRef"
    ></devices-account-decoration>
    <devices-account-remark
      ref="remarkRef"
      @refresh="handleSubmit()"
      @success="handleRemarkSuccess"
    ></devices-account-remark>
    <account-content-modal ref="contentRef"></account-content-modal>
  </div>
</template>

<script setup lang="ts">
  import { onUnmounted, reactive, ref, computed, nextTick } from 'vue';
  import request from '@/api/request';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import {
    deviceStatusM,
    accountStatusLastM,
  } from '@/components/dict-select/dict-account';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import DevicesEdit from '@/views/account/devices/devices-account-edit.vue';
  import DevicesAccountDecoration from '@/views/account/devices/devices-account-decoration.vue';
  import DevicesAccountRemark from '@/views/account/devices/devices-account-remark.vue';
  import { Message } from '@arco-design/web-vue';
  import DevicesAddModal from '@/views/account/devices/devices-add-modal.vue';
  import { pageConfig } from '@/utils/table-utils/table-util';
  import dayjs from 'dayjs';
  import DeviceType from '@/views/account/devices/device-type.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import { colors } from '@/components/dict-select/dict-common';
  import { compact } from 'lodash';
  import { useRouter } from 'vue-router';
  import { isLogin } from '@/utils/auth';
  import AccountContentModal from '@/views/account/devices/account-content-modal.vue';
  import {
    IconFaceFrownFill,
    IconExclamationCircle,
    IconUnlock,
    IconLink,
    IconCheckCircle,
    IconLeft,
    IconApps,
    IconList,
    IconFile,
    IconHeart,
    IconUserGroup,
    IconPlus,
    IconUser,
    IconEdit,
    IconSettings,
    IconDelete,
  } from '@arco-design/web-vue/es/icon';
  import FolderTreeList from '@/components/fold-tree/folder-tree-list.vue';
  import debug from '@/utils/env';
  import type { TableColumnSlot } from '@/global';

  // 授权轮询相关类型定义
  interface AuthPollingState {
    isPolling: boolean;
    timer: any | null;
    startTime: number;
    accountId: string;
  }

  // 授权轮询状态管理
  const authPollingMap = new Map<string, AuthPollingState>();

  const list = ref<any[]>([]);
  const loading = ref(false);

  // 视图切换状态
  const currentView = ref<'card' | 'table'>('card');

  // 表格列配置
  const tableColumns = [
    {
      title: '账号信息',
      dataIndex: 'account_info',
      slotName: 'account_info',
    },
    {
      title: '平台',
      dataIndex: 'platform',
      slotName: 'platform',
      align: 'center',
    },
    {
      title: '账号类型',
      dataIndex: 'account_type',
      slotName: 'account_type',
      align: 'center',
    },
    {
      title: '托管状态',
      dataIndex: 'online_status',
      slotName: 'online_status',
      align: 'center',
    },
    {
      title: '发布内容',
      dataIndex: 'content_stats',
      slotName: 'content_stats',
      align: 'center',
    },
    // {
    //   title: '数据统计',
    //   dataIndex: 'data_stats',
    //   slotName: 'data_stats',
    //   align: 'center',
    // },
    // 获赞
    {
      title: '获赞',
      dataIndex: 'like_count',
      align: 'center',
    },
    // 互关
    {
      title: '互关',
      dataIndex: 'mutual_follower_count',
      align: 'center',
    },
    // 关注
    {
      title: '关注',
      dataIndex: 'following_count',
      align: 'center',
    },
    // 粉丝
    {
      title: '粉丝',
      dataIndex: 'follower_count',
      align: 'center',
    },
    {
      title: '执行托管任务',
      dataIndex: 'management_switch',
      slotName: 'management_switch',
      align: 'center',
    },
    {
      title: '账号分组',
      dataIndex: 'group_info',
      slotName: 'group_info',
      align: 'center',
    },
    {
      title: '运营人员',
      dataIndex: 'operators',
      slotName: 'operators',
      align: 'center',
    },
    {
      title: '备注信息',
      dataIndex: 'remarks',
      slotName: 'remarks',
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'actions',
      slotName: 'actions',
      align: 'center',
      fixed: 'right',
    },
  ];
  const generateFormModel = () => {
    return {
      dir_id: 0,
      device_id: '',
      device_ids: [] as number[],
      account_name: '',
      platform: '',
      product_id: '',
      online_status: '',
      assign_user_ids: [],
      remark: '',
    };
  };
  const editRef = ref();
  const addRef = ref();
  const decorationRef = ref();
  const remarkRef = ref();
  const contentRef = ref();
  const tableRef = ref();
  const formModel = reactive(generateFormModel());
  const accountList = ref([]);
  const pagination = reactive(pageConfig());

  function getTags(item: any) {
    return compact([
      item.gender,
      item.birthday,
      item.account_location,
      item.profession,
      item.school,
    ]);
  }

  const router = useRouter();
  function toDetail(item: any) {
    const target = router.resolve({
      name: 'account-content',
      query: {
        id: item.account_id,
      },
    });
    window.open(target.href, '__blank');
  }

  function toContent(item: any, content?: any) {
    if (item.posts_num) {
      contentRef.value?.show(item, content);
    } else {
      Message.warning('该账号没有数据');
    }
  }

  // 表格数据获取方法（用于 base-table 组件）
  const getTableDataList = async (params: any) => {
    const response = await request('/api/devices/accountList', {
      ...params,
    });

    if (response?.data?.data) {
      response.data.data.forEach((item: any) => {
        try {
          [item.banner_url] = JSON.parse(item.banner_url) || [];
        } catch (e) {
          console.log(e);
        }
        try {
          item.last_fit_up_info.banner_url = item.last_fit_up_info.banner_url
            ? JSON.parse(item.last_fit_up_info.banner_url)[0]
            : '';
        } catch (e) {
          console.log(e);
        }

        // 初始化托管任务开关状态
        if (item.managementEnabled === undefined) {
          item.managementEnabled = true;
        }
      });
    }

    return response;
  };

  const getDataList = async () => {
    loading.value = true;
    request('/api/devices/accountList', {
      ...formModel,
      page: pagination.current,
      pageSize: pagination.pageSize,
    })
      .then((res) => {
        pagination.total = res.data.total;
        let data = res.data.data || [];
        data.forEach((item: any, index: number) => {
          try {
            [item.banner_url] = JSON.parse(item.banner_url) || [];
          } catch (e) {
            console.log(e);
          }
          try {
            [item.last_fit_up_info.banner_url] =
              JSON.parse(item.last_fit_up_info.banner_url) || [];
          } catch (e) {
            console.log(e);
          }

          // MOCK START - 模拟不同授权状态数据用于测试按钮显示效果
          // if (index === 0) {
          //   // 场景1：企业号未授权 - 应显示"去授权"按钮
          //   item.is_company_account = 1;
          //   item.auth_status = 0;
          //   item.app_auth_status = 0;
          //   console.log('模拟数据 - 企业号未授权:', item.account_name);
          // } else if (index === 1) {
          //   // 场景2：企业号已授权但聚光未授权 - 应显示"绑定聚光账号"按钮
          //   item.is_company_account = 1;
          //   item.auth_status = 1;
          //   item.app_auth_status = 0;
          //   console.log(
          //     '模拟数据 - 企业号已授权但聚光未授权:',
          //     item.account_name
          //   );
          // } else if (index === 2) {
          //   // 场景3：企业号全部授权完成 - 应显示"已授权"按钮
          //   item.is_company_account = 1;
          //   item.auth_status = 1;
          //   item.app_auth_status = 1;
          //   console.log('模拟数据 - 企业号全部授权完成:', item.account_name);
          // } else if (index === 3) {
          //   // 场景4：个人号聚光已授权 - 应显示"已授权"按钮
          //   item.is_company_account = 0;
          //   item.auth_status = 0;
          //   item.app_auth_status = 1;
          //   console.log('模拟数据 - 个人号聚光已授权:', item.account_name);
          // } else if (index === 4) {
          //   // 场景5：个人号聚光未授权 - 不显示任何授权相关按钮
          //   item.is_company_account = 0;
          //   item.auth_status = 0;
          //   item.app_auth_status = 0;
          //   console.log('模拟数据 - 个人号聚光未授权:', item.account_name);
          // }
          // MOCK END

          // 为演示目的，给前两个账号添加异常状态
          // if (index === 0) {
          //   item.account_errors =
          //     '发现【05】设备【xx】平台有【xxxxx】异常，请尽快确认。';
          // } else if (index === 1) {
          //   item.exception_info = {
          //     has_exception: true,
          //     device_count: '02',
          //     platform: '小红书',
          //     exception_type: '网络连接异常',
          //     exception_id: 'exc_002',
          //   };
          // }
        });
        accountList.value = data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const foldTreeRef = ref();
  const getTreeData = () => {
    foldTreeRef.value?.getDept();
  };

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    pagination.current = 1;
    Object.assign(formModel, resData);

    // 根据当前视图类型决定刷新方式
    if (currentView.value === 'table') {
      // 表格视图：触发 base-table 的搜索方法
      tableRef.value?.search();
    } else {
      // 卡片视图：使用原有的数据获取方法
      getDataList();
    }
  };
  handleSubmit();

  // 删除账号
  function delAccountAction(record: any) {
    record.okLoading = true;
    request('/api/devices/accountSave', {
      id: record.id,
      online_status: 'disable',
    })
      .then((res) => {
        Message.success('操作成功');
        handleSubmit();
      })
      .finally(() => {
        record.okLoading = false;
      });
  }

  // 刷新设备下账号
  function refreshAccount(item: any) {
    request('/api/devices/refreshAccount', {
      device_id: item.device_id,
    }).then((res) => {
      Message.success('任务提交成功, 请稍后查看');
      item.refresh_flag = 1;
    });
  }

  function selectDeviceAction(item: any) {
    if (formModel.device_ids.includes(item.device_id)) {
      formModel.device_ids = formModel.device_ids.filter(
        (id) => id !== item.device_id
      );
    } else {
      formModel.device_ids.push(item.device_id);
    }
    handleSubmit();
  }

  function getList() {
    if (isLogin()) {
      request('/api/devices/list', {}).then((res) => {
        list.value = res.data || [];
      });
    }
  }
  getList();
  let timer = setInterval(() => {
    getList();
  }, 1000 * 20);

  /**
   * 停止所有授权轮询
   */
  const stopAllAuthPolling = () => {
    authPollingMap.forEach((_, accountId) => {
      // eslint-disable-next-line no-use-before-define
      stopAuthPolling(accountId);
    });
  };

  onUnmounted(() => {
    clearInterval(timer);
    // 清理所有授权轮询定时器
    stopAllAuthPolling();
  });

  // 删除设备
  function delDevice(item: any) {
    item.okLoading = true;
    request('/api/devices/setStatus', {
      device_id: item.device_id,
      online_status: 'deleted',
    })
      .then((res) => {
        getList();
      })
      .finally(() => {
        item.okLoading = false;
      });
  }
  // 修改设备状态
  function disableDevice(item: any) {
    item.okLoading = true;
    request('/api/devices/openClose', {
      device_id: item.device_id,
      mode: item.mode === 'development' ? 'production' : 'development',
    })
      .then((res) => {
        getList();
      })
      .finally(() => {
        item.okLoading = false;
      });
  }

  // 解除账号异常状态
  function resolveException(item: any) {
    item.resolving_exception = true;
    request('/api/devices/accountErrorHandle', {
      account_errors_ids: item.account_errors_ids,
    })
      .then((res) => {
        Message.success('异常状态已解除');
        // 更新账号状态，移除异常信息
        if (item.account_errors) {
          item.account_errors = null;
        }
        // 可选：重新获取数据以确保状态同步
        // handleSubmit();
      })
      .catch((error) => {
        Message.error('解除异常失败，请重试');
        console.error('解除异常失败:', error);
      })
      .finally(() => {
        item.resolving_exception = false;
      });
  }

  // 显示设置蒙层
  function showSettingsOverlay(item: any) {
    // 先隐藏其他所有账号的设置蒙层
    accountList.value.forEach((account: any) => {
      if (account.id !== item.id) {
        account.showSettingsOverlay = false;
      }
    });

    // 显示当前账号的设置蒙层
    item.showSettingsOverlay = true;

    // 初始化托管任务开关状态（如果没有的话）
    if (item.managementEnabled === undefined) {
      item.managementEnabled = true; // 默认开启
    }
  }

  // 隐藏设置蒙层
  function hideSettingsOverlay(item: any) {
    item.showSettingsOverlay = false;
  }

  // 处理账号装修
  function handleAccountDecoration(item: any) {
    decorationRef.value?.show(item);
    hideSettingsOverlay(item);
  }

  // 处理配置分组
  function handleGroupConfig(item: any) {
    editRef.value?.show(item);
    hideSettingsOverlay(item);
  }

  // 处理账号备注
  function handleAccountBackup(item: any) {
    remarkRef.value?.show(item);
    hideSettingsOverlay(item);
  }

  // 处理托管任务开关
  function handleManagementToggle(record: any) {
    // 这里可以添加API调用来保存开关状态
    record.loading = true;
    request('/api/devices/accountSave', {
      id: record.id,
      usage_switch: record.usage_switch === 'active' ? 'inactive' : 'active',
    })
      .then(() => {
        record.usage_switch =
          record.usage_switch === 'active' ? 'inactive' : 'active';
      })
      .finally(() => {
        record.loading = false;
      });
  }

  // 处理自动陌拜开关
  function handleAutoSendMsgToggle(record: any) {
    record.loading = true;
    request('/api/devices/accountSave', {
      id: record.id,
      auto_send_msg_switch: record.auto_send_msg_switch === 0 ? 1 : 0,
    })
      .then(() => {
        record.auto_send_msg_switch = record.auto_send_msg_switch === 0 ? 1 : 0;
      })
      .finally(() => {
        record.loading = false;
      });
  }

  // 处理移除账号
  function handleRemoveAccount(item: any) {
    delAccountAction(item);
    hideSettingsOverlay(item);
  }

  // 处理备注保存成功
  function handleRemarkSuccess(data: { account_id: string; remark: string }) {
    // 更新本地数据中的备注信息
    const account = accountList.value.find(
      (item: any) => item.account_id === data.account_id
    );
    if (account) {
      account.remark = data.remark;
    }
  }

  /**
   * 获取单个账号的最新授权状态
   * @param accountId 账号ID
   * @returns Promise<any> 账号信息
   */
  const getAccountAuthStatus = async (accountId: string): Promise<any> => {
    try {
      // 使用现有的账号列表API，通过account_id筛选获取单个账号信息
      const response = await request('/api/devices/accountList', {
        account_id: accountId,
        page: 1,
        pageSize: 1,
      });

      if (response?.data?.data?.length > 0) {
        return response.data.data[0];
      }
      return null;
    } catch (error) {
      console.error('获取账号授权状态失败:', error);
      return null;
    }
  };

  /**
   * 启动授权状态轮询
   * @param accountId 账号ID
   * @param accountName 账号名称
   * @param checkCondition 检查授权完成的条件函数
   */
  const startAuthPolling = (
    accountId: string,
    accountName: string,
    checkCondition: (account: any) => boolean
  ) => {
    // 如果该账号已经在轮询中，先停止之前的轮询
    // eslint-disable-next-line no-use-before-define
    stopAuthPolling(accountId);

    const pollingState: AuthPollingState = {
      isPolling: true,
      timer: null,
      startTime: Date.now(),
      accountId,
    };

    // 设置账号为授权中状态
    const account = accountList.value.find(
      (acc: any) => acc.account_id === accountId
    );
    if (account) {
      account.isAuthorizing = true;
    }

    const pollFunction = async () => {
      try {
        // 检查是否超时（60秒）
        const elapsed = Date.now() - pollingState.startTime;
        if (elapsed > 60000) {
          // eslint-disable-next-line no-use-before-define
          stopAuthPolling(accountId);
          Message.warning(`账号【${accountName}】授权超时，请重试`);
          return;
        }

        // 获取最新的账号状态
        const latestAccount = await getAccountAuthStatus(accountId);
        if (latestAccount && checkCondition(latestAccount)) {
          // 授权成功，停止轮询
          // eslint-disable-next-line no-use-before-define
          stopAuthPolling(accountId);
          Message.success(`账号【${accountName}】授权成功！`);

          // 更新本地账号数据
          const localAccount = accountList.value.find(
            (acc: any) => acc.account_id === accountId
          );
          if (localAccount) {
            Object.assign(localAccount, latestAccount);
            localAccount.isAuthorizing = false;
          }
        }
      } catch (error) {
        console.error('轮询授权状态失败:', error);
      }
    };

    // 立即执行一次检查
    pollFunction();

    // 设置定时器，每秒检查一次
    pollingState.timer = setInterval(pollFunction, 1000);
    authPollingMap.set(accountId, pollingState);

    console.log(`🔄 开始轮询账号【${accountName}】的授权状态`);
  };

  /**
   * 停止授权状态轮询
   * @param accountId 账号ID
   */
  const stopAuthPolling = (accountId: string) => {
    const pollingState = authPollingMap.get(accountId);
    if (pollingState) {
      if (pollingState.timer) {
        clearInterval(pollingState.timer);
      }
      authPollingMap.delete(accountId);

      // 移除账号的授权中状态
      const account = accountList.value.find(
        (acc: any) => acc.account_id === accountId
      );
      if (account) {
        account.isAuthorizing = false;
      }

      console.log(`⏹️ 停止轮询账号【${accountId}】的授权状态`);
    }
  };

  // 处理授权操作
  async function handleAuthorization(item: any) {
    Message.info(`正在为账号【${item.account_name}】进行授权...`);
    // 通过接口/api/xhs_auth/getAuthUrl拿到跳转地址
    const res = await request('/api/xhs_auth/getAuthUrl', {
      user_id: item.xhs_user_id,
    });
    if (res.code !== 0) {
      Message.error(res.msg);
      return;
    }

    // 打开授权窗口（这里需要根据实际的授权URL进行调整）
    const authUrl = res.data.url;
    window.open(authUrl, '_blank', 'width=800,height=600');

    // 启动状态轮询，检查企业号授权状态
    startAuthPolling(item.account_id, item.account_name, (account: any) => {
      // 企业号授权完成的条件：auth_status 变为 1
      return account.is_company_account === 1 && account.auth_status === 1;
    });
  }

  // 处理绑定聚光账号操作
  async function handleBindSpotlight(item: any) {
    Message.info(`正在为账号【${item.account_name}】绑定聚光账号...`);

    // 通过接口/api/xhs_auth/getAuthUrl拿到跳转地址
    const res = await request('/api/xhs_auth/getAuthCode', {
      user_id: item.xhs_user_id,
    });
    if (res.code !== 0) {
      Message.error(res.msg);
      return;
    }

    // 打开授权窗口（这里需要根据实际的授权URL进行调整）
    const authUrl = res.data.url;
    window.open(authUrl, '_blank', 'width=800,height=600');

    // 启动状态轮询，检查聚光账号绑定状态
    startAuthPolling(item.account_id, item.account_name, (account: any) => {
      // 聚光账号绑定完成的条件：app_auth_status 变为 1
      return account.app_auth_status === 1;
    });
  }

  const dirApis = {
    list: '/api/material/dirList',
    save: '/api/material/dirSave',
  };

  const dirParams = computed(() => ({
    type: 'account',
  }));
  function dirChange(val: any, { node }: any) {
    formModel.dir_id = val;
    handleSubmit();
  }

  // 视图切换处理
  function handleViewChange(value: 'card' | 'table') {
    currentView.value = value;

    // 切换到表格视图时，触发表格数据加载
    if (value === 'table') {
      // 使用 nextTick 确保表格组件已经渲染
      nextTick(() => {
        tableRef.value?.search();
      });
    }

    console.log('视图切换到:', value);
  }

  // 分页处理方法
  function onPageChange(current: number) {
    pagination.current = current;

    // 根据当前视图类型决定刷新方式
    if (currentView.value === 'table') {
      // 表格视图：base-table 组件会自动处理分页
      // 这里不需要手动调用，因为分页是在 base-table 内部处理的
    } else {
      // 卡片视图：使用原有的数据获取方法
      getDataList();
    }
  }

  function onPageSizeChange(pageSize: number) {
    pagination.pageSize = pageSize;
    pagination.current = 1;

    // 根据当前视图类型决定刷新方式
    if (currentView.value === 'table') {
      // 表格视图：base-table 组件会自动处理分页
      // 这里不需要手动调用，因为分页是在 base-table 内部处理的
    } else {
      // 卡片视图：使用原有的数据获取方法
      getDataList();
    }
  }
</script>

<style scoped lang="less">
  .device-box {
    flex-shrink: 0;
    width: 275px;
    .title-line-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 4px;
    }
    .device-list {
      padding: 6px 10px 0 0;
    }
    .device-item {
      background: var(--color-bg-2);
      border: 1px solid var(--color-border-2);
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      padding: 12px 12px 6px;
      margin: 0 0 12px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 0 0 0;
        border-color: transparent transparent transparent transparent;
        transition: all 0.2s ease;
      }

      &.device-item-active {
        background: var(--color-bg-2);
        // border-color: rgb(var(--primary-6));
        box-shadow: 0 2px 12px rgba(var(--primary-6), 0.1);

        &::before {
          content: '已选择';
          position: absolute;
          left: -1px;
          top: 0;
          background: rgb(var(--primary-6));
          color: #fff;
          font-size: 10px;
          padding: 4px 8px;
          width: max-content;
          height: max-content;
          border-radius: 12px 0 12px 0;
          z-index: 2;
        }
      }

      &.device-item-offline {
        // background: var(--color-fill-2);
        border-color: var(--color-border-2);
        opacity: 0.9;

        .device-icon {
          opacity: 0.6;
        }

        .device-id {
          color: var(--color-text-3);
        }

        .status-offline {
          color: var(--color-text-3);
          .status-dot {
            background: var(--color-text-3);
          }
        }
      }

      &.device-item-development {
        background: var(--color-fill-1);
        border-color: var(--color-border-1);

        .status-development {
          color: var(--color-text-3);
          .status-dot {
            background: var(--color-text-3);
          }
        }
      }

      .device-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .device-main {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .device-title {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        flex: 1;
        min-width: 0;
      }

      .device-icon {
        width: 36px;
        height: 36px;
        flex-shrink: 0;
        transition: transform 0.3s ease;
      }

      .device-info {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 2px;
        margin-left: 5px;
      }

      .device-id-wrapper {
        min-width: 0;
        .device-id {
          font-size: 13px;
          font-weight: 500;
          color: var(--color-text-1);
          display: inline-block;
          align-items: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          // background: var(--color-fill-1);
          padding: 2px 8px;
          border-radius: var(--border-radius-large);
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
            monospace;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .device-status {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--color-text-2);

        .status-dot {
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: rgb(var(--success-6));
          margin-right: 4px;
          flex-shrink: 0;
        }

        .status-text {
          white-space: nowrap;
        }
      }

      .device-stats {
        display: flex;
        align-items: center;
        justify-content: space-around;
        background: var(--color-fill-1);
        border-radius: var(--border-radius-large);
        padding: 4px 0;
      }

      .stat-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 0;
      }

      .stat-value {
        font-size: 14px;
        font-weight: 600;
        color: var(--color-text-1);
        // line-height: 1.2;
      }

      .stat-label {
        font-size: 12px;
        color: var(--color-text-3);
        margin-top: 1px;
      }

      .device-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .action-group {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .action-btn {
        transition: all 0.2s ease;

        &.del-btn {
          opacity: 0;
        }

        &.switch-btn {
          transform: scale(0.85);
        }

        // &:hover {
        //   transform: scale(1.05);
        // }
      }

      .icon-spin {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .del-icon {
        position: absolute;
        right: 10px;
        top: 10px;
        opacity: 0;
        transition: all 0.2s ease;
        z-index: 2;

        &:hover {
          transform: scale(1.1);
        }
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

        .device-icon {
          transform: scale(1.05);
        }
        .del-btn {
          opacity: 1;
        }

        .del-icon {
          opacity: 1;
        }
      }
    }

    .device-add-box {
      margin: 0 10px 0 0;
    }
    .add-icon {
      cursor: pointer;
      border: 2px dashed var(--color-neutral-3);
      border-radius: var(--border-radius-large);
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      transition: all 0.3s ease;
      .plus-icon {
        color: var(--color-neutral-4);
      }
      &:hover {
        border: 2px dashed rgb(var(--primary-6));
        .plus-icon {
          color: rgb(var(--primary-6));
        }
      }
    }
  }

  .account-list-box {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
    width: 100%;
    padding-left: 10px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
    .account-item {
      position: relative;
      display: inline-flex;
      overflow: hidden;
      flex-direction: column;
      width: 100%;
      transition: all 0.3s ease;
      border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);

      .account-top {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        .account-bg {
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          object-fit: cover;
          width: 100%;
          height: 100%;
          display: block;
          border-radius: var(--border-radius-large);
        }
        &::after {
          content: '';
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.4) 50%,
            rgba(0, 0, 0, 0) 100%
          );
          pointer-events: none;
        }
        .account-top-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          z-index: 2;
          padding: 10px 10px;
          padding-bottom: 15px;

          .account-platform {
            width: 24px;
            height: 24px;
          }
          .online-status {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            padding: 0px 12px;
            border-radius: 25px;
            background: rgba(0, 0, 0, 0.35);
            font-weight: bold;
            :deep(.arco-badge-status-text) {
              display: flex;
              align-items: center;
              font-size: 12px;
              font-weight: 500;
              color: inherit;
            }

            // 授权中状态特殊样式
            .auth-status-badge {
              :deep(.arco-badge-status-text) {
                animation: auth-pulse 2s ease-in-out infinite;
              }

              :deep(.arco-badge-status-dot) {
                background: #1890ff;
                animation: auth-dot-pulse 1.5s ease-in-out infinite;
              }
            }
          }
        }

        .account-info {
          position: relative;
          z-index: 2;
          margin-bottom: 15px;
          :deep(.arco-typography) {
            color: #fff;
            margin: 0;
          }
          :deep(.arco-typography-operation-copy) {
            color: #fff;
            &:hover {
              background: rgba(255, 255, 255, 0.1);
            }
          }
          .account-avatar {
            // background: #fff;
            border: 2px solid rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            margin: 0 10px;
          }
          .account-info-content {
            font-size: 12px;
            flex: 1;
            padding-right: 15px;
            .account-info-content-box {
              margin-bottom: 3px;
              .ellipsis-1-line {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100%;
                display: inline-block;
                color: var(--color-bg-1);
                font-size: 16px;
                font-weight: 500;
                padding-right: 5px;
                max-width: 140px;
              }
            }
            .auth-status-btn-box {
              display: flex;
              align-items: center;
              gap: 4px;
              :deep(.arco-link) {
                font-size: 12px !important;
                &:hover {
                  background: transparent;
                }
              }
            }
          }
        }
        .account-info-box {
          display: flex;
          justify-content: space-between;
          z-index: 2;
          color: var(--color-bg-1);
          padding: 0 20px;
          .account-no {
            font-size: 14px;
            font-weight: 500;
            text-align: center;
          }
          .account-no-text {
            font-size: 12px;
            opacity: 0.55;
          }
        }
      }
      .account-bot {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px 12px;
        font-size: 12px;
        border: 1px solid var(--color-border-2);
        border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
        // background-color: var(--color-neutral-1);
        .account-des {
          white-space: pre-line;
          height: 42px;
          margin-top: 10px;
          flex-wrap: wrap;
          :deep(.arco-tag) {
            margin-bottom: 2px;
          }
        }
      }
      &:hover {
        box-shadow: 0 1px 8px var(--color-border-2);
      }
    }
  }

  .action-button-group {
    :deep(.arco-space-item) {
      margin-bottom: 0 !important;
    }

    // 授权状态按钮样式优化
    :deep(.arco-btn) {
      transition: all 0.3s ease;
      border-radius: var(--border-radius-medium);

      // 去授权按钮样式
      &.arco-btn-primary {
        background: rgb(var(--primary-6));
        border-color: rgb(var(--primary-6));

        &:hover {
          background: rgb(var(--primary-5));
          border-color: rgb(var(--primary-5));
          transform: translateY(-1px);
        }
      }

      // 绑定聚光账号按钮样式
      &.arco-btn-outline {
        border-color: rgb(var(--primary-6));
        color: rgb(var(--primary-6));

        &:hover {
          background: rgba(var(--primary-1), 0.8);
          transform: translateY(-1px);
        }
      }

      // 已授权按钮样式
      &.arco-btn-text[status='success'] {
        color: rgb(var(--success-6));
        background: rgba(var(--success-1), 0.5);

        .arco-icon {
          color: rgb(var(--success-6));
        }
      }
    }
  }

  .divider-line {
    border-color: var(--color-border-2);
  }
  .refresh-icon {
    color: rgb(var(--primary-6));
  }

  // 视图切换样式
  .view-switch-container {
    display: flex;
    justify-content: flex-end;
    margin: 0 0 10px 0;

    .view-switch-group {
      :deep(.arco-radio-button) {
        border-radius: var(--border-radius-medium);
        transition: all 0.3s ease;

        &:first-child {
          border-top-left-radius: var(--border-radius-medium);
          border-bottom-left-radius: var(--border-radius-medium);
        }

        &:last-child {
          border-top-right-radius: var(--border-radius-medium);
          border-bottom-right-radius: var(--border-radius-medium);
        }
      }
    }
  }

  // 表格视图样式
  .account-table-container {
    padding-left: 10px;
    .account-table {
      :deep(.arco-table) {
        border-radius: var(--border-radius-large);
        overflow: hidden;
      }

      :deep(.arco-table-th) {
        background: var(--color-fill-1);
        font-weight: 600;
        color: var(--color-text-1);
      }

      :deep(.arco-table-td) {
        border-bottom: 1px solid var(--color-border-1);
      }

      :deep(.arco-table-tr:hover .arco-table-td) {
        background: var(--color-fill-1);
      }
    }
  }

  // 表格单元格样式
  .platform-cell {
    display: flex;
    align-items: center;
    gap: 4px;

    .platform-icon {
      width: 20px;
      height: 20px;
    }

    .platform-name {
      font-size: 13px;
      font-weight: 500;
      text-transform: capitalize;
    }
  }

  .account-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;

    .account-avatar-wrapper {
      flex-shrink: 0;
    }

    .account-avatar-small {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid var(--color-border-1);
    }

    .account-details {
      flex: 1;
      min-width: 0;

      .account-name-row {
        display: flex;
        align-items: center;
        gap: 2px;
        margin-bottom: 4px;

        .account-name {
          font-size: 14px;
          font-weight: 600;
          color: var(--color-text-1);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .blue-v-icon-small {
          width: 14px;
          height: 14px;
          flex-shrink: 0;
        }
      }

      .account-id {
        font-size: 12px;
        color: var(--color-text-3);
        margin-bottom: 4px;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
          monospace;
      }

      .account-auth-status {
        display: flex;
        gap: 4px;
      }
    }
  }

  .content-stats-cell {
    .content-count-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      color: rgb(var(--primary-6));
      transition: all 0.3s ease;

      &:hover {
        background: rgba(var(--primary-1), 0.8);
        transform: translateY(-1px);
      }
    }
  }

  .data-stats-cell {
    .stats-row {
      display: flex;
      gap: 12px;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--color-text-2);

        .stat-icon {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
    }
  }

  .group-info-cell,
  .operators-cell {
    .group-name,
    .operators-text {
      font-size: 13px;
      color: var(--color-text-1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 120px;
      display: inline-block;
    }

    .no-group,
    .no-operators {
      font-size: 13px;
      color: var(--color-text-3);
      font-style: italic;
    }
  }

  // 备注列样式
  .remarks-cell {
    .remarks-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      max-width: 140px;
      padding: 4px 8px;
      border-radius: var(--border-radius-small);
      transition: all 0.3s ease;

      &:hover {
        background: var(--color-fill-2);
        transform: translateY(-1px);
      }

      .remarks-text {
        font-size: 13px;
        color: var(--color-text-1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      .no-remarks {
        font-size: 13px;
        color: var(--color-text-3);
        font-style: italic;
      }

      .arco-icon {
        font-size: 12px;
        color: var(--color-text-3);
        flex-shrink: 0;
      }
    }
  }

  .actions-cell {
    font-size: 12px;
    :deep(.arco-space-item) {
      margin-bottom: 0 !important;
    }
  }

  // 内容展示样式
  .account-content {
    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .content-count {
        font-size: 12px;
        color: var(--color-text-3);
      }

      .view-all-btn {
        font-size: 12px;
        color: var(--color-primary-6);
        padding: 0;
        height: auto;
      }
    }

    .content-list {
      .content-item {
        display: flex;
        gap: 8px;
        margin-bottom: 4px;
        border-radius: 6px;
        padding: 4px 0;
        cursor: pointer;
        &:hover {
          background: var(--color-fill-1);
        }

        .content-preview {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          overflow: hidden;
          flex-shrink: 0;

          .content-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .content-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--color-fill-2);
            color: var(--color-text-4);
          }
        }

        .content-info {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .content-title {
            font-size: 12px;
            color: var(--color-text-1);
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .content-stats {
            display: flex;
            gap: 8px;

            .stat-icon {
              display: flex;
              align-items: center;
              gap: 2px;
              font-size: 11px;
              color: var(--color-text-3);

              .arco-icon {
                font-size: 10px;
              }
            }
          }
        }
      }

      .view-more {
        text-align: center;
        padding: 8px 0;

        .view-more-text {
          font-size: 12px;
          color: var(--color-text-3);
          cursor: pointer;
        }
      }
    }

    .no-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 0;

      .no-content-icon {
        font-size: 32px;
        color: var(--color-text-4);
        margin-bottom: 8px;
      }

      .no-content-text {
        font-size: 12px;
        color: var(--color-text-4);
        margin-bottom: 4px;
      }

      .no-content-action {
        margin-top: 2px;
        .action-text {
          font-size: 10px;
          color: var(--color-text-4);
        }
      }
    }
  }

  .account-action {
    position: relative;
    // border-top: 1px solid var(--color-border-2);
    // margin-top: 10px;
    // padding-top: 10px;
  }

  .product-config-overlay {
    position: absolute;
    left: -12px;
    right: -12px;
    bottom: -10px;
    // background: rgba(var(--primary-1), 0.95);
    background: var(--color-bg-1);
    backdrop-filter: blur(4px);
    border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
    border-top: 1px solid rgba(var(--primary-6), 0.1);
    padding: 4px 12px 8px;
    height: 82px;

    .overlay-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      width: 100%;
    }

    .overlay-icon {
      font-size: 18px;
      color: rgb(var(--primary-6));
      opacity: 0.9;
    }

    .overlay-text {
      text-align: center;

      .overlay-title {
        font-size: 12px;
        font-weight: 500;
        color: rgb(var(--primary-6));
        margin-bottom: 1px;
      }

      .overlay-desc {
        font-size: 12px;
        color: var(--color-text-2);
        line-height: 1.2;
      }
    }

    .arco-btn {
      background: rgb(var(--primary-6));
      border-color: rgb(var(--primary-6));
      height: 24px;
      padding: 0 12px;
      font-size: 12px;

      &:hover {
        background: rgb(var(--primary-5));
        border-color: rgb(var(--primary-5));
      }
    }
  }

  .exception-overlay {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    border-radius: var(--border-radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    transition: all 0.3s ease;

    .exception-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      width: 100%;
      padding: 20px;
      text-align: center;
    }

    .exception-icon {
      font-size: 24px;
      color: #ff4d4f;
      opacity: 0.9;
    }

    .exception-text {
      color: #fff;

      .exception-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #fff;
      }

      .exception-desc {
        font-size: 12px;
        line-height: 1.4;
        color: rgba(255, 255, 255, 0.9);
        max-width: 180px;
      }
    }

    .arco-btn {
      background: #ff4d4f;
      border-color: #ff4d4f;
      height: 28px;
      padding: 0 16px;
      font-size: 12px;
      font-weight: 500;
      color: #fff;

      &:hover {
        background: #ff7875;
        border-color: #ff7875;
      }

      &:active {
        background: #d9363e;
        border-color: #d9363e;
      }

      &.arco-btn-loading {
        background: #ff7875;
        border-color: #ff7875;
      }
    }
  }

  .icon-decoration-swing {
    animation: decoration-swing 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    transform-origin: center;
  }

  @keyframes decoration-swing {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(-80deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  // 授权状态动画
  @keyframes auth-pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes auth-dot-pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
  }

  .blue-v-icon {
    width: 12px;
    height: 12px;
  }

  // 设置蒙层样式
  .settings-overlay {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(8px);
    border-radius: var(--border-radius-medium);
    z-index: 30;
    animation: settingsSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 1px 8px var(--color-border-2);

    .settings-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }

    .settings-header {
      margin-bottom: 20px;

      .back-button {
        color: var(--color-text-2);
        padding: 4px 8px;
        border-radius: var(--border-radius-medium);
        transition: all 0.2s ease;

        &:hover {
          background: var(--color-fill-2);
          color: var(--color-text-1);
        }

        .arco-icon {
          margin-right: 4px;
        }
      }
    }

    .settings-options {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .setting-option-btn {
        width: 100%;
        height: 40px;
      }

      .setting-switch-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--color-fill-1);
        border-radius: var(--border-radius-large);
        transition: all 0.2s ease;

        &:hover {
          background: var(--color-fill-3);
        }

        .switch-label {
          font-size: 13px;
        }
        .label-box {
          display: flex;
          flex-flow: column;
          gap: 4px;
          .label-desc {
            font-size: 10px;
            color: rgb(var(--warning-6));
          }
        }

        :deep(.arco-switch) {
          &.arco-switch-checked {
            background-color: rgb(var(--success-6));
          }
        }
      }
    }
  }

  // 设置蒙层动画
  @keyframes settingsSlideIn {
    0% {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  // 设置按钮样式优化
  .account-setting {
    color: var(--color-bg-1);
    font-size: 20px;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.2s ease;
    &:hover {
      opacity: 1;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.98);
    }

    .arco-icon {
      color: var(--color-bg-1);
      transition: color 0.2s ease;
    }
  }

  .icon-only-link {
    position: relative;
    .icon-link-text {
      opacity: 0;
      max-width: 0;
      margin-left: 0;
      transition: opacity 0.2s, max-width 0.2s, margin-left 0.2s;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
      vertical-align: middle;
    }
    &:hover .icon-link-text {
      opacity: 1;
      max-width: 80px;
      margin-left: 6px;
    }
  }
</style>

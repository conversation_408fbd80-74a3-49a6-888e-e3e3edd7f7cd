<!-- eslint-disable no-use-before-define -->
<template>
  <div class="content-box">
    <a-card class="view-box">
      <div class="df">
        <!-- 设备列表 -->
        <folder-tree-list
          ref="foldTreeRef"
          v-model:dir-id="formModel.dir_id"
          class="w-200"
          :apis="dirApis"
          :send-params="dirParams"
          :show-pop-menu="(nodeData:any) => nodeData.parent_id"
          :show-resource-count="true"
          whole-title="账号分组"
          @change="dirChange"
        ></folder-tree-list>
        <a-divider :margin="5" direction="vertical" class="divider-line" />
        <!-- 账号列表 -->
        <a-spin style="width: 100%" :loading="loading" tip="加载中...">
          <div style="flex: 1; min-width: 0">
            <search-form-fold
              :form-data="formModel"
              :get-default-form-data="generateFormModel"
              :continue-key="['device_ids']"
              class="no-top-padding-card"
              @search="handleSubmit()"
            >
              <template #formItemGroup>
                <a-form-item label="账号">
                  <a-input
                    v-model="formModel.account_name"
                    placeholder="请输入"
                    allow-clear
                  />
                </a-form-item>
                <a-form-item label="平台">
                  <platform-select
                    v-model="formModel.platform"
                  ></platform-select>
                </a-form-item>
                <a-form-item label="产品">
                  <request-select
                    v-model="formModel.product_id"
                    api="product"
                  />
                </a-form-item>
                <a-form-item label="托管状态">
                  <dict-select
                    v-model="formModel.online_status"
                    :data-list="accountStatusM"
                  />
                </a-form-item>
                <!-- 运营人员 -->
                <a-form-item label="运营人员">
                  <request-select
                    v-model="formModel.assign_user_ids"
                    multiple
                    api="allUser"
                  />
                </a-form-item>
                <!-- 平台 -->
                <a-form-item label="平台">
                  <a-input
                    v-model="formModel.ping"
                    placeholder="请输入"
                    allow-clear
                  />
                </a-form-item>
              </template>
            </search-form-fold>

            <!--table 区域-->
            <template v-if="pagination.total > 0 && !loading">
              <div class="account-list-box">
                <div
                  v-for="item in accountList"
                  :key="item.id"
                  class="account-item"
                >
                  <div class="account-top">
                    <img
                      v-if="item.banner_url"
                      class="account-bg"
                      :src="item.banner_url || ''"
                      alt=""
                    />
                    <img
                      v-else
                      class="account-bg"
                      src="@/assets/images/logo-h.png"
                      alt=""
                    />

                    <div class="df jc-sb account-top-info">
                      <a-space>
                        <img
                          class="account-platform"
                          :src="`icons/platform/${item.platform}.png`"
                          alt=""
                        />
                        <!-- 授权状态按钮 -->
                        <!-- 去授权按钮：企业号且未授权时显示 -->
                        <a-button
                          v-if="
                            item.is_company_account == 1 &&
                            item.auth_status == 0
                          "
                          size="mini"
                          status="warning"
                          :loading="item.isAuthorizing"
                          @click="handleAuthorization(item)"
                        >
                          <template #icon>
                            <icon-safe />
                          </template>
                          {{ item.isAuthorizing ? '授权中...' : '去授权' }}
                        </a-button>

                        <!-- 绑定聚光账号按钮：企业号已授权但聚光未授权时显示 -->
                        <a-button
                          v-if="
                            item.is_company_account == 1 &&
                            item.auth_status == 1 &&
                            item.app_auth_status == 0
                          "
                          size="mini"
                          status="success"
                          :loading="item.isAuthorizing"
                          @click="handleBindSpotlight(item)"
                        >
                          <template #icon>
                            <icon-link />
                          </template>
                          {{
                            item.isAuthorizing ? '绑定中...' : '绑定聚光账号'
                          }}
                        </a-button>

                        <!-- 已授权按钮：企业号全部授权完成或个人号聚光已授权时显示 -->
                        <a-button
                          v-if="
                            (item.is_company_account == 1 &&
                              item.auth_status == 1 &&
                              item.app_auth_status == 1) ||
                            (item.is_company_account != 1 &&
                              item.app_auth_status == 1)
                          "
                          type="text"
                          status="success"
                          size="mini"
                          class="auth-button"
                          disabled
                        >
                          <template #icon>
                            <icon-check-circle />
                          </template>
                          已授权
                        </a-button>

                        <a-popconfirm
                          :content="`确认删除账号【${item.account_name}】吗？`"
                          :ok-loading="!!item.okLoading"
                          @ok="delAccountAction(item)"
                        >
                          <a-button class="account-del" size="mini">
                            <template #icon>
                              <icon-delete class="del-red-color" />
                            </template>
                          </a-button>
                        </a-popconfirm>
                      </a-space>

                      <div class="online-status">
                        <!-- 授权中状态指示器 -->
                        <a-badge
                          v-if="item.isAuthorizing"
                          status="processing"
                          text="授权中..."
                          class="auth-status-badge"
                        />
                        <!-- 正常在线状态 -->
                        <a-badge
                          v-else
                          :style="{
                            color: getColor(accountStatusM, item.online_status),
                          }"
                          :color="getColor(accountStatusM, item.online_status)"
                          :text="getText(accountStatusM, item.online_status)"
                        >
                        </a-badge>
                      </div>
                    </div>
                    <div class="df ai-cen account-info">
                      <img
                        v-if="item.avatar_url"
                        class="account-avatar"
                        :src="item.avatar_url"
                        alt=""
                      />
                      <img
                        v-else
                        class="account-avatar"
                        src="@/assets/images/avatar.png"
                        alt=""
                      />

                      <div class="account-info-content">
                        <a-typography-title :heading="6">
                          {{ item.account_name }}
                          <!-- 是否为蓝V -->
                          <img
                            v-if="item.is_company_account == 1"
                            class="blue-v-icon"
                            src="@/assets/images/blue-v.png"
                            alt=""
                          />
                        </a-typography-title>
                        <a-typography-paragraph>
                          账号：{{ item.account_id }}
                        </a-typography-paragraph>
                      </div>
                    </div>
                  </div>
                  <div class="account-bot">
                    <div class="account-info-box" :fill="true" :size="12">
                      <a-space>
                        <a-space direction="vertical" align="center">
                          <div class="account-no">{{
                            item.like_count || '0'
                          }}</div>
                          <div>获赞</div>
                        </a-space>
                        <a-space direction="vertical" align="center">
                          <div class="account-no">
                            {{ item.mutual_follower_count || '0' }}
                          </div>
                          <div>互关</div>
                        </a-space>
                        <a-space direction="vertical" align="center">
                          <div class="account-no">
                            {{ item.following_count || '0' }}
                          </div>
                          <div>关注</div>
                        </a-space>
                        <a-space direction="vertical" align="center">
                          <div class="account-no">
                            {{ item.follower_count || '0' }}
                          </div>
                          <div>粉丝</div>
                        </a-space>
                      </a-space>
                      <a-space
                        direction="vertical"
                        class="action-button-group"
                        align="center"
                      >
                        <a-tooltip
                          v-if="item.sync_state == 2"
                          content="内容正在同步中..."
                          @click="toContent(item)"
                        >
                          <a-button type="text" size="mini">
                            <template #icon>
                              <icon-eye />
                            </template>
                            详情
                          </a-button>
                        </a-tooltip>
                        <a-button
                          v-else
                          type="text"
                          size="mini"
                          @click="contentRef?.show(item)"
                        >
                          <template #icon>
                            <icon-eye />
                          </template>
                          详情
                        </a-button>

                        <a-button
                          type="text"
                          size="mini"
                          @click="decorationRef?.show(item)"
                        >
                          <template #icon>
                            <icon-tool
                              :class="{
                                'icon-decoration-swing':
                                  item.fit_up_status === 1,
                              }"
                            />
                          </template>
                          {{ item.fit_up_status === 1 ? '装修中' : '装修' }}
                        </a-button>
                      </a-space>
                    </div>
                    <!-- <div class="account-des ellipsis2">
                      {{ item.introduction || '暂无' }}
                    </div>
                    <a-space class="account-des ellipsis2">
                      <a-tag
                        v-for="(citem, index) in getTags(item)"
                        :key="index"
                        size="mini"
                      >
                        {{ citem }}
                      </a-tag>
                    </a-space> -->
                    <!-- 内容展示区域 -->
                    <div class="account-content">
                      <!-- 已发布内容数量 -->
                      <div class="content-header">
                        <span class="content-count"
                          >已发布: {{ item.production_num || 0 }}</span
                        >
                        <!-- <a-button
                          type="text"
                          size="mini"
                          class="view-all-btn"
                          @click="contentRef?.show(item)"
                        >
                          查看详情 >
                        </a-button> -->
                      </div>

                      <!-- 内容列表 -->
                      <div
                        v-if="
                          item.latest_contents &&
                          item.latest_contents.length > 0
                        "
                        class="content-list"
                      >
                        <div
                          v-for="content in item.latest_contents.slice(0, 2)"
                          :key="content.id"
                          class="content-item"
                        >
                          <div class="content-preview">
                            <img
                              v-if="content.cover_url"
                              :src="content.cover_url"
                              class="content-cover"
                              alt=""
                            />
                            <div v-else class="content-placeholder">
                              <icon-image />
                            </div>
                          </div>
                          <div class="content-info">
                            <div class="content-title">{{
                              content.title ||
                              '内容标题内容标题内容标题内容标题内容标题......'
                            }}</div>
                            <div class="content-stats">
                              <span class="stat-icon">
                                <icon-heart />
                                {{ content.like_count || 5 }}
                              </span>
                              <span class="stat-icon">
                                <icon-star />
                                {{ content.star_count || 9 }}
                              </span>
                              <span class="stat-icon">
                                <icon-message />
                                {{ content.comment_count || 9 }}
                              </span>
                            </div>
                          </div>
                        </div>

                        <!-- 查看更多 -->
                        <div class="view-more">
                          <span class="view-more-text"
                            >共{{ item.production_num || 12 }}篇</span
                          >
                        </div>
                      </div>

                      <!-- 无内容状态 -->
                      <div v-else class="no-content">
                        <div class="no-content-icon">
                          <icon-file />
                        </div>
                        <div class="no-content-text">账号还未发布内容</div>
                        <div class="no-content-action">
                          <span class="action-text">快去发布第1篇内容吧~</span>
                        </div>
                      </div>
                    </div>
                    <div class="account-action">
                      <a-divider :margin="10" />
                      <div class="df ai-cen">
                        <span style="white-space: nowrap">
                          <icon-storage class="mr-5" />分组：
                        </span>
                        <a-tooltip
                          v-if="item.dir_name"
                          :content="item.dir_name"
                        >
                          <span class="ellipsis" style="flex: 1">
                            {{ item.dir_name || '-' }}
                          </span>
                        </a-tooltip>
                        <span v-else class="ellipsis" style="flex: 1"> - </span>
                        <a-button
                          type="text"
                          size="small"
                          @click="editRef?.show(item)"
                        >
                          <icon-edit />
                        </a-button>
                      </div>
                      <div class="df ai-cen mb-5">
                        <span style="white-space: nowrap">
                          <icon-user-group class="mr-5" />运营人员：
                        </span>
                        <a-tooltip
                          v-if="item.assign_user_names?.length"
                          :content="item.assign_user_names?.join('，')"
                        >
                          <span class="ellipsis" style="flex: 1">
                            {{ item.assign_user_names?.join('，') || '-' }}
                          </span>
                        </a-tooltip>
                        <span v-else class="ellipsis" style="flex: 1"> - </span>
                      </div>
                      <div class="mt-5">
                        <a-tooltip content="添加时间">
                          <icon-clock-circle class="mr-5" />
                        </a-tooltip>
                        <span>
                          {{ item.add_time || '-' }}
                        </span>
                      </div>
                      <!-- 产品配置提示蒙层 -->
                      <div
                        v-if="!item.product_names?.length"
                        class="product-config-overlay"
                      >
                        <div class="overlay-content">
                          <icon-tool class="overlay-icon" />
                          <div class="overlay-text">
                            <div class="overlay-title"
                              >请配置所属分组后使用</div
                            >
                          </div>
                          <a-button
                            type="primary"
                            size="mini"
                            @click="editRef?.show(item)"
                          >
                            立即配置
                          </a-button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 异常状态提示遮罩层 -->
                  <div v-if="item.account_errors" class="exception-overlay">
                    <div class="exception-content">
                      <icon-exclamation-circle
                        size="40"
                        class="exception-icon"
                      />
                      <div class="exception-text">
                        <div class="exception-title">当前账号异常！</div>
                        <div class="exception-desc">
                          {{ item.account_errors }}
                        </div>
                      </div>
                      <a-button
                        type="primary"
                        size="mini"
                        :loading="item.resolving_exception"
                        @click="resolveException(item)"
                      >
                        已处理
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="pagination.total > 0" class="df jc-sb mt-10">
                <span></span>
                <a-pagination
                  :current="pagination.current"
                  :page-size="pagination.pageSize"
                  :total="pagination.total"
                  show-total
                />
              </div>
            </template>
            <a-empty
              v-else-if="!loading"
              style="
                height: 60vh;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            />
          </div>
        </a-spin>
      </div>
    </a-card>
    <devices-edit
      ref="editRef"
      @refresh="
        handleSubmit();
        getTreeData();
      "
    ></devices-edit>
    <devices-add-modal ref="addRef"></devices-add-modal>
    <devices-account-decoration
      ref="decorationRef"
    ></devices-account-decoration>
    <account-content-modal ref="contentRef"></account-content-modal>
  </div>
</template>

<script setup lang="ts">
  import { onUnmounted, reactive, ref, computed } from 'vue';
  import request from '@/api/request';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import {
    deviceStatusM,
    accountStatusM,
  } from '@/components/dict-select/dict-account';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import DevicesEdit from '@/views/account/devices/devices-account-edit.vue';
  import DevicesAccountDecoration from '@/views/account/devices/devices-account-decoration.vue';
  import { Message } from '@arco-design/web-vue';
  import DevicesAddModal from '@/views/account/devices/devices-add-modal.vue';
  import { pageConfig } from '@/utils/table-utils/table-util';
  import dayjs from 'dayjs';
  import DeviceType from '@/views/account/devices/device-type.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import { colors } from '@/components/dict-select/dict-common';
  import { compact } from 'lodash';
  import { useRouter } from 'vue-router';
  import { isLogin } from '@/utils/auth';
  import AccountContentModal from '@/views/account/devices/account-content-modal.vue';
  import {
    IconFaceFrownFill,
    IconExclamationCircle,
    IconUnlock,
    IconLink,
    IconCheckCircle,
  } from '@arco-design/web-vue/es/icon';
  import FolderTreeList from '@/components/fold-tree/folder-tree-list.vue';
  import debug from '@/utils/env';

  // 授权轮询相关类型定义
  interface AuthPollingState {
    isPolling: boolean;
    timer: any | null;
    startTime: number;
    accountId: string;
  }

  // 授权轮询状态管理
  const authPollingMap = new Map<string, AuthPollingState>();

  const list = ref<any[]>([]);
  const loading = ref(false);
  const columns = [
    { title: '账号', dataIndex: 'account_id', width: 260 },
    { title: '平台', dataIndex: 'platform' },
    { title: '产品', dataIndex: 'product_names' },
    { title: '头图Banner', dataIndex: 'banner_url', width: 160 },
    { title: '简介', dataIndex: 'introduction' },
    { title: '在线状态', dataIndex: 'online_status' },
    { title: '添加时间', dataIndex: 'add_time' },
    { title: '操作', dataIndex: 'action', width: 120 },
  ];
  const generateFormModel = () => {
    return {
      dir_id: 0,
      device_id: '',
      device_ids: [] as number[],
      account_name: '',
      platform: '',
      product_id: '',
    };
  };
  const editRef = ref();
  const addRef = ref();
  const decorationRef = ref();
  const contentRef = ref();
  const formModel = reactive(generateFormModel());
  const accountList = ref([]);
  const pagination = reactive(pageConfig());

  function getTags(item: any) {
    return compact([
      item.gender,
      item.birthday,
      item.account_location,
      item.profession,
      item.school,
    ]);
  }

  const router = useRouter();
  function toDetail(item: any) {
    const target = router.resolve({
      name: 'account-content',
      query: {
        id: item.account_id,
      },
    });
    window.open(target.href, '__blank');
  }

  function toContent(item: any) {
    if (item.production_num) {
      contentRef.value?.show(item);
    } else {
      Message.warning('该账号没有数据');
    }
  }

  const getDataList = async () => {
    loading.value = true;
    request('/api/devices/accountList', {
      ...formModel,
      page: pagination.current,
      pageSize: pagination.pageSize,
    })
      .then((res) => {
        pagination.total = res.data.total;
        let data = res.data.data || [];
        data.forEach((item: any, index: number) => {
          try {
            [item.banner_url] = JSON.parse(item.banner_url) || [];
          } catch (e) {
            console.log(e);
          }
          try {
            [item.last_fit_up_info.banner_url] =
              JSON.parse(item.last_fit_up_info.banner_url) || [];
          } catch (e) {
            console.log(e);
          }

          // MOCK START - 模拟不同授权状态数据用于测试按钮显示效果
          // if (index === 0) {
          //   // 场景1：企业号未授权 - 应显示"去授权"按钮
          //   item.is_company_account = 1;
          //   item.auth_status = 0;
          //   item.app_auth_status = 0;
          //   console.log('模拟数据 - 企业号未授权:', item.account_name);
          // } else if (index === 1) {
          //   // 场景2：企业号已授权但聚光未授权 - 应显示"绑定聚光账号"按钮
          //   item.is_company_account = 1;
          //   item.auth_status = 1;
          //   item.app_auth_status = 0;
          //   console.log(
          //     '模拟数据 - 企业号已授权但聚光未授权:',
          //     item.account_name
          //   );
          // } else if (index === 2) {
          //   // 场景3：企业号全部授权完成 - 应显示"已授权"按钮
          //   item.is_company_account = 1;
          //   item.auth_status = 1;
          //   item.app_auth_status = 1;
          //   console.log('模拟数据 - 企业号全部授权完成:', item.account_name);
          // } else if (index === 3) {
          //   // 场景4：个人号聚光已授权 - 应显示"已授权"按钮
          //   item.is_company_account = 0;
          //   item.auth_status = 0;
          //   item.app_auth_status = 1;
          //   console.log('模拟数据 - 个人号聚光已授权:', item.account_name);
          // } else if (index === 4) {
          //   // 场景5：个人号聚光未授权 - 不显示任何授权相关按钮
          //   item.is_company_account = 0;
          //   item.auth_status = 0;
          //   item.app_auth_status = 0;
          //   console.log('模拟数据 - 个人号聚光未授权:', item.account_name);
          // }
          // MOCK END

          // 为演示目的，给前两个账号添加异常状态
          // if (index === 0) {
          //   item.account_errors =
          //     '发现【05】设备【xx】平台有【xxxxx】异常，请尽快确认。';
          // } else if (index === 1) {
          //   item.exception_info = {
          //     has_exception: true,
          //     device_count: '02',
          //     platform: '小红书',
          //     exception_type: '网络连接异常',
          //     exception_id: 'exc_002',
          //   };
          // }
        });
        accountList.value = data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const foldTreeRef = ref();
  const getTreeData = () => {
    foldTreeRef.value?.getDept();
  };

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    pagination.current = 1;
    Object.assign(formModel, resData);
    getDataList();
  };
  handleSubmit();

  // 删除账号
  function delAccountAction(record: any) {
    record.okLoading = true;
    request('/api/devices/accountSave', {
      id: record.id,
      online_status: 'disable',
    })
      .then((res) => {
        Message.success('操作成功');
        handleSubmit();
      })
      .finally(() => {
        record.okLoading = false;
      });
  }

  // 刷新设备下账号
  function refreshAccount(item: any) {
    request('/api/devices/refreshAccount', {
      device_id: item.device_id,
    }).then((res) => {
      Message.success('任务提交成功, 请稍后查看');
      item.refresh_flag = 1;
    });
  }

  function selectDeviceAction(item: any) {
    if (formModel.device_ids.includes(item.device_id)) {
      formModel.device_ids = formModel.device_ids.filter(
        (id) => id !== item.device_id
      );
    } else {
      formModel.device_ids.push(item.device_id);
    }
    handleSubmit();
  }

  function getList() {
    if (isLogin()) {
      request('/api/devices/list', {}).then((res) => {
        list.value = res.data || [];
      });
    }
  }
  getList();
  let timer = setInterval(() => {
    getList();
  }, 1000 * 20);

  /**
   * 停止所有授权轮询
   */
  const stopAllAuthPolling = () => {
    authPollingMap.forEach((_, accountId) => {
      // eslint-disable-next-line no-use-before-define
      stopAuthPolling(accountId);
    });
  };

  onUnmounted(() => {
    clearInterval(timer);
    // 清理所有授权轮询定时器
    stopAllAuthPolling();
  });

  // 删除设备
  function delDevice(item: any) {
    item.okLoading = true;
    request('/api/devices/setStatus', {
      device_id: item.device_id,
      online_status: 'deleted',
    })
      .then((res) => {
        getList();
      })
      .finally(() => {
        item.okLoading = false;
      });
  }
  // 修改设备状态
  function disableDevice(item: any) {
    item.okLoading = true;
    request('/api/devices/openClose', {
      device_id: item.device_id,
      mode: item.mode === 'development' ? 'production' : 'development',
    })
      .then((res) => {
        getList();
      })
      .finally(() => {
        item.okLoading = false;
      });
  }

  // 解除账号异常状态
  function resolveException(item: any) {
    item.resolving_exception = true;
    request('/api/devices/accountErrorHandle', {
      account_errors_ids: item.account_errors_ids,
    })
      .then((res) => {
        Message.success('异常状态已解除');
        // 更新账号状态，移除异常信息
        if (item.account_errors) {
          item.account_errors = null;
        }
        // 可选：重新获取数据以确保状态同步
        // handleSubmit();
      })
      .catch((error) => {
        Message.error('解除异常失败，请重试');
        console.error('解除异常失败:', error);
      })
      .finally(() => {
        item.resolving_exception = false;
      });
  }

  /**
   * 获取单个账号的最新授权状态
   * @param accountId 账号ID
   * @returns Promise<any> 账号信息
   */
  const getAccountAuthStatus = async (accountId: string): Promise<any> => {
    try {
      // 使用现有的账号列表API，通过account_id筛选获取单个账号信息
      const response = await request('/api/devices/accountList', {
        account_id: accountId,
        page: 1,
        pageSize: 1,
      });

      if (response?.data?.data?.length > 0) {
        return response.data.data[0];
      }
      return null;
    } catch (error) {
      console.error('获取账号授权状态失败:', error);
      return null;
    }
  };

  /**
   * 启动授权状态轮询
   * @param accountId 账号ID
   * @param accountName 账号名称
   * @param checkCondition 检查授权完成的条件函数
   */
  const startAuthPolling = (
    accountId: string,
    accountName: string,
    checkCondition: (account: any) => boolean
  ) => {
    // 如果该账号已经在轮询中，先停止之前的轮询
    // eslint-disable-next-line no-use-before-define
    stopAuthPolling(accountId);

    const pollingState: AuthPollingState = {
      isPolling: true,
      timer: null,
      startTime: Date.now(),
      accountId,
    };

    // 设置账号为授权中状态
    const account = accountList.value.find(
      (acc: any) => acc.account_id === accountId
    );
    if (account) {
      account.isAuthorizing = true;
    }

    const pollFunction = async () => {
      try {
        // 检查是否超时（60秒）
        const elapsed = Date.now() - pollingState.startTime;
        if (elapsed > 60000) {
          // eslint-disable-next-line no-use-before-define
          stopAuthPolling(accountId);
          Message.warning(`账号【${accountName}】授权超时，请重试`);
          return;
        }

        // 获取最新的账号状态
        const latestAccount = await getAccountAuthStatus(accountId);
        if (latestAccount && checkCondition(latestAccount)) {
          // 授权成功，停止轮询
          // eslint-disable-next-line no-use-before-define
          stopAuthPolling(accountId);
          Message.success(`账号【${accountName}】授权成功！`);

          // 更新本地账号数据
          const localAccount = accountList.value.find(
            (acc: any) => acc.account_id === accountId
          );
          if (localAccount) {
            Object.assign(localAccount, latestAccount);
            localAccount.isAuthorizing = false;
          }
        }
      } catch (error) {
        console.error('轮询授权状态失败:', error);
      }
    };

    // 立即执行一次检查
    pollFunction();

    // 设置定时器，每秒检查一次
    pollingState.timer = setInterval(pollFunction, 1000);
    authPollingMap.set(accountId, pollingState);

    console.log(`🔄 开始轮询账号【${accountName}】的授权状态`);
  };

  /**
   * 停止授权状态轮询
   * @param accountId 账号ID
   */
  const stopAuthPolling = (accountId: string) => {
    const pollingState = authPollingMap.get(accountId);
    if (pollingState) {
      if (pollingState.timer) {
        clearInterval(pollingState.timer);
      }
      authPollingMap.delete(accountId);

      // 移除账号的授权中状态
      const account = accountList.value.find(
        (acc: any) => acc.account_id === accountId
      );
      if (account) {
        account.isAuthorizing = false;
      }

      console.log(`⏹️ 停止轮询账号【${accountId}】的授权状态`);
    }
  };

  // 处理授权操作
  async function handleAuthorization(item: any) {
    Message.info(`正在为账号【${item.account_name}】进行授权...`);
    // 通过接口/api/xhs_auth/getAuthUrl拿到跳转地址
    const res = await request('/api/xhs_auth/getAuthUrl', {
      user_id: item.xhs_user_id,
    });
    if (res.code !== 0) {
      Message.error(res.msg);
      return;
    }

    // 打开授权窗口（这里需要根据实际的授权URL进行调整）
    const authUrl = res.data.url;
    window.open(authUrl, '_blank', 'width=800,height=600');

    // 启动状态轮询，检查企业号授权状态
    startAuthPolling(item.account_id, item.account_name, (account: any) => {
      // 企业号授权完成的条件：auth_status 变为 1
      return account.is_company_account === 1 && account.auth_status === 1;
    });
  }

  // 处理绑定聚光账号操作
  async function handleBindSpotlight(item: any) {
    Message.info(`正在为账号【${item.account_name}】绑定聚光账号...`);

    // 通过接口/api/xhs_auth/getAuthUrl拿到跳转地址
    const res = await request('/api/xhs_auth/getAuthCode', {
      user_id: item.xhs_user_id,
    });
    if (res.code !== 0) {
      Message.error(res.msg);
      return;
    }

    // 打开授权窗口（这里需要根据实际的授权URL进行调整）
    const authUrl = res.data.url;
    window.open(authUrl, '_blank', 'width=800,height=600');

    // 启动状态轮询，检查聚光账号绑定状态
    startAuthPolling(item.account_id, item.account_name, (account: any) => {
      // 聚光账号绑定完成的条件：app_auth_status 变为 1
      return account.app_auth_status === 1;
    });
  }

  const dirApis = {
    list: '/api/material/dirList',
    save: '/api/material/dirSave',
  };

  const dirParams = computed(() => ({
    type: 'account',
  }));
  function dirChange(val: any, { node }: any) {
    formModel.dir_id = val;
    handleSubmit();
  }
</script>

<style scoped lang="less">
  .device-box {
    flex-shrink: 0;
    width: 275px;
    .title-line-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 4px;
    }
    .device-list {
      padding: 6px 10px 0 0;
    }
    .device-item {
      background: var(--color-bg-2);
      border: 1px solid var(--color-border-2);
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      padding: 12px 12px 6px;
      margin: 0 0 12px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 0 0 0;
        border-color: transparent transparent transparent transparent;
        transition: all 0.2s ease;
      }

      &.device-item-active {
        background: var(--color-bg-2);
        // border-color: rgb(var(--primary-6));
        box-shadow: 0 2px 12px rgba(var(--primary-6), 0.1);

        &::before {
          content: '已选择';
          position: absolute;
          left: -1px;
          top: 0;
          background: rgb(var(--primary-6));
          color: #fff;
          font-size: 10px;
          padding: 4px 8px;
          width: max-content;
          height: max-content;
          border-radius: 12px 0 12px 0;
          z-index: 2;
        }
      }

      &.device-item-offline {
        // background: var(--color-fill-2);
        border-color: var(--color-border-2);
        opacity: 0.9;

        .device-icon {
          opacity: 0.6;
        }

        .device-id {
          color: var(--color-text-3);
        }

        .status-offline {
          color: var(--color-text-3);
          .status-dot {
            background: var(--color-text-3);
          }
        }
      }

      &.device-item-development {
        background: var(--color-fill-1);
        border-color: var(--color-border-1);

        .status-development {
          color: var(--color-text-3);
          .status-dot {
            background: var(--color-text-3);
          }
        }
      }

      .device-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .device-main {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .device-title {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        flex: 1;
        min-width: 0;
      }

      .device-icon {
        width: 36px;
        height: 36px;
        flex-shrink: 0;
        transition: transform 0.3s ease;
      }

      .device-info {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 2px;
        margin-left: 5px;
      }

      .device-id-wrapper {
        min-width: 0;
        .device-id {
          font-size: 13px;
          font-weight: 500;
          color: var(--color-text-1);
          display: inline-block;
          align-items: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          // background: var(--color-fill-1);
          padding: 2px 8px;
          border-radius: var(--border-radius-large);
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
            monospace;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .device-status {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--color-text-2);

        .status-dot {
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: rgb(var(--success-6));
          margin-right: 4px;
          flex-shrink: 0;
        }

        .status-text {
          white-space: nowrap;
        }
      }

      .device-stats {
        display: flex;
        align-items: center;
        justify-content: space-around;
        background: var(--color-fill-1);
        border-radius: var(--border-radius-large);
        padding: 4px 0;
      }

      .stat-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 0;
      }

      .stat-value {
        font-size: 14px;
        font-weight: 600;
        color: var(--color-text-1);
        // line-height: 1.2;
      }

      .stat-label {
        font-size: 12px;
        color: var(--color-text-3);
        margin-top: 1px;
      }

      .device-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .action-group {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .action-btn {
        transition: all 0.2s ease;

        &.del-btn {
          opacity: 0;
        }

        &.switch-btn {
          transform: scale(0.85);
        }

        // &:hover {
        //   transform: scale(1.05);
        // }
      }

      .icon-spin {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .del-icon {
        position: absolute;
        right: 10px;
        top: 10px;
        opacity: 0;
        transition: all 0.2s ease;
        z-index: 2;

        &:hover {
          transform: scale(1.1);
        }
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

        .device-icon {
          transform: scale(1.05);
        }
        .del-btn {
          opacity: 1;
        }

        .del-icon {
          opacity: 1;
        }
      }
    }

    .device-add-box {
      margin: 0 10px 0 0;
    }
    .add-icon {
      cursor: pointer;
      border: 2px dashed var(--color-neutral-3);
      border-radius: var(--border-radius-large);
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      transition: all 0.3s ease;
      .plus-icon {
        color: var(--color-neutral-4);
      }
      &:hover {
        border: 2px dashed rgb(var(--primary-6));
        .plus-icon {
          color: rgb(var(--primary-6));
        }
      }
    }
  }

  .account-list-box {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
    width: 100%;
    padding-left: 10px;
    .account-info-box {
      display: flex;
      justify-content: space-between;
    }
    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
    .account-item {
      position: relative;
      display: inline-flex;
      border-radius: var(--border-radius-large);
      overflow: hidden;
      flex-direction: column;
      width: 100%;
      transition: all 0.3s ease;

      .account-top {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        .account-bg {
          position: absolute;
          z-index: 1;
          inset: 0;
          object-fit: cover;
          width: 100%;
          height: 100%;
          display: block;
        }
        &::after {
          content: '';
          position: absolute;
          z-index: 1;
          inset: 0;
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.4) 50%,
            rgba(0, 0, 0, 0) 100%
          );
          pointer-events: none;
        }
        .account-top-info {
          // position: relative;
          z-index: 2;
          padding: 10px 10px;
          padding-bottom: 10px;
          .account-platform {
            width: 28px;
            height: 28px;
          }
          .online-status {
            position: absolute;
            right: 0;
            bottom: 0;
            // padding: 2px 6px 0 10px;
            padding: 1px 5px 0 6px;
            border-radius: var(--border-radius-large) 0 0 0;
            // backdrop-filter: blur(20px);
            background: rgba(0, 0, 0, 0.4);
            // font-weight: bold;
            :deep(.arco-badge-status-text) {
              display: flex;
              align-items: center;
              color: inherit;
              font-size: 12px;
              font-weight: 500;
              margin-left: 3px;
            }

            // 授权中状态特殊样式
            .auth-status-badge {
              :deep(.arco-badge-status-text) {
                color: #fff;
                animation: auth-pulse 2s ease-in-out infinite;
              }

              :deep(.arco-badge-status-dot) {
                background: #1890ff;
                animation: auth-dot-pulse 1.5s ease-in-out infinite;
              }
            }
          }
          .account-del {
            position: absolute;
            right: 10px;
            top: 10px;
            transition: all 0.3s ease;
            opacity: 0;
            &:hover {
              transform: scale(1.1);
            }
          }

          // 顶部区域授权按钮样式
          .auth-button {
            transition: all 0.3s ease;
            backdrop-filter: blur(4px);
            border-radius: var(--border-radius-medium);

            // 去授权按钮在深色背景上的样式
            &.arco-btn-primary {
              background: rgba(var(--primary-6), 0.9);
              border-color: rgba(var(--primary-6), 0.9);
              color: #fff;

              &:hover {
                background: rgba(var(--primary-5), 0.95);
                border-color: rgba(var(--primary-5), 0.95);
                transform: translateY(-1px);
              }
            }

            // 绑定聚光账号按钮在深色背景上的样式
            &.arco-btn-outline {
              background: rgba(255, 255, 255, 0.9);
              border-color: rgba(var(--primary-6), 0.8);
              color: rgb(var(--primary-6));

              &:hover {
                background: rgba(255, 255, 255, 0.95);
                border-color: rgb(var(--primary-6));
                transform: translateY(-1px);
              }
            }

            // 已授权按钮在深色背景上的样式
            &.arco-btn-text[status='success'] {
              background: rgba(var(--success-1), 0.9);
              color: rgb(var(--success-6));

              .arco-icon {
                color: rgb(var(--success-6));
              }
            }
          }
          :deep(.arco-popconfirm) {
            .account-del {
              opacity: 1;
            }
          }
        }

        .account-info {
          position: relative;
          z-index: 2;
          margin-bottom: 10px;
          :deep(.arco-typography) {
            color: #fff;
            margin: 0;
          }
          :deep(.arco-typography-operation-copy) {
            color: #fff;
            &:hover {
              background: rgba(255, 255, 255, 0.3);
            }
          }
          .account-avatar {
            background: #fff;
            border: 3px solid #fff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            margin: 0 10px;
          }
          .account-info-content {
            font-size: 12px;
          }
        }
      }
      .account-bot {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10px 12px;
        font-size: 12px;
        border: 1px solid var(--color-border-2);
        border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
        // background-color: var(--color-neutral-1);
        .account-des {
          white-space: pre-line;
          height: 42px;
          margin-top: 10px;
          flex-wrap: wrap;
          :deep(.arco-tag) {
            margin-bottom: 2px;
          }
        }
        .account-no {
          color: #000;
          font-size: 15px;
          margin-bottom: -6px;
          font-weight: 500;
          min-width: 24px;
          text-align: center;
        }
      }
      &:hover {
        box-shadow: 0 1px 8px var(--color-border-2);
        .account-top {
          .account-del {
            opacity: 1;
          }
        }
      }
    }
  }

  .action-button-group {
    :deep(.arco-space-item) {
      margin-bottom: 0 !important;
    }

    // 授权状态按钮样式优化
    :deep(.arco-btn) {
      transition: all 0.3s ease;
      border-radius: var(--border-radius-medium);

      // 去授权按钮样式
      &.arco-btn-primary {
        background: rgb(var(--primary-6));
        border-color: rgb(var(--primary-6));

        &:hover {
          background: rgb(var(--primary-5));
          border-color: rgb(var(--primary-5));
          transform: translateY(-1px);
        }
      }

      // 绑定聚光账号按钮样式
      &.arco-btn-outline {
        border-color: rgb(var(--primary-6));
        color: rgb(var(--primary-6));

        &:hover {
          background: rgba(var(--primary-1), 0.8);
          transform: translateY(-1px);
        }
      }

      // 已授权按钮样式
      &.arco-btn-text[status='success'] {
        color: rgb(var(--success-6));
        background: rgba(var(--success-1), 0.5);

        .arco-icon {
          color: rgb(var(--success-6));
        }
      }
    }
  }

  .divider-line {
    border-color: var(--color-border-2);
  }
  .refresh-icon {
    color: rgb(var(--primary-6));
  }

  // 内容展示样式
  .account-content {
    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;

      .content-count {
        font-size: 12px;
        color: var(--color-text-3);
      }

      .view-all-btn {
        font-size: 12px;
        color: var(--color-primary-6);
        padding: 0;
        height: auto;
      }
    }

    .content-list {
      .content-item {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        padding: 8px;
        border-radius: 6px;
        background: var(--color-fill-1);

        .content-preview {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          overflow: hidden;
          flex-shrink: 0;

          .content-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .content-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--color-fill-2);
            color: var(--color-text-4);
          }
        }

        .content-info {
          flex: 1;
          min-width: 0;

          .content-title {
            font-size: 12px;
            color: var(--color-text-1);
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .content-stats {
            display: flex;
            gap: 8px;

            .stat-icon {
              display: flex;
              align-items: center;
              gap: 2px;
              font-size: 11px;
              color: var(--color-text-3);

              .arco-icon {
                font-size: 10px;
              }
            }
          }
        }
      }

      .view-more {
        text-align: center;
        padding: 8px 0 0;

        .view-more-text {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
    }

    .no-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 0;

      .no-content-icon {
        font-size: 32px;
        color: var(--color-text-4);
        margin-bottom: 8px;
      }

      .no-content-text {
        font-size: 12px;
        color: var(--color-text-4);
        margin-bottom: 4px;
      }

      .no-content-action {
        margin-top: 2px;
        .action-text {
          font-size: 10px;
          color: var(--color-text-4);
        }
      }
    }
  }

  .account-action {
    position: relative;
    // border-top: 1px solid var(--color-border-2);
    // margin-top: 10px;
    // padding-top: 10px;
  }

  .product-config-overlay {
    position: absolute;
    left: -12px;
    right: -12px;
    bottom: -10px;
    background: rgba(var(--primary-1), 0.95);
    backdrop-filter: blur(4px);
    border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
    border-top: 1px solid rgba(var(--primary-6), 0.1);
    padding: 4px 12px 8px;
    height: 82px;

    .overlay-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      width: 100%;
    }

    .overlay-icon {
      font-size: 18px;
      color: rgb(var(--primary-6));
      opacity: 0.9;
    }

    .overlay-text {
      text-align: center;

      .overlay-title {
        font-size: 12px;
        font-weight: 500;
        color: rgb(var(--primary-6));
        margin-bottom: 1px;
      }

      .overlay-desc {
        font-size: 12px;
        color: var(--color-text-2);
        line-height: 1.2;
      }
    }

    .arco-btn {
      background: rgb(var(--primary-6));
      border-color: rgb(var(--primary-6));
      height: 24px;
      padding: 0 12px;
      font-size: 12px;

      &:hover {
        background: rgb(var(--primary-5));
        border-color: rgb(var(--primary-5));
      }
    }
  }

  .exception-overlay {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    border-radius: var(--border-radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    transition: all 0.3s ease;

    .exception-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      width: 100%;
      padding: 20px;
      text-align: center;
    }

    .exception-icon {
      font-size: 24px;
      color: #ff4d4f;
      opacity: 0.9;
    }

    .exception-text {
      color: #fff;

      .exception-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #fff;
      }

      .exception-desc {
        font-size: 12px;
        line-height: 1.4;
        color: rgba(255, 255, 255, 0.9);
        max-width: 180px;
      }
    }

    .arco-btn {
      background: #ff4d4f;
      border-color: #ff4d4f;
      height: 28px;
      padding: 0 16px;
      font-size: 12px;
      font-weight: 500;
      color: #fff;

      &:hover {
        background: #ff7875;
        border-color: #ff7875;
      }

      &:active {
        background: #d9363e;
        border-color: #d9363e;
      }

      &.arco-btn-loading {
        background: #ff7875;
        border-color: #ff7875;
      }
    }
  }

  .icon-decoration-swing {
    animation: decoration-swing 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    transform-origin: center;
  }

  @keyframes decoration-swing {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(-80deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  // 授权状态动画
  @keyframes auth-pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes auth-dot-pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
  }

  .blue-v-icon {
    width: 12px;
  }
</style>

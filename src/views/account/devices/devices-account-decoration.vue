<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`账号装修【${formModel.account_id}】`"
    @cancel="handleCancel"
  >
    <a-form
      v-if="visible"
      ref="formRef"
      :model="formModel"
      :auto-label-width="true"
    >
      <a-form-item label="账号昵称" field="account_name" :rules="requiredRule">
        <a-input
          v-model="formModel.account_name"
          :max-length="24"
          show-word-limit
          allow-clear
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="头像" field="avatar_url" :rules="requiredUploadRule">
        <upload-file
          v-model="formModel.avatar_url"
          file-type="image/png,image/jpg,image/jpeg"
          :limit="1"
          :multiple="false"
          :send-params="{ type: 'account_avatar' }"
          @success="
            (fileItem:any) => {
              formModel.avatar = fileItem.response?.data?.url;
            }
          "
        />
      </a-form-item>
      <a-form-item label="简介">
        <a-textarea
          v-model="formModel.introduction"
          :auto-size="{ minRows: 4, maxRows: 8 }"
          :max-length="100"
          show-word-limit
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="头图Banner">
        <upload-file
          v-model="formModel.banner_url"
          file-type="image/png,image/jpg,image/jpeg"
          :limit="1"
          :multiple="false"
          :send-params="{ type: 'account_banner' }"
          @success="
            (fileItem:any) => {
              formModel.banner = fileItem.response?.data?.url;
            }
          "
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredUploadRule } from '@/utils/util';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import RequestSelect from '@/components/select/request-select.vue';
  import UploadFile from '@/components/upload-file/upload-file.vue';

  const defaultForm = () => ({
    id: null,
    account_id: null,
    account_name: null,
    avatar: '',
    avatar_url: '',
    banner: '',
    banner_url: '',
    introduction: null,
  });

  const emit = defineEmits(['refresh']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      let fitInfo = dinfo.last_fit_up_info || {};
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          fitInfo[key] || initForm[key as keyof typeof initForm];
      });
      formModel.value.id = dinfo.id;
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/devices/fitmentAccount', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('任务发起成功，请稍后查看任务结果');
          handleCancel();
          emit('refresh');
        })
        .catch(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

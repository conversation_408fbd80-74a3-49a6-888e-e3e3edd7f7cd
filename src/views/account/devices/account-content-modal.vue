<template>
  <d-modal
    v-model:visible="visible"
    title="账号已发布内容"
    :footer="false"
    width="1200px"
    unmount-on-close
    :body-style="{ height: '85vh', padding: '16px 20px' }"
  >
    <div
      class="df"
      style="
        height: 100%;
        border-radius: 12px;
        overflow: hidden;
        background: var(--color-bg-2);
      "
    >
      <div class="video-list-box">
        <a-spin class="w100p" style="height: 100%" :loading="loading">
          <div class="df fd-cl ai-cen" style="height: 100%">
            <div class="header-box">
              <a-input-search
                v-model="formModel.title"
                class="w100p mt-10 mb-5"
                placeholder="请输入内容关键词"
                @search="getList()"
                @keydown.enter="getList()"
              />
              <dict-radio
                v-model="formModel.order_field"
                :data-list="sortType"
                :show-icon="false"
                :max-width="true"
                @change="getList()"
              />
            </div>
            <div class="video-list">
              <div
                v-for="item in list"
                :key="item.media_video_id"
                :data-media-video-id="item.media_video_id"
                :class="{
                  'video-item': true,
                  'video-item-active':
                    currentVideo.media_video_id === item.media_video_id,
                }"
                @click="selectVideo(item)"
              >
                <div class="video-cover">
                  <img class="mengban" :src="item.cover_url" alt="" />
                  <a-image
                    width="100%"
                    height="100%"
                    fit="contain"
                    :src="item.cover_url"
                    :preview="false"
                  >
                  </a-image>
                </div>
                <div class="video-info">
                  <div class="video-title ellipsis" :title="item.title">{{
                    item.title
                  }}</div>
                  <div class="video-time">
                    <icon-clock-circle size="12" />
                    {{ item.create_time }}</div
                  >
                  <div class="jc ai-cen jc-sb mt-3 icon-box">
                    <!-- <div>
                      <icon-play-circle size="14" />
                      <span>{{ item.play_count }}</span>
                    </div> -->
                    <div>
                      <icon-heart size="14" />
                      <span>{{ item.like_count }}</span>
                    </div>
                    <div>
                      <icon-message size="14" />
                      <span>{{ item.comment_count }}</span>
                    </div>
                    <div>
                      <icon-star size="14" />
                      <span>{{ item.favorite_count }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
      <div class="video-detail">
        <div class="video-preview">
          <template v-if="currentVideo.local_download_url?.length">
            <template v-if="currentVideo.content_type === 'video'">
              <video
                v-if="visible"
                class="preview-item"
                style="object-fit: contain"
                :src="currentVideo.local_download_url[0]"
                :poster="currentVideo.cover_url"
                controls
                autoplay
                preload="none"
              />
            </template>
            <template v-else>
              <a-carousel
                class="preview-item"
                indicator-type="line"
                style="height: 100%"
                :auto-play="false"
                p
              >
                <a-carousel-item
                  v-for="(image, index) in currentVideo.local_download_url"
                  :key="index"
                  style="height: 100%"
                >
                  <a-image
                    width="100%"
                    height="100%"
                    fit="contain"
                    :src="image"
                    :preview="false"
                  ></a-image>
                </a-carousel-item>
              </a-carousel>
            </template>
          </template>
          <div v-else class="preview-box" @click="previewInNewWindow()">
            <img
              height="100%"
              class="preview-item img-item"
              :preview="false"
              fit="contain"
              :src="currentVideo.cover_url"
            />
            <div class="preview-icon">
              <icon-play-circle-fill
                v-if="currentVideo.content_type === 'video'"
                style="display: block"
              />
              <icon-search v-else style="display: block" />
            </div>
          </div>
        </div>
        <div class="comment-list">
          <account-info :info="info" />
          <a-typography-title :heading="6" style="margin: 16px 0 8px">
            {{ currentVideo.title }}
          </a-typography-title>

          <!-- 内容区域 -->
          <div v-if="currentVideo.desc" class="content-box">
            <div class="content-item-content">{{ currentVideo.desc }}</div>
          </div>

          <!-- 点赞信息 -->
          <div class="interaction-stats">
            <div class="stat-item">
              <icon-clock-circle size="16" />
              <span>{{ currentVideo.create_time }}</span>
            </div>
            <div class="stat-group">
              <!-- <div class="stat-item">
                <icon-play-circle size="16" />
                <span>{{ currentVideo.play_count }}</span>
              </div> -->
              <div class="stat-item">
                <icon-heart size="16" />
                <span>{{ currentVideo.like_count }}</span>
              </div>
              <div class="stat-item">
                <icon-message size="16" />
                <span>{{ currentVideo.comment_count }}</span>
              </div>
              <div class="stat-item">
                <icon-star size="16" />
                <span>{{ currentVideo.favorite_count }}</span>
              </div>
            </div>
          </div>
          <div class="comment-divider">
            <div class="comment-header">
              <span>全部评论</span>
              <div class="inline"> </div>
              <a-radio-group v-model="commentSortType" type="button">
                <a-radio value="desc" @click="toggleCommentSort('desc')"
                  >按时间倒序</a-radio
                >
                <a-radio value="asc" @click="toggleCommentSort('asc')"
                  >按时间正序</a-radio
                >
              </a-radio-group>
            </div>
          </div>
          <!-- 评论加载中状态 -->
          <div v-if="commentLoading" class="comment-loading">
            <a-spin />
            <span class="loading-text">评论加载中...</span>
          </div>
          <!-- 评论列表 -->
          <div v-else-if="commentList.length" class="comment-container">
            <div class="comment-list-box">
              <div
                v-for="(item, index) in commentList"
                :key="index"
                class="comment-item"
              >
                <a-comment :datetime="item.comment_time">
                  <template #avatar>
                    <a-avatar
                      :size="32"
                      class="comment-avatar clickable-avatar"
                      @click="handleAvatarClick(item, false)"
                    >
                      <img
                        v-if="item.avatar_url"
                        :src="item.avatar_url"
                        :alt="item.account_name"
                        @error="
                          (e) => {
                            e.target.src = './icons/common/avatar.png';
                          }
                        "
                      />
                      <span v-else>
                        {{ item.account_name?.charAt(0) || '用' }}
                      </span>
                    </a-avatar>
                  </template>
                  <!-- 昵称 -->
                  <template #author>
                    <span
                      class="comment-nickname"
                      @click="handleAvatarClick(item, false)"
                      >{{ item.account_name }}</span
                    >
                  </template>
                  <template #content>
                    <div class="comment-content-wrapper">
                      <chat-emoji-text
                        :content="item.thread_content"
                        :platform="item.platform"
                      />
                      <!-- 临时加个置顶的状态展示 -->
                      <div v-if="item.is_top" class="comment-top-status">
                        置顶评论
                      </div>
                      <!-- 一级评论操作按钮 -->
                      <template
                        v-if="
                          !(
                            item.thread_content &&
                            item.thread_content.startsWith('[') &&
                            item.thread_content.endsWith(']')
                          )
                        "
                      >
                        <div
                          v-if="![3, 4].includes(item.status)"
                          class="comment-actions"
                        >
                          <a-link
                            :type="item.is_top ? 'primary' : 'text'"
                            size="mini"
                            class="action-btn top-btn"
                            :class="{ 'is-top': item.is_top }"
                            @click="handleTopComment(item)"
                          >
                            <icon-pushpin />
                            {{ item.is_top ? '已置顶' : '置顶' }}
                          </a-link>
                          <a-link
                            type="text"
                            size="mini"
                            class="action-btn"
                            @click="handleReplyComment(item)"
                          >
                            <icon-message />
                            回复
                          </a-link>
                          <a-link
                            type="text"
                            size="mini"
                            class="action-btn like-btn"
                            :class="{ liked: item.is_like }"
                            @click="handleLikeComment(item)"
                          >
                            <icon-heart
                              class="like-heart"
                              :class="[
                                item.is_like ? 'liked' : 'not-liked',
                                item.likeAnim ? 'like-anim' : '',
                              ]"
                            />
                            {{ item.like_count || '赞' }}
                          </a-link>
                        </div>
                      </template>
                    </div>
                  </template>
                  <template v-if="item.sub_comment?.length">
                    <a-comment
                      v-for="(citem, cindex) in item.sub_comment"
                      :key="cindex"
                      :datetime="citem.comment_time"
                    >
                      <template #avatar>
                        <a-avatar
                          :size="32"
                          class="comment-avatar clickable-avatar"
                          @click="handleAvatarClick(citem, true)"
                        >
                          <img
                            v-if="citem.avatar_url"
                            :src="citem.avatar_url"
                            :alt="citem.account_name"
                            @error="
                              (e) => {
                                e.target.src = './icons/common/avatar.png';
                              }
                            "
                          />
                          <span v-else>
                            {{ citem.account_name?.charAt(0) || '用' }}
                          </span>
                        </a-avatar>
                      </template>
                      <!-- 昵称 -->
                      <template #author>
                        <span
                          class="comment-nickname"
                          @click="handleAvatarClick(citem, false)"
                          >{{ citem.account_name }}</span
                        >
                      </template>
                      <template #content>
                        <div class="comment-content-wrapper">
                          <chat-emoji-text
                            :content="citem.thread_content"
                            :platform="citem.platform"
                          />
                          <!-- 二级评论操作按钮 -->
                          <template
                            v-if="
                              !(
                                citem.thread_content &&
                                citem.thread_content.startsWith('[') &&
                                citem.thread_content.endsWith(']')
                              )
                            "
                          >
                            <div
                              v-if="![3, 4].includes(citem.status)"
                              class="comment-actions"
                            >
                              <a-link
                                type="text"
                                size="mini"
                                class="action-btn"
                                @click="handleReplySubComment(citem, item)"
                              >
                                <icon-message />
                                回复
                              </a-link>
                              <a-link
                                type="text"
                                size="mini"
                                class="action-btn like-btn"
                                :class="{ liked: citem.is_like }"
                                @click="handleLikeSubComment(citem)"
                              >
                                <icon-heart
                                  class="like-heart"
                                  :class="[
                                    citem.is_like ? 'liked' : 'not-liked',
                                    citem.likeAnim ? 'like-anim' : '',
                                  ]"
                                />
                                {{ citem.like_count || '赞' }}
                              </a-link>
                            </div>
                          </template>
                        </div>
                      </template>
                    </a-comment>
                  </template>
                </a-comment>
              </div>
            </div>
            <div class="df jc-sb mt-4 mt-10 pagination-box">
              <div></div>
              <a-pagination
                v-bind="pagination"
                size="mini"
                :show-total="true"
                :show-jumper="false"
                simple
                @change="(val: number) => { pagination.current = val; getCommentList(); }"
                @page-size-change="(val: number) => { pagination.current = 1; pagination.pageSize = val; getCommentList(); }"
              />
            </div>
          </div>
          <!-- 空状态 -->
          <a-empty v-else description="暂无评论" />
          <!-- 聊天输入模块 - 仅在回复模式下显示 -->
          <div class="chat-input-module">
            <!-- 回复提示 -->
            <div v-if="showReplyInput" class="reply-prompt">
              <span>回复 @{{ replyTarget.account_name }}：</span>
              <a-button type="text" size="mini" @click="cancelReply">
                <template #icon>
                  <icon-close />
                </template>
              </a-button>
            </div>
            <div v-else class="reply-prompt">
              <span>发表评论</span>
            </div>
            <!-- 输入工具栏与文本区域整合 -->
            <div class="input-main-area">
              <div class="input-content">
                <a-textarea
                  ref="inputRef"
                  :model-value="inputMessage"
                  :placeholder="
                    showReplyInput
                      ? `回复 @${replyTarget.account_name}...`
                      : '说点什么吧...'
                  "
                  class="message-textarea"
                  :auto-size="{ minRows: 3, maxRows: 6 }"
                  :allow-clear="false"
                  :max-length="300"
                  show-word-limit
                  @update:model-value="handleInputChange"
                />
                <div class="send-action">
                  <div class="input-toolbar">
                    <chat-emoji class="emoji-btn" @select="selectEmoji">
                      <img
                        class="toolbar-btn"
                        src="@/assets/images/msg-smile.png"
                        alt="表情"
                      />
                    </chat-emoji>
                  </div>

                  <a-button
                    type="primary"
                    size="small"
                    @click="handleSendReply"
                  >
                    <template #icon>
                      <icon-send />
                    </template>
                    {{ showReplyInput ? '回复' : '发布' }}
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </d-modal>

  <!-- 聊天弹窗 -->
  <ChatDialog
    v-model:visible="chatDialogVisible"
    :message-data="chatDialogMessageData"
    :account-data="chatDialogAccountData"
    @close="handleChatDialogClose"
  />
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { ref, computed, nextTick } from 'vue';
  import request from '@/api/request';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { pageConfig } from '@/utils/table-utils/table-util';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import ChatEmojiText from '@/views/travel-clue/sale-clue-list/chat-emoji-text.vue';
  import ChatEmoji from '@/views/travel-clue/sale-clue-list/chat-emoji.vue';
  import ChatDialog from '@/components/chat-dialog/ChatDialog.vue';
  import { Message, Modal } from '@arco-design/web-vue';

  // 聊天弹窗相关类型定义
  interface ChatDialogProps {
    visible: boolean;
    messageData?: any;
    accountData?: any;
    isReceive?: number; // 0-陌拜场景, 1-正常聊天
  }

  interface SendMsgCommentParams {
    content?: string; // 消息内容
    content_type?: string; // 消息类型：'text' 或 'image'
    from_account_id?: string; // 发送方账号ID
    tread_id?: string; // 线索ID
  }

  const defaultForm = () => ({
    order_field: 'create_time',
    order_type: 'desc',
    title: '',
  });
  const formModel = ref(defaultForm());
  const visible = ref(false);
  const loading = ref(false);
  const commentLoading = ref(false);
  const info = ref<any>({});
  const currentVideo = ref<any>({});
  const list = ref<any[]>([]);
  const commentList = ref<any[]>([]);
  const pagination = pageConfig({ pageSize: 10 });
  // 是否需要回显视频
  const needShowMediaVideoId = ref('');

  // 评论排序相关状态
  const commentSortType = ref<'desc' | 'asc'>('desc'); // 默认时间倒序
  const commentSortText = computed(() => {
    return commentSortType.value === 'desc' ? '时间倒序' : '时间正序';
  });
  const sortType = [
    { label: '最新发布', value: 'create_time', icon: 'icon-calendar' },
    { label: '最高播放', value: 'play_count', icon: 'icon-play-circle' },
    { label: '最多评论', value: 'comment_count', icon: 'icon-message' },
    { label: '最高点赞', value: 'like_count', icon: 'icon-heart' },
  ];

  // 输入相关状态
  const inputMessage = ref('');
  const localInputMessage = ref(''); // 本地输入框内容（用于表情包插入）
  const inputRef = ref();

  // 回复相关状态
  const showReplyInput = ref(false);
  const replyTarget = ref<any>({});
  const replyType = ref<'comment' | 'sub_comment'>('comment'); // 回复类型：一级评论或二级评论
  const parentComment = ref<any>({}); // 父评论（用于回复二级评论时）

  // 聊天弹窗相关数据
  const chatDialogVisible = ref(false);
  const chatDialogMessageData = ref<any>(null);
  const chatDialogAccountData = ref<any>(null);

  function previewInNewWindow() {
    window.open(currentVideo.value.video_url, '_blank');
  }

  function getCommentList() {
    // 1. 立即清空评论信息，避免显示上一个帖子的评论
    commentList.value = [];
    pagination.total = 0;

    // 2. 加载状态
    commentLoading.value = true;

    request('/api/account/accountProductionCommentList', {
      media_video_id: currentVideo.value.media_video_id,
      page: pagination.current,
      pageSize: pagination.pageSize,
      order_field: 'comment_time',
      order_type: commentSortType.value,
    })
      .then((res) => {
        // 3. 评论
        commentList.value = res.data.data || [];
        pagination.total = res.data.total;
      })
      .catch((error) => {
        console.error('获取评论列表失败:', error);
        // 错误时也清空评论列表
        commentList.value = [];
        pagination.total = 0;
      })
      .finally(() => {
        // 确保loading状态正确结束
        commentLoading.value = false;
      });
  }

  // 切换评论排序方式
  function toggleCommentSort(sortTypeStr: any) {
    commentSortType.value = sortTypeStr;
    // 重置分页到第一页
    pagination.current = 1;
    // 重新获取评论列表
    getCommentList();
  }

  function selectVideo(item: any) {
    // 立即清空当前评论，避免显示上一个视频的评论
    commentList.value = [];
    pagination.total = 0;

    // 更新当前视频
    currentVideo.value = item;

    // 重置分页到第一页
    pagination.current = 1;

    // 重置评论排序为默认倒序
    commentSortType.value = 'desc';

    // 重置回复状态
    showReplyInput.value = false;
    replyTarget.value = {};
    replyType.value = 'comment';
    parentComment.value = {};
    inputMessage.value = '';
    localInputMessage.value = '';

    // 获取新视频的评论列表
    getCommentList();
  }

  function getList() {
    loading.value = true;
    request('/api/account/accountProductionList', {
      ...formModel.value,
      account_id: info.value.account_id,
      page: 1,
      pageSize: 1000,
    })
      .then((res) => {
        list.value = res.data.data || [];
        if (list.value.length > 0 && needShowMediaVideoId.value) {
          // 回显视频
          const video = list.value.find(
            (item: any) => item.media_video_id === needShowMediaVideoId.value
          );
          if (video) {
            selectVideo(video);
            nextTick(() => {
              // 需要滚动到该视频
              const videoListEl = document.querySelector('.video-list');
              const videoItemEl =
                videoListEl?.querySelector(
                  `.video-item[key="${video.media_video_id}"]`
                ) ||
                videoListEl?.querySelector(
                  `.video-item[data-media-video-id="${video.media_video_id}"]`
                );
              if (videoItemEl && videoListEl) {
                // 滚动到该视频元素
                videoItemEl.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                });
              }
            });
          }
        } else if (list.value.length > 0 && !currentVideo.value.create_time) {
          selectVideo(list.value[0]);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function show(ddata: any, content?: any) {
    visible.value = true;
    info.value = ddata;
    list.value = [];
    commentList.value = [];
    currentVideo.value = {};
    formModel.value = defaultForm();
    // 重置评论排序为默认倒序
    commentSortType.value = 'desc';

    // 重置回复状态
    showReplyInput.value = false;
    replyTarget.value = {};
    replyType.value = 'comment';
    parentComment.value = {};
    inputMessage.value = '';
    localInputMessage.value = '';
    // 只设置needShowMediaVideoId，由getList统一处理selectVideo
    if (content?.media_video_id) {
      needShowMediaVideoId.value = content.media_video_id;
    } else {
      needShowMediaVideoId.value = '';
    }
    getList();
  }

  // 处理输入框内容变化
  const handleInputChange = (value: string) => {
    inputMessage.value = value;
    localInputMessage.value = value;
  };
  // 表情包选择处理 - 优化版本
  const selectEmoji = (emoji: string) => {
    if (!inputRef.value?.textareaRef) {
      console.warn('输入框引用不存在');
      return;
    }

    const textareaElement = inputRef.value.textareaRef;
    const currentValue = inputMessage.value || '';
    const cursorPosition =
      textareaElement.selectionStart || currentValue.length;

    // 在光标位置插入表情包
    const newValue =
      currentValue.slice(0, cursorPosition) +
      emoji +
      currentValue.slice(cursorPosition);

    // 更新输入框内容
    inputMessage.value = newValue;
    localInputMessage.value = newValue;

    // 设置光标位置到表情包后面
    nextTick(() => {
      textareaElement.focus();
      const newCursorPosition = cursorPosition + emoji.length;
      textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
    });

    console.log('😊 表情包已插入:', emoji);
  };

  // 处理置顶评论
  const handleTopComment = async (comment: any) => {
    console.log('置顶评论:', comment);
    try {
      await request('/api/posts/pinOrUnpinComment', {
        from_account_id: info.value.account_id,
        post_id: comment.tread_id,
      });
      // 重新获取评论列表
      getCommentList();
    } catch (err) {
      console.error('置顶评论失败:', err);
    }
  };

  // 处理回复一级评论
  const handleReplyComment = (comment: any) => {
    showReplyInput.value = true;
    replyTarget.value = comment;
    replyType.value = 'comment';
    parentComment.value = {};

    // 聚焦到输入框
    nextTick(() => {
      inputRef.value?.focus();
    });
  };

  // 处理回复二级评论
  const handleReplySubComment = (subComment: any, parentCommentItem: any) => {
    showReplyInput.value = true;
    replyTarget.value = subComment;
    replyType.value = 'sub_comment';
    parentComment.value = parentCommentItem;

    // 聚焦到输入框
    nextTick(() => {
      inputRef.value?.focus();
    });
  };

  // 处理点赞一级评论
  const handleLikeComment = async (comment: any) => {
    const prevLiked = comment.is_like;
    // 乐观更新
    comment.is_like = !prevLiked;
    comment.like_count = comment.is_like
      ? (comment.like_count || 0) + 1
      : Math.max(0, (comment.like_count || 0) - 1);
    comment.likeAnim = true;
    setTimeout(() => {
      comment.likeAnim = false;
    }, 500);
    try {
      await request('/api/posts/likeOrUnlikeComment', {
        from_account_id: info.value.account_id,
        post_id: comment.tread_id,
      });
    } catch (e) {
      // 回滚
      comment.is_like = prevLiked;
      comment.like_count = prevLiked
        ? (comment.like_count || 0) + 1
        : Math.max(0, (comment.like_count || 0) - 1);
    }
  };

  // 处理点赞二级评论
  const handleLikeSubComment = async (subComment: any) => {
    const prevLiked = subComment.is_like;
    subComment.is_like = !prevLiked;
    subComment.like_count = subComment.is_like
      ? (subComment.like_count || 0) + 1
      : Math.max(0, (subComment.like_count || 0) - 1);
    subComment.likeAnim = true;
    setTimeout(() => {
      subComment.likeAnim = false;
    }, 500);
    try {
      await request('/api/posts/likeOrUnlikeComment', {
        from_account_id: info.value.account_id,
        post_id: subComment.tread_id,
      });
    } catch (e) {
      subComment.is_like = prevLiked;
      subComment.like_count = prevLiked
        ? (subComment.like_count || 0) + 1
        : Math.max(0, (subComment.like_count || 0) - 1);
    }
  };

  // 取消回复
  const cancelReply = () => {
    showReplyInput.value = false;
    replyTarget.value = {};
    replyType.value = 'comment';
    parentComment.value = {};
    inputMessage.value = '';
    localInputMessage.value = '';
  };

  // 处理发送回复
  const handleSendReply = async (event?: KeyboardEvent) => {
    // 如果是回车键事件，检查是否按住了Shift键
    if (event && event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift+Enter 换行，不发送消息
        return;
      }
      // 阻止默认的换行行为
      event.preventDefault();
    }

    const messageContent = inputMessage.value?.trim();
    if (!messageContent) {
      console.warn('回复内容为空，无法发送');
      return;
    }

    try {
      if (showReplyInput.value) {
        // 回复评论逻辑（原有逻辑）
        await request('/api/posts/replyComment', {
          content: messageContent,
          from_account_id: info.value.account_id,
          post_id: replyTarget.value.tread_id,
        });
        // 清空输入框并隐藏回复框
        cancelReply();
      } else {
        // 直接评论逻辑
        await request('/api/posts/remarkNote', {
          content: messageContent,
          from_account_id: info.value.account_id,
          post_id: currentVideo.value.media_video_id,
        });
        inputMessage.value = '';
        localInputMessage.value = '';
      }
      // 重新获取评论列表
      getCommentList();
      console.log('✅ 评论发送成功');
      Modal.success({
        title: '评论发送成功',
        content: '评论发送成功,请稍后查看',
      });
    } catch (err) {
      console.error('❌ 评论发送失败:', err);
    }
  };

  // 处理头像点击事件
  const handleAvatarClick = (commentData: any, isSubComment = false) => {
    console.log(
      '🎯 评论区头像点击事件:',
      commentData.account_name,
      isSubComment ? '(子评论)' : '(主评论)'
    );

    // 设置聊天弹窗数据
    chatDialogMessageData.value = {
      account_name: commentData.account_name,
      avatar_url: commentData.avatar_url,
      platform: commentData.platform || '未知平台',
      tread_id: commentData.tread_id || commentData.id,
      is_receive: commentData.is_receive,
      id: commentData.id,
      account_id: commentData.account_id,
      clue_id: commentData.clue_id,
    };

    // 设置当前账号数据
    chatDialogAccountData.value = {
      account_id: info.value.account_id,
      account_name: info.value.account_name,
      avatar: info.value.avatar,
    };

    // 打开聊天弹窗
    chatDialogVisible.value = true;
  };

  // 处理聊天弹窗关闭事件
  const handleChatDialogClose = () => {
    console.log('聊天弹窗已关闭');
    chatDialogVisible.value = false;
    chatDialogMessageData.value = null;
    chatDialogAccountData.value = null;
  };

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .video-list-box {
    width: 350px;
    height: 100%;
    overflow-y: hidden;
    background: var(--color-bg-1);
    // padding-right: 12px;

    .header-box {
      width: 100%;
      .tabs-box {
        margin-bottom: 8px;
      }
    }

    .video-list {
      width: 100%;
      flex: 1;
      overflow-y: auto;
      padding: 0 5px 0 0;
    }

    .video-item {
      width: 100%;
      border: 1px solid var(--color-border-1);
      border-radius: 12px;
      background: var(--color-bg-2);
      transition: all 0.2s ease;
      cursor: pointer;
      padding: 8px;
      margin: 8px 0;
      position: relative;
      display: flex;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(var(--primary-1), 0.15);
      }

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 0 0 0;
        border-color: transparent transparent transparent transparent;
        transition: all 0.2s ease;
      }

      &.video-item-active {
        border: 1px solid var(--color-border-2);
        box-shadow: 0 2px 8px rgba(var(--primary-1), 0.2);

        &::before {
          content: '已选择';
          position: absolute;
          left: -1px;
          top: 0;
          background: rgb(var(--primary-6));
          color: #fff;
          font-size: 12px;
          padding: 2px 10px;
          width: max-content;
          height: max-content;
          border-radius: 12px 0 12px 0;
          z-index: 1;
        }
      }

      .video-cover {
        background: var(--color-neutral-2);
        position: relative;
        overflow: hidden;
        width: 100px;
        height: 65px;
        border-radius: 8px;

        .mengban {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 8px;
          object-fit: cover;
          background: #211d2f;
          filter: blur(8px);
          transform: scale(1.5);
          opacity: 0.6;
        }
      }

      .video-info {
        flex: 1;
        margin-left: 12px;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .video-title {
          color: var(--color-neutral-10);
          height: 20px;
          font-weight: 500;
        }

        .video-time {
          color: var(--color-neutral-6);
          font-size: 10px;
          margin-top: 2px;
        }

        .icon-box {
          & > div {
            display: flex;
            align-items: center;
            padding: 2px 4px;
            border-radius: 4px;
            transition: background-color 0.2s;

            &:hover {
              background: var(--color-fill-2);
            }
          }

          font-size: 12px;
          :deep(.arco-icon) {
            color: var(--color-neutral-6);
          }
        }
      }
    }
  }

  .video-detail {
    flex: 1;
    height: 100%;
    display: flex;
    background: var(--color-bg-2);
    padding-top: 10px;
    margin-left: 10px;
    overflow: hidden;

    .video-preview {
      position: relative;
      height: 100%;
      width: 45.3vh;
      padding-top: 56.25%; /* 16:9 ratio (9/16 * 100) */
      overflow: hidden;
      background: #000;
      border-radius: 12px;
      @media (max-width: 1600px) {
        width: 44.7vh !important;
      }

      .preview-item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        &.img-item {
          width: 100%;
          height: auto;
          object-fit: contain;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        :deep(.arco-carousel-arrow > div) {
          background: rgba(0, 0, 0, 0.4);
          backdrop-filter: blur(4px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          border-radius: 50%;
          width: 36px;
          height: 36px;

          &:hover {
            background: rgba(0, 0, 0, 0.6);
          }
        }
      }
    }

    .preview-box {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;

      .preview-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 68px;
        z-index: 2;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(4px);
        opacity: 0;
        transition: all 0.2s ease;

        &:hover {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1.1);
        }
      }
      &:hover {
        .preview-icon {
          opacity: 1;
        }
      }
    }

    .comment-list {
      flex: 1;
      height: 100%;
      overflow-y: auto;
      padding: 0 20px;
      min-width: 360px;
      .comment-divider {
        margin-top: 30px;
        margin-bottom: 10px;

        .comment-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          .inline {
            flex: 1;
            height: 1px;
            background: var(--color-border-1);
            margin: 0 2px;
          }

          .sort-button {
            color: var(--color-text-2);
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
              color: var(--color-primary-6);
              background: var(--color-primary-light-1);
            }

            &:not(:disabled):active {
              transform: scale(0.95);
            }
          }
        }
      }

      .account-info {
        position: relative;
        z-index: 2;
        margin-bottom: 16px;

        :deep(.arco-typography) {
          margin: 0;
        }

        .account-avatar {
          background: #fff;
          border: 3px solid #fff;
          border-radius: 50%;
          width: 64px;
          height: 64px;
          margin: 0 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .account-info-content {
          font-size: 13px;
        }
      }

      .comment-container {
        .comment-item {
          padding: 5px 12px;
          border-radius: 8px;
          transition: background-color 0.2s;

          // &:hover {
          //   background: var(--color-fill-2);
          // }

          // 可点击头像样式
          .clickable-avatar {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.05);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              border: 2px solid var(--color-primary-6);
            }

            &:active {
              transform: scale(0.98);
            }
          }
          .comment-nickname {
            cursor: pointer;
            transition: all 0.2s ease;
            &:hover {
              color: var(--color-primary-6);
            }
          }

          :deep(.arco-comment) {
            padding: 0;
          }
        }
        .pagination-box {
          margin-bottom: 20px;
        }
      }
    }
  }

  // 评论内容包装器
  .comment-content-wrapper {
    position: relative;

    .comment-actions {
      display: flex;
      gap: 8px;
      margin: 8px 0 0;
      padding-top: 8px;
      border-top: 1px solid var(--color-border-1);

      .action-btn {
        padding: 2px 8px;
        min-width: 56px;
        font-size: 12px;
        color: var(--color-text-3);
        border-radius: 16px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        gap: 4px;

        // &:hover {
        //   color: var(--color-primary-6);
        //   background: var(--color-primary-light-1);
        // }

        :deep(.arco-icon) {
          margin-right: 2px;
        }
      }
    }
  }

  // 回复提示样式
  .reply-prompt {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--color-primary-light-1);
    border-radius: 8px 8px 0 0;
    border: 1px solid var(--color-primary-light-2);
    border-bottom: none;
    margin: 0 10px 0 50px;

    span {
      color: var(--color-primary-6);
      font-size: 13px;
      font-weight: 500;
    }

    .arco-btn {
      padding: 2px 4px;
      height: 20px;
      color: var(--color-text-3);

      &:hover {
        color: var(--color-text-1);
        background: var(--color-fill-2);
      }
    }
  }

  .interaction-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    margin-bottom: 8px;

    .stat-group {
      display: flex;
      align-items: center;
      gap: 24px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 6px;
      color: var(--color-text-2);
      font-size: 13px;

      .arco-icon {
        color: var(--color-text-3);
      }

      span {
        min-width: 24px;
        text-align: left;
      }
    }
  }

  .comment-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--color-neutral-6);

    .loading-text {
      margin-top: 12px;
      font-size: 14px;
    }
  }

  // 聊天输入模块 - 整合优化，统一设计风格
  .chat-input-module {
    flex-shrink: 0;
    margin-top: 16px;

    // 主输入区域
    .input-main-area {
      padding: 8px 18px 8px 18px;
      border-radius: 0 0 24px 24px;
      box-shadow: 0 4px 32px 0 rgba(80, 60, 180, 0.08),
        0 1.5px 6px 0 rgba(80, 60, 180, 0.04);
      border: 1.5px solid #ece6ff;
      border-top: none;
      margin: 0 10px 24px 10px;
      position: relative;
      transition: box-shadow 0.2s;
      &:hover {
        box-shadow: 0 8px 40px 0 rgba(80, 60, 180, 0.13),
          0 2px 8px 0 rgba(80, 60, 180, 0.06);
      }

      .input-toolbar {
        display: flex;
        gap: 10px;
        padding-left: 4px;
        margin-bottom: 2px;

        .toolbar-btn {
          border-radius: 10px;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          width: 22px;
          height: 22px;
          cursor: pointer;
          background: #fff;
          box-shadow: 0 1px 4px rgba(80, 60, 180, 0.07);
          &:hover {
            transform: translateY(-2px) scale(1.08);
            box-shadow: 0 2px 8px rgba(80, 60, 180, 0.13);
          }
        }
      }

      .input-content {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: flex-end;

        .message-textarea {
          flex: 1;
          border-radius: 14px;
          border: 1.5px solid #ece6ff;
          background: #fff;
          font-size: 15px;
          line-height: 1.7;
          padding: 10px 0;
          box-shadow: 0 1px 4px rgba(80, 60, 180, 0.04);
          transition: border-color 0.2s, box-shadow 0.2s;
          min-height: 44px;
          &:focus {
            border-color: var(--color-primary-6);
            box-shadow: 0 0 0 2px rgba(var(--primary-6), 0.1);
          }
          // 隐藏滚动条
          -ms-overflow-style: none;
          &::-webkit-scrollbar {
            display: none;
          }
          :deep(.arco-textarea) {
            -ms-overflow-style: none;
            scrollbar-width: none;
            &::-webkit-scrollbar {
              display: none;
            }
          }
          :deep(textarea) {
            -ms-overflow-style: none;
            scrollbar-width: none;
            &::-webkit-scrollbar {
              display: none;
            }
          }
          &:focus-within {
            :deep(.arco-textarea) {
              border: none !important;
              box-shadow: none !important;
            }
          }
        }

        .send-action {
          // position: absolute;
          right: 8px;
          bottom: 8px;
          z-index: 999;
          display: flex;
          align-items: center;
          gap: 10px;
        }
      }
    }

    // 回复提示
    .reply-prompt {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 18px 10px 28px;
      background: linear-gradient(90deg, #f3f0ff 60%, #f8fafc 100%);
      border-radius: 20px 20px 0 0;
      border: 1.5px solid #ece6ff;
      border-bottom: none;
      margin: 0 10px;
      box-shadow: 0 4px 24px rgba(80, 60, 180, 0.07);
      position: relative;
      z-index: 2;
      span {
        color: var(--color-primary-6);
        font-size: 15px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
      .arco-btn {
        padding: 2px 8px;
        height: 24px;
        color: var(--color-text-3);
        border-radius: 8px;
        background: #fff;
        box-shadow: 0 1px 4px rgba(80, 60, 180, 0.07);
        transition: all 0.18s cubic-bezier(0.4, 0, 0.2, 1);
        &:hover {
          color: var(--color-primary-6);
          background: var(--color-primary-light-1);
        }
      }
    }
  }

  .action-btn.top-btn {
    &.is-top {
      background: linear-gradient(90deg, #ffd700 0%, #fffbe6 100%);
      color: #b48800 !important;
      border: 1px solid #ffe066;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(255, 215, 0, 0.18);
      position: relative;
      z-index: 2;
      &:hover {
        background: linear-gradient(90deg, #ffe066 0%, #fffbe6 100%);
        color: #a07a00 !important;
        border-color: #ffd700;
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.25);
      }
      // &::after {
      //   content: 'TOP';
      //   position: absolute;
      //   top: -10px;
      //   right: -14px;
      //   background: #ffd700;
      //   color: #fff;
      //   font-size: 8px;
      //   font-weight: 700;
      //   padding: 2px 4px;
      //   border-radius: 8px;
      //   box-shadow: 0 2px 8px rgba(255, 215, 0, 0.18);
      // }
    }
  }
  .action-btn.like-btn {
    position: relative;
    .like-heart {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      transition: color 0.18s, transform 0.18s, filter 0.18s;
      &.liked {
        color: #e63946;
        fill: #e63946;
      }
      &.like-anim {
        animation: like-bounce 0.5s cubic-bezier(0.5, -0.5, 0.5, 1.5);
      }
    }
  }
  @keyframes like-bounce {
    0% {
      transform: scale(1);
    }
    30% {
      transform: scale(1.35);
    }
    60% {
      transform: scale(0.92);
    }
    100% {
      transform: scale(1);
    }
  }

  .comment-top-status {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: rgba(var(--danger-6));
    margin-top: 5px;
    margin-bottom: 10px;
    font-weight: 500;
    background: rgba(var(--danger-1), 0.8);
    padding: 2px 6px;
    border-radius: 25px;
    width: 55px;
  }
</style>

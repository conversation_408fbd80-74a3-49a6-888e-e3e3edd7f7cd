<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    title="添加设备"
    :footer="false"
  >
    <div style="margin: 0 auto" class="w-200">
      <a-image
        :width="200"
        :height="200"
        fit="contain"
        src="others/apk-code.png"
      />
      <div style="text-align: center"> 请用手机扫码下载应用并安装 </div>
    </div>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';

  const visible = ref(false);

  const show = () => {
    visible.value = true;
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

<template>
  <a-modal
    v-model:visible="visible"
    :title="atUser ? `回复 @${atUser}` : '发布评论'"
    :footer="false"
    width="400px"
  >
    <div v-if="atUser" style="margin-bottom: 8px; color: #888"
      >回复 @{{ atUser }}</div
    >
    <!-- 输入工具栏与文本区域整合 -->
    <div class="input-main-area">
      <div class="input-content">
        <a-textarea
          ref="inputRef"
          :model-value="inputMessage"
          :placeholder="
            showReplyInput
              ? `回复 @${replyTarget.account_name}...`
              : '说点什么吧...'
          "
          class="message-textarea"
          :auto-size="{ minRows: 3, maxRows: 6 }"
          :allow-clear="false"
          :max-length="300"
          show-word-limit
          @update:model-value="handleInputChange"
        />
        <div class="send-action">
          <div class="input-toolbar">
            <chat-emoji class="emoji-btn" @select="selectEmoji">
              <img
                class="toolbar-btn"
                src="@/assets/images/msg-smile.png"
                alt="表情"
              />
            </chat-emoji>
          </div>
          <a-button type="primary" size="small" @click="handleSubmit">
            <template #icon>
              <icon-send />
            </template>
            {{ showReplyInput ? '回复' : '发布' }}
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import ChatEmojiText from '@/views/travel-clue/sale-clue-list/chat-emoji-text.vue';
  import ChatEmoji from '@/views/travel-clue/sale-clue-list/chat-emoji.vue';

  // 输入相关状态（提前声明，避免未定义报错）
  const inputMessage = ref('');
  const localInputMessage = ref(''); // 本地输入框内容（用于表情包插入）
  const inputRef = ref();
  const loading = ref(false);

  const visible = ref(false);
  let postId = '';
  let fromAccountId = '';
  let atUser = '';
  let parentCommentId = '';

  // 回复相关状态
  const showReplyInput = ref(false);
  const replyTarget = ref<any>({});
  const replyType = ref<'comment' | 'sub_comment'>('comment'); // 回复类型：一级评论或二级评论
  const parentComment = ref<any>({}); // 父评论（用于回复二级评论时）

  function show({
    post_id,
    from_account_id,
    at_user = '',
    parent_comment_id = '',
  }: {
    post_id: string;
    from_account_id: string;
    at_user?: string;
    parent_comment_id?: string;
  }) {
    postId = post_id;
    fromAccountId = from_account_id;
    atUser = at_user;
    parentCommentId = parent_comment_id;
    inputMessage.value = atUser ? `` : '';
    visible.value = true;
  }

  // 处理输入框内容变化
  const handleInputChange = (value: string) => {
    inputMessage.value = value;
    localInputMessage.value = value;
  };
  // 表情包选择处理 - 优化版本
  const selectEmoji = (emoji: string) => {
    if (!inputRef.value?.textareaRef) {
      console.warn('输入框引用不存在');
      return;
    }

    const textareaElement = inputRef.value.textareaRef;
    const currentValue = inputMessage.value || '';
    const cursorPosition =
      textareaElement.selectionStart || currentValue.length;

    // 在光标位置插入表情包
    const newValue =
      currentValue.slice(0, cursorPosition) +
      emoji +
      currentValue.slice(cursorPosition);

    // 更新输入框内容
    inputMessage.value = newValue;
    localInputMessage.value = newValue;

    // 设置光标位置到表情包后面
    nextTick(() => {
      textareaElement.focus();
      const newCursorPosition = cursorPosition + emoji.length;
      textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
    });

    console.log('😊 表情包已插入:', emoji);
  };

  async function handleSubmit() {
    if (!inputMessage.value.trim()) {
      Message.warning('请输入评论内容');
      return;
    }
    loading.value = true;
    try {
      const params: any = {
        post_id: postId,
        from_account_id: fromAccountId,
        content: inputMessage.value.trim(),
      };
      if (parentCommentId) params.parent_comment_id = parentCommentId;
      await request('/api/posts/replyComment', params);
      Message.success('回复成功');
      visible.value = false;
    } catch (e: any) {
      // Message.error(e?.message || '回复失败');
    } finally {
      loading.value = false;
    }
  }

  defineExpose({ show });
</script>

<style scoped lang="less">
  // 聊天输入模块 - 整合优化，统一设计风格
  // .chat-input-module {
  //   flex-shrink: 0;
  //   margin-top: 16px;

  // 主输入区域
  .input-main-area {
    position: relative;
    transition: box-shadow 0.2s;

    .input-toolbar {
      display: flex;
      gap: 10px;
      padding-left: 4px;
      margin-bottom: 2px;

      .toolbar-btn {
        border-radius: 10px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        width: 22px;
        height: 22px;
        cursor: pointer;
        background: #fff;
        box-shadow: 0 1px 4px rgba(80, 60, 180, 0.07);
        &:hover {
          box-shadow: 0 2px 8px rgba(80, 60, 180, 0.13);
        }
      }
    }

    .input-content {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 14px;
      align-items: flex-end;
      margin-top: 8px;

      .message-textarea {
        flex: 1;
        border-radius: 14px;
        border: 1.5px solid #ece6ff;
        background: #fff;
        font-size: 15px;
        line-height: 1.7;
        padding: 10px 0px;
        box-shadow: 0 1px 4px rgba(80, 60, 180, 0.04);
        transition: border-color 0.2s, box-shadow 0.2s;
        min-height: 44px;
        &:focus {
          border-color: var(--color-primary-6);
          box-shadow: 0 0 0 2px rgba(var(--primary-6), 0.1);
        }
        // 隐藏滚动条
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
          display: none;
        }
        :deep(.arco-textarea) {
          -ms-overflow-style: none;
          scrollbar-width: none;
          &::-webkit-scrollbar {
            display: none;
          }
        }
        :deep(textarea) {
          -ms-overflow-style: none;
          scrollbar-width: none;
          &::-webkit-scrollbar {
            display: none;
          }
        }
        &:focus-within {
          :deep(.arco-textarea) {
            border: none !important;
            box-shadow: none !important;
          }
        }
      }

      .send-action {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
        width: 100%;
      }
    }
  }

  // 回复提示
  .reply-prompt {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 18px 10px 28px;
    background: linear-gradient(90deg, #f3f0ff 60%, #f8fafc 100%);
    border-radius: 20px 20px 0 0;
    border: 1.5px solid #ece6ff;
    border-bottom: none;
    margin: 0 10px;
    box-shadow: 0 4px 24px rgba(80, 60, 180, 0.07);
    position: relative;
    z-index: 2;
    span {
      color: var(--color-primary-6);
      font-size: 15px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
    .arco-btn {
      padding: 2px 8px;
      height: 24px;
      color: var(--color-text-3);
      border-radius: 8px;
      background: #fff;
      box-shadow: 0 1px 4px rgba(80, 60, 180, 0.07);
      transition: all 0.18s cubic-bezier(0.4, 0, 0.2, 1);
      &:hover {
        color: var(--color-primary-6);
        background: var(--color-primary-light-1);
      }
    }
  }
  // }
</style>

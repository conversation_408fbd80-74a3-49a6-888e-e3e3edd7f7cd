<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <!-- 发布内容 -->
          <a-input-search
            v-model="formModel.thread_content"
            placeholder="请输入评论内容或上级评论内容"
            allow-clear
            style="width: 280px"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
          <!-- 发布账号 -->
          <a-input-search
            v-model="formModel.account"
            placeholder="请输入发布账号名称或ID"
            allow-clear
            style="width: 220px"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
          <!-- 平台 -->
          <platform-select
            v-model="formModel.platform"
            placeholder="请选择平台"
            style="width: 120px"
            @change="handleSubmit()"
          ></platform-select>
          <!-- 状态 -->
          <dict-radio
            v-model="formModel.status"
            :data-list="[{ label: '全部', value: '' }, ...commentStatusM]"
            @change="handleSubmit()"
          />
        </a-space>
        <a-space> </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="table-card"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <template #content="{ record }: TableColumnSlot">
          <xhs-post-inline-simple
            v-if="record.video_info"
            :video-info="record.video_info"
          />
          <div v-else class="parent-comment-empty">无</div>
        </template>
        <template #from_account_name="{ record }: TableColumnSlot">
          <account-info-row
            :id="record.from_account_id"
            :avatar="record.from_account_avatar_url"
            :name="record.from_account_name"
          />
        </template>
        <template #account_name="{ record }: TableColumnSlot">
          <div
            v-if="record && (record.avatar_url || record.account_name)"
            class="account-info-row"
          >
            <img
              :src="record.avatar_url"
              class="account-info-avatar"
              alt="avatar"
            />
            <span class="account-info-name">{{
              record.account_name || '匿名'
            }}</span>
          </div>
          <div v-else class="parent-comment-empty">无</div>
        </template>

        <template #thread_content="{ record }: TableColumnSlot">
          <div
            v-if="
              record &&
              (record.avatar_url ||
                record.account_name ||
                record.thread_content)
            "
            class="main-comment-vertical"
          >
            <div class="main-comment-row">
              <div class="main-comment-info-simple">
                <span class="main-comment-content-simple">
                  <chat-emoji-text
                    :content="record.thread_content"
                    :platform="record.platform"
                  />
                </span>
                <div class="main-comment-id"
                  >ID: {{ record.media_video_id }}</div
                >
              </div>
            </div>
          </div>
          <div v-else class="parent-comment-empty">无</div>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <status-badge :list="commentStatusM" :value="record.status" />
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showContent(record)">查看</a-link>
            <template
              v-if="
                ![3, 4].includes(record.status) &&
                !(
                  record.thread_content &&
                  record.thread_content.startsWith('[') &&
                  record.thread_content.endsWith(']')
                )
              "
            >
              <template v-if="record.comment_type_text == 1">
                <a-popconfirm
                  v-if="!record.is_top"
                  content="确定要置顶该评论吗？"
                  @ok="handlePin(record)"
                >
                  <a-link>置顶</a-link>
                </a-popconfirm>
                <a-popconfirm
                  v-else
                  content="确定要取消置顶该评论吗？"
                  @ok="handleUnpin(record)"
                >
                  <a-link>取消置顶</a-link>
                </a-popconfirm>
              </template>
              <a-link @click="handleReply(record)">回复</a-link>
              <a-popconfirm
                v-if="record.status !== 3"
                content="确定要删除该评论吗？"
                position="left"
                @ok="handleDelete(record)"
              >
                <a-link>删除</a-link>
              </a-popconfirm>
            </template>
          </a-space>
        </template>
        <template #parent_comment="{ record }">
          <div
            v-if="
              record.parent_comment && typeof record.parent_comment === 'object'
            "
            class="parent-comment-simple"
          >
            <img
              :src="record.parent_comment.avatar_url"
              class="parent-comment-avatar-simple"
              alt="avatar"
            />
            <div class="parent-comment-info-simple">
              <span class="parent-comment-nickname-simple">{{
                record.parent_comment.account_name || '-'
              }}</span>
              <span class="parent-comment-content-simple">
                <chat-emoji-text
                  :content="record.parent_comment.thread_content"
                  :platform="record.parent_comment.platform"
                />
              </span>
            </div>
          </div>
          <div v-else class="parent-comment-empty">无</div>
        </template>
        <!-- 平台列 -->
        <template #platform="{ record }: TableColumnSlot">
          <div class="platform-cell">
            <img
              class="platform-icon"
              :src="`icons/platform/${record.platform}.png`"
              :alt="record.platform"
            />
            <span class="platform-name">{{ record.platform }}</span>
          </div>
        </template>
        <template #like_count="{ record }: TableColumnSlot">
          <span class="like-count-cell">
            <icon-heart style="font-size: 16px" />
            <span>{{ record.like_count }}</span>
          </span>
        </template>
        <template #comment_type_text="{ record }">
          <span>
            {{
              record.comment_type_text ? record.comment_type_text + '级' : '-'
            }}
          </span>
        </template>
        <template #intentionality_intent="{ record }">
          <span
            :class="[
              'intentionality-intent',
              record.intentionality_intent === 'positive'
                ? 'intent-positive'
                : record.intentionality_intent === 'negative'
                ? 'intent-negative'
                : 'intent-unknown',
            ]"
          >
            {{
              record.intentionality_intent == 'positive'
                ? '有意向'
                : record.intentionality_intent == 'negative'
                ? '无意向'
                : '未知'
            }}
          </span>
        </template>
      </base-table>
    </a-card>
    <account-content-modal ref="contentRef"></account-content-modal>
    <ReplyCommentModal ref="replyModalRef" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { commentStatusM } from '@/components/dict-select/dict-account';
  import { TableColumnSlot } from '@/global';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import AccountContentModal from '@/views/account/devices/account-content-modal.vue';
  import PlatformSelect from '@/components/dict-select/platform-select.vue';
  import ChatEmojiText from '@/views/travel-clue/sale-clue-list/chat-emoji-text.vue';
  import { IconHeartFill, IconMessage } from '@arco-design/web-vue/es/icon';
  import XhsPostInlineSimple from '@/components/xhs-post-inline-simple.vue';
  import AccountInfoRow from '@/components/account-info-row.vue';
  import SegmentedProgressBar from './components/segmented-progress-bar.vue';
  import ReplyCommentModal from './reply-comment-modal.vue';

  // 定义 emit 事件
  const emit = defineEmits<{
    viewTask: [taskId: string | number];
  }>();

  const generateFormModel = () => {
    return {
      status: null,
      thread_content: null,
      account: null,
      platform: null,
    };
  };

  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());
  const replyModalRef = ref();

  const getList = async (data: any) => {
    const res = await request('/api/account/accountCommentList', {
      ...data,
    });
    // 兼容 parent_comment 为字符串的情况，转为对象
    if (Array.isArray(res?.data)) {
      res.data.forEach((item) => {
        if (item.parent_comment && typeof item.parent_comment === 'string') {
          try {
            item.parent_comment = JSON.parse(item.parent_comment);
          } catch (e) {
            item.parent_comment = null;
          }
        }
      });
    }
    return res;
  };

  const detailRef = ref();

  // 取消请求控制器
  let cancelToken: AbortController;

  // 1. 修改 columns 配置，将评论ID和评论内容合并为一列
  const columns = [
    {
      title: '评论',
      dataIndex: 'thread_content',
      slotName: 'thread_content', // 使用自定义插槽
      ellipsis: true,
      tooltip: true,
    },
    // 评论
    {
      title: '评论用户',
      dataIndex: 'account_name',
      align: 'left',
      slotName: 'account_name',
    },
    // 点赞
    {
      title: '点赞量',
      dataIndex: 'like_count',
      slotName: 'like_count',
      align: 'center',
    },
    // 收藏
    {
      title: 'IP归属地',
      dataIndex: 'ip_label',
      align: 'center',
    },
    // 状态
    {
      title: '评论时间',
      dataIndex: 'comment_time',
      align: 'center',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '层级',
      dataIndex: 'comment_type_text',
      slotName: 'comment_type_text',
      align: 'center',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '上级评论内容',
      dataIndex: 'parent_comment',
      slotName: 'parent_comment', // 使用自定义插槽
      ellipsis: true,
      tooltip: true,
      width: 280,
    },
    // 平台
    {
      title: '下级评论数',
      dataIndex: 'sub_comment_count',
      align: 'center',
    },
    // 状态
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
    },
    // 线索意向度
    {
      title: '线索意向度',
      dataIndex: 'intentionality_intent',
      align: 'center',
      slotName: 'intentionality_intent',
    },
    {
      title: '归属内容',
      dataIndex: 'content',
      slotName: 'content',
      width: 320,
    },
    // 归属账号
    {
      title: '归属账号',
      dataIndex: 'from_account_name',
      width: 220,
      ellipsis: true,
      tooltip: true,
    },
    // 平台
    {
      title: '平台',
      dataIndex: 'platform',
      align: 'center',
      ellipsis: true,
      tooltip: true,
      slotName: 'platform',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  const contentRef = ref();
  function showContent(item: any, content?: any) {
    contentRef.value?.show(
      {
        avatar_url: item.from_account_avatar_url,
        account_id: item.from_account_id,
        account_name: item.from_account_name,
        platform: item.platform,
      },
      {
        ...content,
        media_video_id: item.media_video_id,
      }
    );
  }

  // 处理查看按钮点击事件 - 通过 emit 传递任务ID给父组件
  const handleViewTask = (record: any) => {
    emit('viewTask', record.id);
  };

  // 取消任务操作
  const cancelTaskAction = (record: any) => {
    const loadingInstance = Message.loading('正在取消任务...', 0);

    // 取消之前的请求
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/cancelPublish',
      {
        type: 'task', // 任务级别取消
        task_ids: [record.id],
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success('取消任务成功');
        // 刷新任务列表
        theTable.value?.fetchData();
      })
      .catch((error) => {
        Message.error(error.message || '取消任务失败');
      })
      .finally(() => {
        loadingInstance.close();
      });
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  // 获取详细状态列表 - 如果API没有提供则生成模拟数据
  const getDetailStatusList = (record: any): number[] => {
    // 如果API响应中包含detail_status_list字段，直接使用
    if (record.detail_status_list && Array.isArray(record.detail_status_list)) {
      return record.detail_status_list;
    }

    // 否则根据现有数据生成模拟的详细状态列表
    const totalNum = record.total_num || 0;
    const successNum = record.success_num || 0;
    const failNum = record.fail_num || 0;
    const runningNum = Math.max(0, totalNum - successNum - failNum);

    const statusList: number[] = [];

    // 生成状态列表：先成功，再失败，最后是进行中/排队中
    for (let i = 0; i < successNum; i += 1) {
      statusList.push(2); // 已完成
    }
    for (let i = 0; i < failNum; i += 1) {
      statusList.push(3); // 失败
    }
    for (let i = 0; i < runningNum; i += 1) {
      // 随机分配进行中和排队中状态，让进度条更真实
      statusList.push(Math.random() > 0.7 ? 1 : 0); // 30%进行中，70%排队中
    }

    return statusList;
  };

  function handlePin(record: any) {
    request('/api/posts/pinOrUnpinComment', {
      from_account_id: record.from_account_id,
      post_id: record.tread_id,
    }).then(() => {
      Message.success('置顶成功');
      theTable.value?.fetchData();
    });
  }
  function handleUnpin(record: any) {
    request('/api/posts/pinOrUnpinComment', {
      from_account_id: record.from_account_id,
      post_id: record.tread_id,
    }).then(() => {
      Message.success('取消置顶成功');
      theTable.value?.fetchData();
    });
  }
  function handleReply(record: any) {
    // 打开回复评论弹窗
    replyModalRef.value?.show({
      post_id: record.tread_id,
      from_account_id: record.from_account_id,
      at_user: record.account_name,
      parent_comment_id: record.id,
    });
  }
  function handleDelete(record: any) {
    // 删除评论接口
    request('/api/posts/delComment', {
      from_account_id: record.from_account_id,
      post_id: record.tread_id,
    }).then(() => {
      Message.success('删除成功');
      theTable.value?.fetchData();
    });
  }
</script>

<style scoped lang="less">
  .progress-box {
    display: flex;
    flex-direction: column;
  }
  .progress-box-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;

    .progress-info-wrapper {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .progress-box-info-item {
      margin-right: 10px;
      flex-shrink: 0;

      &:last-child {
        margin-right: 0;
      }
    }
  }
  .progress-bar {
    cursor: pointer;
  }

  .account-display-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .accounts-avatars {
    display: flex;
    align-items: center;
    position: relative;
    height: 32px;
    flex-shrink: 0;

    .avatar-wrap {
      position: relative;
      width: 32px;
      height: 32px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--color-fill-3);
      transition: all 0.2s ease;
      margin-left: -8px;

      &:first-child {
        margin-left: 0;
      }

      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .platform-icon {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid var(--color-fill-2);
        background: var(--color-fill-2);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .more-count {
      position: relative;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: var(--color-fill-2);
      border: 1px solid var(--color-fill-3);
      margin-left: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-text-2);
      font-size: 12px;
      font-weight: 500;
    }
  }

  .accounts-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
    margin-left: 8px;

    .account-names,
    .account-ids {
      display: inline-block;
      align-items: center;
      gap: 4px;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1;
      margin-top: 4px;
      max-width: 250px;
    }

    .account-name {
      font-size: 13px;
      color: var(--color-text-1);
      font-weight: 500;
    }

    .account-id {
      font-size: 12px;
      color: var(--color-text-3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .separator {
      color: var(--color-text-3);
      font-size: 12px;
      flex-shrink: 0;
    }

    .more-text {
      font-size: 12px;
      color: var(--color-text-3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  :deep(.arco-table-tr-empty) {
    height: 75vh !important;
  }

  // 表格单元格省略号样式优化
  :deep(.arco-table-td) {
    .arco-table-cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // 确保表格内容不会超出容器
  :deep(.arco-table-container) {
    overflow: hidden;
  }

  // 响应式优化 - 在小屏幕上调整列宽
  @media (max-width: 1200px) {
    .account-display-wrapper {
      .accounts-info {
        .account-name {
          max-width: 80px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .account-display-wrapper {
      .accounts-info {
        .account-name {
          max-width: 60px;
        }
      }
    }

    .progress-box-info {
      .progress-info-wrapper {
        .progress-box-info-item {
          font-size: 11px;
          margin-right: 6px;
        }
      }
    }
  }

  .thread-content-wrapper {
    white-space: pre-wrap;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    line-height: 1.5;
    max-height: 3em;
  }

  .parent-comment-card {
    background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px 0 rgba(60, 72, 88, 0.06);
    display: flex;
    flex-direction: column;
    min-width: 0;
    transition: box-shadow 0.2s;
    margin: 2px 0;
    &:hover {
      box-shadow: 0 4px 16px 0 rgba(60, 72, 88, 0.12);
    }
  }
  .parent-comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  }
  .parent-comment-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 1.5px solid #e5e7eb;
    margin-right: 10px;
    box-shadow: 0 1px 4px 0 rgba(60, 72, 88, 0.08);
  }
  .parent-comment-user {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  .parent-comment-nickname {
    font-size: 14px;
    font-weight: 600;
    color: #222;
    line-height: 1.2;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .parent-comment-platform {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
  }
  .parent-comment-content {
    font-size: 13px;
    color: #374151;
    margin-left: 42px;
    line-height: 1.5;
    word-break: break-all;
    background: none;
    padding: 0;
  }
  .parent-comment-empty {
    color: #b0b3b8;
    font-style: italic;
    text-align: center;
    padding: 10px 0;
  }

  .main-comment-simple {
    display: flex;
    align-items: flex-start;
    max-width: 360px;
    min-width: 0;
    gap: 14px;
  }
  .main-comment-id {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 2px;
    margin-left: 2px;
    font-family: monospace;
  }
  .main-comment-avatar-simple {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    margin-top: 2px;
    box-shadow: 0 2px 8px 0 rgba(60, 72, 88, 0.1);
  }
  .main-comment-info-simple {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
  }
  .main-comment-nickname-simple {
    font-size: 15px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.2;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 3px;
  }
  .main-comment-content-simple {
    font-size: 14px;
    color: #222;
    line-height: 1.6;
    word-break: break-all;
    max-width: 340px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-line;
  }
  .parent-comment-simple {
    display: flex;
    align-items: flex-start;
    max-width: 340px;
    min-width: 0;
    gap: 10px;
  }
  .parent-comment-avatar-simple {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    margin-top: 2px;
  }
  .parent-comment-info-simple {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
  }
  .parent-comment-nickname-simple {
    font-size: 13px;
    font-weight: 500;
    color: #555;
    line-height: 1.2;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2px;
  }
  .parent-comment-content-simple {
    font-size: 13px;
    color: #888;
    line-height: 1.5;
    word-break: break-all;
    max-width: 320px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-line;
  }
  .main-comment-vertical {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 360px;
    min-width: 0;
    gap: 4px;
  }
  .main-comment-row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
  }
  .account-info-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .account-info-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
  }
  .account-info-col {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  .account-info-name {
    font-size: 13px;
    color: #222;
    font-weight: 500;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .account-info-id {
    font-size: 12px;
    color: #888;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .like-count-cell {
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: center;
  }
  .like-heart {
    display: inline-block;
    vertical-align: middle;
  }
  .xhs-post-card {
    display: flex;
    align-items: flex-start;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px 0 rgba(60, 72, 88, 0.08);
    cursor: pointer;
    padding: 8px 12px;
    gap: 12px;
    transition: box-shadow 0.2s;
    max-width: 270px;
    min-width: 0;
    &:hover {
      box-shadow: 0 4px 16px 0 rgba(60, 72, 88, 0.16);
    }
  }
  .xhs-post-card-large {
    max-width: 400px;
    min-width: 0;
    gap: 18px;
    padding: 12px 16px;
  }
  .xhs-post-cover {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    flex-shrink: 0;
  }
  .xhs-post-cover-large {
    width: 90px;
    height: 90px;
    border-radius: 10px;
  }
  .xhs-post-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
    gap: 4px;
  }
  .xhs-post-info-large {
    gap: 8px;
    min-width: 0;
    flex: 1;
  }
  .xhs-post-title {
    font-size: 14px;
    font-weight: 600;
    color: #222;
    line-height: 1.3;
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .xhs-post-title-large {
    font-size: 16px;
    font-weight: 600;
    max-width: 260px;
  }
  .xhs-post-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .xhs-post-desc-large {
    font-size: 14px;
    color: #444;
    line-height: 1.6;
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-line;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .xhs-post-author {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  .xhs-post-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
  }
  .xhs-post-author-name {
    font-size: 12px;
    color: #888;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .xhs-post-link {
    font-size: 12px;
    color: #ff2442;
    margin-top: 2px;
    font-weight: 500;
  }
  .xhs-post-inline {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    gap: 10px;
    max-width: 260px;
    min-width: 0;
    transition: background 0.15s;
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
    &:hover {
      background: #fafbfc;
    }
  }
  .xhs-post-inline-cover {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
  }
  .xhs-post-inline-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
    gap: 2px;
  }
  .xhs-post-inline-title {
    font-size: 13px;
    font-weight: 600;
    color: #222;
    line-height: 1.3;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .xhs-post-inline-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .xhs-post-inline-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 4px;
  }
  .xhs-post-inline-meta-item {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 12px;
    color: #888;
  }
  .xhs-post-inline-icon {
    display: inline-block;
    vertical-align: middle;
  }
  .xhs-post-inline-simple {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    box-shadow: none;
    padding: 0;
    max-width: 300px;
    min-width: 0;
  }
  .xhs-post-cover-simple {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
  }
  .xhs-post-info-simple {
    min-width: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  .xhs-post-title-simple {
    font-size: 13px;
    font-weight: 600;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .xhs-post-desc-simple {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-line;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .intentionality-intent {
    font-size: 14px;
  }
  .intent-positive {
    color: rgb(var(--primary-6));
  }
  .intent-negative {
    color: rgb(var(--danger-6));
  }
  .intent-unknown {
    color: rgb(var(--gray-6));
  }
  // 表格单元格样式
  .platform-cell {
    display: flex;
    align-items: center;
    gap: 4px;

    .platform-icon {
      width: 20px;
      height: 20px;
    }

    .platform-name {
      font-size: 13px;
      font-weight: 500;
      text-transform: capitalize;
    }
  }
</style>

<template>
  <div>
    <a-tabs
      v-model:active-key="activeKey"
      :hide-content="true"
      @change="changeTabs"
    >
      <a-tab-pane key="task">
        <template #title> 任务发布列表 </template>
      </a-tab-pane>
      <a-tab-pane key="content">
        <template #title> 内容发布列表 </template>
      </a-tab-pane>
    </a-tabs>
    <TaskPublishTask
      v-if="activeKey === 'task'"
      @view-task="handleViewTask"
    ></TaskPublishTask>
    <ContentPublishTask
      v-else
      :task-id="selectedTaskId"
      @clear-task-id="clearTaskId"
    ></ContentPublishTask>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import TaskPublishTask from './task-publish-task.vue';
  import ContentPublishTask from './content-publish-task.vue';

  const route = useRoute();
  const activeKey = ref('task');
  const selectedTaskId = ref<string | number | undefined>();

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    modelValue: {
      type: [String, Number, Array],
      default: () => '',
    },
  });

  // 处理查看任务事件 - 切换到内容发布列表并传递任务ID
  const handleViewTask = (taskId: string | number) => {
    selectedTaskId.value = taskId;
    activeKey.value = 'content';
  };

  // 清除选中的任务ID
  const clearTaskId = () => {
    selectedTaskId.value = undefined;
  };

  const changeTabs = (key: string) => {
    activeKey.value = key;
    if (key === 'task') {
      clearTaskId();
    }
  };

  // 监听路由参数变化，自动切换到对应的 tab
  onMounted(() => {
    const { tab, id } = route.query;
    if (tab === 'content' && id) {
      activeKey.value = 'content';
      selectedTaskId.value = id as string;
    }
  });
</script>

<style lang="less" scoped></style>

<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.status"
            :data-list="[{ label: '全部', value: '' }, ...publishTaskStatusM2]"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.account"
            placeholder="请输入账户昵称"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
        </a-space>
        <a-space> </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="table-card"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <template #account_id="{ record }: TableColumnSlot">
          <a-popover trigger="click" :content-style="{ width: '260px' }">
            <template #content>
              <a-space direction="vertical" class="w100p">
                <account-info
                  v-for="item in record.accounts"
                  :key="item.account_id"
                  :info="item"
                >
                </account-info>
              </a-space>
            </template>
            <div class="account-display-wrapper">
              <div class="accounts-avatars">
                <template
                  v-for="(acc, index) in record.accounts.slice(0, 3)"
                  :key="index"
                >
                  <div
                    class="avatar-wrap"
                    :style="{ zIndex: record.accounts.length - index }"
                  >
                    <img
                      :src="acc.profile_photo"
                      :alt="acc.name"
                      class="avatar"
                    />
                    <img
                      :src="`/icons/platform/${acc.platform}.png`"
                      :alt="acc.platform"
                      class="platform-icon"
                    />
                  </div>
                </template>
                <div v-if="record.accounts.length > 3" class="more-count">
                  +{{ record.accounts.length - 3 }}
                </div>
              </div>
              <div class="accounts-info">
                <div class="account-names">
                  <template
                    v-for="(acc, index) in record.accounts"
                    :key="index"
                  >
                    <span class="account-name">{{ acc.account_name }}</span>
                    <span
                      v-if="index < record.accounts.length - 1"
                      class="separator"
                      >,</span
                    >
                  </template>
                </div>
                <div class="account-ids">
                  <span v-if="record.accounts.length" class="more-text"
                    >共{{ record.accounts.length }}个账号</span
                  >
                </div>
              </div>
            </div>
          </a-popover>
        </template>
        <template #progress="{ record }: TableColumnSlot">
          <div class="progress-box" @click="handleViewTask(record)">
            <div class="progress-box-info">
              <a-tooltip
                :content="`成功: ${record.success_num}, 失败: ${record.fail_num}, 总计: ${record.total_num}`"
                position="top"
              >
                <div class="progress-info-wrapper">
                  <span
                    class="progress-box-info-item"
                    style="color: rgb(var(--green-4))"
                  >
                    成功 {{ record.success_num }}
                  </span>
                  <span
                    class="progress-box-info-item"
                    style="color: rgb(var(--red-6))"
                  >
                    失败 {{ record.fail_num }}
                  </span>
                  <span
                    class="progress-box-info-item"
                    style="color: rgb(var(--arcoblue-6))"
                  >
                    共 {{ record.total_num }}
                  </span>
                </div>
              </a-tooltip>
            </div>
            <!-- 使用自定义分段进度条组件 -->
            <segmented-progress-bar
              class="progress-bar"
              :detail-status-list="getDetailStatusList(record)"
              :total-num="record.total_num"
              :success-num="record.success_num"
              :fail-num="record.fail_num"
            />
          </div>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <status-badge :list="publishTaskStatusM2" :value="record.status" />
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <!-- 取消任务按钮 - 仅在排队中状态显示 -->
            <a-popconfirm
              v-if="record.status === -1"
              content="确定要取消此任务吗？"
              position="left"
              @ok="cancelTaskAction(record)"
            >
              <a-link type="text"> 取消发布 </a-link>
            </a-popconfirm>

            <a-link @click="handleViewTask(record)"> 查看 </a-link>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <publish-task-detail ref="detailRef" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { publishTaskStatusM2 } from '@/components/dict-select/dict-account';
  import { TableColumnSlot } from '@/global';
  import PublishTaskDetail from '@/views/account/publish-task/publish-task-detail.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import SegmentedProgressBar from './components/segmented-progress-bar.vue';

  // 定义 emit 事件
  const emit = defineEmits<{
    viewTask: [taskId: string | number];
  }>();

  const generateFormModel = () => {
    return {
      status: null,
    };
  };

  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/contentPublish/list', {
      ...data,
    });
  };

  const detailRef = ref();

  // 取消请求控制器
  let cancelToken: AbortController;

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      align: 'center',
      width: 120,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '账号',
      dataIndex: 'account_id',
      width: 280,
    },
    // {
    //   title: '内容数量',
    //   dataIndex: 'total_num',
    //   align: 'center',
    // },
    {
      title: '进度',
      dataIndex: 'progress',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
      align: 'center',
      width: 160,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '完成时间',
      dataIndex: 'completion_time',
      align: 'center',
      width: 160,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  // 处理查看按钮点击事件 - 通过 emit 传递任务ID给父组件
  const handleViewTask = (record: any) => {
    emit('viewTask', record.id);
  };

  // 取消任务操作
  const cancelTaskAction = (record: any) => {
    const loadingInstance = Message.loading('正在取消任务...', 0);

    // 取消之前的请求
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/cancelPublish',
      {
        type: 'task', // 任务级别取消
        task_ids: [record.id],
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success('取消任务成功');
        // 刷新任务列表
        theTable.value?.fetchData();
      })
      .catch((error) => {
        Message.error(error.message || '取消任务失败');
      })
      .finally(() => {
        loadingInstance.close();
      });
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  // 获取详细状态列表 - 如果API没有提供则生成模拟数据
  const getDetailStatusList = (record: any): number[] => {
    // 如果API响应中包含detail_status_list字段，直接使用
    if (record.detail_status_list && Array.isArray(record.detail_status_list)) {
      return record.detail_status_list;
    }

    // 否则根据现有数据生成模拟的详细状态列表
    const totalNum = record.total_num || 0;
    const successNum = record.success_num || 0;
    const failNum = record.fail_num || 0;
    const runningNum = Math.max(0, totalNum - successNum - failNum);

    const statusList: number[] = [];

    // 生成状态列表：先成功，再失败，最后是进行中/排队中
    for (let i = 0; i < successNum; i += 1) {
      statusList.push(2); // 已完成
    }
    for (let i = 0; i < failNum; i += 1) {
      statusList.push(3); // 失败
    }
    for (let i = 0; i < runningNum; i += 1) {
      // 随机分配进行中和排队中状态，让进度条更真实
      statusList.push(Math.random() > 0.7 ? 1 : 0); // 30%进行中，70%排队中
    }

    return statusList;
  };
</script>

<style scoped lang="less">
  .progress-box {
    display: flex;
    flex-direction: column;
  }
  .progress-box-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;

    .progress-info-wrapper {
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .progress-box-info-item {
      margin-right: 10px;
      flex-shrink: 0;

      &:last-child {
        margin-right: 0;
      }
    }
  }
  .progress-bar {
    cursor: pointer;
  }

  .account-display-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .accounts-avatars {
    display: flex;
    align-items: center;
    position: relative;
    height: 32px;
    flex-shrink: 0;

    .avatar-wrap {
      position: relative;
      width: 32px;
      height: 32px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--color-fill-3);
      transition: all 0.2s ease;
      margin-left: -8px;

      &:first-child {
        margin-left: 0;
      }

      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .platform-icon {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid var(--color-fill-2);
        background: var(--color-fill-2);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .more-count {
      position: relative;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: var(--color-fill-2);
      border: 1px solid var(--color-fill-3);
      margin-left: -8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-text-2);
      font-size: 12px;
      font-weight: 500;
    }
  }

  .accounts-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
    margin-left: 8px;

    .account-names,
    .account-ids {
      display: inline-block;
      align-items: center;
      gap: 4px;
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1;
      margin-top: 4px;
      max-width: 250px;
    }

    .account-name {
      font-size: 13px;
      color: var(--color-text-1);
      font-weight: 500;
    }

    .account-id {
      font-size: 12px;
      color: var(--color-text-3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .separator {
      color: var(--color-text-3);
      font-size: 12px;
      flex-shrink: 0;
    }

    .more-text {
      font-size: 12px;
      color: var(--color-text-3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  :deep(.arco-table-tr-empty) {
    height: 75vh !important;
  }

  // 表格单元格省略号样式优化
  :deep(.arco-table-td) {
    .arco-table-cell {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // 确保表格内容不会超出容器
  :deep(.arco-table-container) {
    overflow: hidden;
  }

  // 响应式优化 - 在小屏幕上调整列宽
  @media (max-width: 1200px) {
    .account-display-wrapper {
      .accounts-info {
        .account-name {
          max-width: 80px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .account-display-wrapper {
      .accounts-info {
        .account-name {
          max-width: 60px;
        }
      }
    }

    .progress-box-info {
      .progress-info-wrapper {
        .progress-box-info-item {
          font-size: 11px;
          margin-right: 6px;
        }
      }
    }
  }
</style>

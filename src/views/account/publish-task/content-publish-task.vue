<template>
  <a-card size="small" class="mt-10">
    <div class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.status"
            :data-list="[
              { label: '全部', value: '' },
              ...publishContentStatusM2,
            ]"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.account"
            placeholder="请输入账户昵称、ID或任务ID"
            allow-clear
            class="w-300"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
          <a-tag
            v-if="props.taskId"
            closable
            color="arcoblue"
            @close="clearTaskId"
          >
            任务ID: {{ props.taskId }}
          </a-tag>
        </a-space>
      </div>
      <div class="table-card-action">
        <div>
          <a-space v-if="multipleAction">
            <span class="title-box">
              已选择
              <a-tag>
                {{ rowSelection.selectedRowKeys.length }}
              </a-tag>
              个内容
            </span>
            <a-button type="text" class="mr-10" @click="resetHandler">
              <template #icon>
                <icon-close-circle />
              </template>
              取消
            </a-button>
          </a-space>
          <a-space v-else>
            <a-button
              type="outline"
              @click="
                selectKeys = [];
                multipleAction = true;
              "
            >
              <template #icon>
                <icon-unordered-list />
              </template>
              批量操作
            </a-button>
          </a-space>
          <a-space v-if="multipleAction">
            <a-button
              type="primary"
              :disabled="!rowSelection.selectedRowKeys.length"
              @click="batchRetryAction"
            >
              <template #icon>
                <icon-redo />
              </template>
              批量重试
            </a-button>
            <a-button
              type="outline"
              status="danger"
              :disabled="!rowSelection.selectedRowKeys.length"
              @click="batchCancelPublishAction"
            >
              <template #icon>
                <icon-close />
              </template>
              批量取消发布
            </a-button>
          </a-space>
        </div>
      </div>
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :send-params="tableParams"
        :row-selection="multipleAction ? rowSelection : null"
        @select-change="selectionChange"
      >
        <template #account_id="{ record }: TableColumnSlot">
          <account-info :info="record" />
        </template>
        <template #content="{ record }: TableColumnSlot">
          <div v-if="record.content.type === 'video'">
            <video-content-item
              :item="record.content"
              size="mini"
              :can-select="false"
              :open-edit="false"
            />
          </div>
          <div v-else-if="record.content.image_list?.length">
            <image-content-item
              :item="record.content"
              size="mini"
              :can-select="false"
            />
          </div>
        </template>
        <template #cover_url="{ record }: TableColumnSlot">
          <div v-if="record.content.type === 'video'">
            <image-card-item
              :item="{
                url: record.content.cover_url,
                file_name: record.content.cover_name,
              }"
              size="mini"
              :can-select="false"
            />
          </div>
          <div v-else-if="record.content.image_list?.length">
            <image-card-item
              :item="{
                url: record.content.image_list.slice(0, 1)[0],
                file_name: record.content.cover_name,
              }"
              size="mini"
              :can-select="false"
            />
          </div>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <status-badge :list="publishContentStatusM2" :value="record.status" />
        </template>
        <template #publish_entrance="{ record }: TableColumnSlot">
          <a-popover v-if="record.inspiration_detail?.length">
            <template #content>
              <div class="tag-list">
                <div
                  v-for="(word, index) in record.inspiration_detail"
                  :key="index"
                  class="mt-5"
                >
                  <span>
                    #{{ word.name }}
                    {{
                      index < record.inspiration_detail?.length - 1 ? '，' : ''
                    }}
                  </span>
                </div>
              </div>
            </template>
            <a-link>
              <span>
                {{ record.publish_entrance }}
                <icon-eye />
                <br />
                (共{{ record.inspiration_detail?.length }}个灵感词)
              </span>
            </a-link>
          </a-popover>
          <span v-else>{{ record.publish_entrance }}</span>
        </template>
        <template #describe="{ record }: TableColumnSlot">
          <a-popover v-if="record.content?.describe?.length > 50">
            <template #content>
              <div
                style="
                  white-space: pre-line;
                  width: 300px;
                  max-height: 300px;
                  overflow: auto;
                "
              >
                {{ record.content?.describe }}
              </div>
            </template>
            <span> {{ record.content?.describe.slice(0, 50) }}... </span>
          </a-popover>
          <span v-else>{{ record.content?.describe || '-' }}</span>
        </template>
        <template #title="{ record }: TableColumnSlot">
          <a-popover v-if="record.content?.title?.length > 50">
            <template #content>
              <div
                style="
                  white-space: pre-line;
                  width: 300px;
                  max-height: 300px;
                  overflow: auto;
                "
              >
                {{ record.content?.title }}
              </div>
            </template>
            <span> {{ record.content?.title.slice(0, 50) }}... </span>
          </a-popover>
          <span v-else>{{ record.content?.title || '-' }}</span>
        </template>
        <template #operation="{ record }: TableColumnSlot">
          <a-space>
            <!-- 取消发布按钮 - 仅在排队中状态显示 -->
            <a-popconfirm
              v-if="record.status === -1"
              content="确定要取消发布这个内容吗？"
              @ok="cancelPublishAction(record)"
            >
              <a-link type="text"> 取消发布 </a-link>
            </a-popconfirm>

            <!-- 重试按钮 - 仅在发布失败状态显示 -->
            <a-link
              v-if="record.status === 3"
              type="text"
              @click="retryAction(record)"
            >
              <template #icon>
                <icon-redo />
              </template>
              重试
            </a-link>

            <!-- 查看按钮 - 对所有内容都显示 -->
            <a-link type="text" @click="previewContent(record)"> 查看 </a-link>
          </a-space>
        </template>
      </base-table>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, reactive, ref, watch, createApp } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconRefresh,
    IconCloseCircle,
    IconUnorderedList,
    IconRedo,
    IconClose,
    IconEye,
  } from '@arco-design/web-vue/es/icon';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import PlayerCom from '@/components/popup-preview-player/index.vue';
  import { defaultEmptyShow } from '@/utils/table-utils/columns-config';
  import { publishContentStatusM2 } from '@/components/dict-select/dict-account';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import ImageCardItem from '@/views/account/material-manage/components/image-card-item.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';

  // 定义 props 接收任务 ID
  const props = defineProps<{
    taskId?: string | number;
  }>();

  // 定义 emit 事件
  const emit = defineEmits<{
    clearTaskId: [];
  }>();

  const columns = computed(() => [
    {
      title: '发布内容ID',
      dataIndex: 'id',
      width: 120,
      align: 'center',
    },
    {
      title: '账号',
      dataIndex: 'account_id',
      width: 240,
    },
    // {
    //   title: '内容名称',
    //   dataIndex: 'content.name',
    //   render: defaultEmptyShow('content.name'),
    // },
    {
      title: '素材',
      dataIndex: 'content',
      align: 'center',
      width: 160,
    },
    // {
    //   title: '封面',
    //   dataIndex: 'cover_url',
    //   align: 'center',
    //   width: 160,
    // },
    {
      title: '标题',
      dataIndex: 'content.title',
      render: defaultEmptyShow('content.title'),
      slotName: 'title',
    },
    {
      title: '描述',
      dataIndex: 'content.describe',
      slotName: 'describe',
    },
    {
      title: '发布方式',
      dataIndex: 'publish_entrance',
      width: 150,
      align: 'center',
    },
    // {
    //   title: '完成时间',
    //   dataIndex: 'completion_time',
    //   width: 150,
    //   align: 'center',
    // },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      fixed: 'right',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 160,
      align: 'center',
      fixed: 'right',
    },
  ]);
  const visible = ref(false);
  const loading = ref(false);
  const theTable = ref();
  const multipleAction = ref(false);
  const info = ref<any>({});
  function defaultForm() {
    return {};
  }
  const formModel = ref(defaultForm());

  function queryAction() {
    theTable.value?.search();
  }

  const show = (dinfo: any) => {
    info.value = dinfo || {};
    visible.value = true;
    nextTick(() => {
      queryAction();
    });
  };

  // 清除任务ID
  const clearTaskId = () => {
    // emit('clearTaskId');
    info.value = {};
    queryAction();
  };

  // 监听 taskId props 变化，自动加载对应任务的数据
  watch(
    () => props.taskId,
    (newTaskId) => {
      if (newTaskId) {
        show({ id: newTaskId });
      }
    },
    { immediate: true }
  );
  const tableParams = computed(() => ({
    ...formModel.value,
    publish_id: info.value.id,
  }));

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
    checkStrictly: true,
  });
  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    rowSelection.selectedRowKeys = selectedRows.map((row) => row.id);
    rowSelection.selectedRows = selectedRows;
  };
  const resetHandler = () => {
    multipleAction.value = false;
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  let cancelToken: AbortController;
  function getList(data: any) {
    resetHandler();
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    return request(
      '/api/contentPublish/detail',
      {
        ...data,
      },
      cancelToken.signal
    );
  }

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  // 单个重试操作
  const retryAction = (record: any) => {
    const ids = [record.id];

    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/resendTask',
      {
        send_ids: ids,
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success('重试成功，请稍后查看任务结果');
        queryAction();
      })
      .catch(() => {
        loading.value = false;
      });
  };

  // 批量重试操作
  const batchRetryAction = () => {
    // 验证选中项状态：仅允许"发布失败"状态的内容进行重试
    const { selectedRows } = rowSelection;
    const invalidRows = selectedRows.filter((row) => row.status !== 3);

    if (invalidRows.length > 0) {
      Message.error('仅"发布失败"状态的发布内容支持重试；请检查所选内容。');
      return;
    }

    const ids = rowSelection.selectedRowKeys;

    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/resendTask',
      {
        send_ids: ids,
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success(
          `批量重试成功，共${ids.length}个任务，请稍后查看任务结果`
        );
        resetHandler(); // 重置选择状态
        queryAction();
      })
      .catch(() => {
        loading.value = false;
      });
  };

  // 单个取消发布操作
  const cancelPublishAction = (record: any) => {
    const loadingInstance = Message.loading('正在取消发布...', 0);

    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/cancelPublish',
      {
        type: 'task_detail',
        task_ids: [record.id],
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success('取消发布成功');
        queryAction();
      })
      .catch((error) => {
        Message.error(error.message || '取消发布失败');
      })
      .finally(() => {
        loadingInstance.close();
      });
  };

  // 批量取消发布操作
  const batchCancelPublishAction = () => {
    // 验证选中项状态：仅允许"排队中"状态的内容进行取消发布
    const { selectedRows } = rowSelection;
    const invalidRows = selectedRows.filter((row) => row.status !== -1);

    if (invalidRows.length > 0) {
      Message.error('仅"排队中"状态的发布内容支持取消发布；请检查所勾选内容。');
      return;
    }

    const ids = rowSelection.selectedRowKeys;
    const loadingInstance = Message.loading(
      `正在批量取消发布${ids.length}个任务...`,
      0
    );

    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();

    request(
      '/api/contentPublish/cancelPublish',
      {
        type: 'task_detail',
        task_ids: ids,
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success(`批量取消发布成功，共${ids.length}个任务`);
        resetHandler(); // 重置选择状态
        queryAction();
      })
      .catch((error) => {
        Message.error(error.message || '批量取消发布失败');
      })
      .finally(() => {
        loadingInstance.close();
      });
  };

  // 创建预览播放器实例
  let previewPlayerInstance: any = null;
  const getPreviewPlayer = () => {
    if (!previewPlayerInstance) {
      const PlayerC = createApp(PlayerCom);
      previewPlayerInstance = PlayerC.mount(document.createElement('div'));
      document.body.appendChild(previewPlayerInstance.$el);
    }
    return previewPlayerInstance;
  };

  // 预览内容
  const previewContent = (record: any) => {
    const { content } = record;
    if (!content) {
      Message.warning('暂无内容可预览');
      return;
    }

    try {
      const player = getPreviewPlayer();

      // 根据内容类型调用对应的预览方法
      if (content.content_type === 'video' || content.type === 'video') {
        // 视频内容预览 - 使用与 VideoContentItem 一致的调用方式
        player.play({
          videoUrl: content.video_url,
          title: content.name || content.content_name,
          extra: content,
        });
      } else if (
        content.content_type === 'image_text' ||
        content.content_type === 'image'
      ) {
        // 图文内容预览 - 使用与 ImageContentItem 一致的调用方式
        const imageList = content.image_list || content.content_urls || [];
        if (imageList.length > 0) {
          player.play({
            mediaList: imageList.map((current) => ({
              url: current,
              type: content.content_type,
              title:
                current.file_name || current.name || current.split('/').pop(),
              extra: content,
            })),
            activeUrl: imageList[0],
          });
        } else {
          Message.warning('该内容暂无图片可预览');
        }
      } else {
        // 混合内容或其他类型
        Message.info('该内容类型暂不支持预览');
      }
    } catch (error) {
      console.error('预览播放器错误:', error);
      Message.error('预览功能出现错误');
    }
  };

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-item {
    position: relative;
    background: var(--color-bg-1);
    width: 107px;
    height: 60px;
    display: inline-block;
    overflow: hidden;
    .img-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(5px);
      transform: scale(2);
      z-index: 1;
    }
    .img-cover {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
      position: relative;
      z-index: 2;
    }
    .preview-icon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 24px;
      z-index: 2;
      background: rgba(0, 0, 0, 0.3);
      padding: 5px;
      border-radius: 50%;
      opacity: 0.8;
      cursor: pointer;
    }
    &:hover {
      .preview-icon {
        opacity: 1;
      }
    }
    &:hover {
      box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
    }
  }

  .tag-list {
    max-height: 300px;
    overflow: auto;
  }

  .table-card {
    margin-top: 0px;
    .table-card-header {
      margin-top: 0px;
    }
  }

  .no-task-selected {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--color-fill-1);
    border-radius: 6px;
    margin-top: 10px;
  }

  .table-card-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
</style>

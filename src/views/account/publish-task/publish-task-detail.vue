<template>
  <d-modal
    v-model:visible="visible"
    :title="`任务详情【${info.id}】`"
    width="80vw"
    unmount-on-close
    :esc-to-close="false"
    :footer="null"
  >
    <div class="table-card">
      <div class="table-card-header mt-10">
        <a-space v-if="multipleAction">
          <span class="title-box">
            已选择
            <a-tag>
              {{ rowSelection.selectedRowKeys.length }}
            </a-tag>
            个内容
          </span>
          <a-button type="text" @click="resetHandler">
            <template #icon>
              <icon-close-circle />
            </template>
            取消
          </a-button>
        </a-space>
        <a-space v-else>
          <a-button
            type="outline"
            @click="
              selectKeys = [];
              multipleAction = true;
            "
          >
            <template #icon>
              <icon-unordered-list />
            </template>
            批量操作
          </a-button>
        </a-space>
        <a-space v-if="multipleAction">
          <a-button
            type="primary"
            :disabled="!rowSelection.selectedRowKeys.length"
            @click="retryAction"
          >
            <template #icon>
              <icon-redo />
            </template>
            批量重试
          </a-button>
        </a-space>
      </div>
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :auto-request="false"
        :scroll-percent="{ x: 1400, y: '70vh' }"
        :send-params="tableParams"
        :row-selection="multipleAction ? rowSelection : null"
        @select-change="selectionChange"
      >
        <template #account_id="{ record }: TableColumnSlot">
          <account-info :info="record" />
        </template>
        <template #content="{ record }: TableColumnSlot">
          <div v-if="record.content.type === 'video'">
            <video-content-item
              :item="record.content"
              size="mini"
              :can-select="false"
              :open-edit="false"
            />
          </div>
          <div v-else-if="record.content.image_list?.length">
            <image-content-item
              :item="record.content"
              size="mini"
              :can-select="false"
            />
          </div>
        </template>
        <template #cover_url="{ record }: TableColumnSlot">
          <div v-if="record.content.type === 'video'">
            <image-card-item
              :item="{
                url: record.content.cover_url,
                file_name: record.content.cover_name,
              }"
              size="mini"
              :can-select="false"
            />
          </div>
          <div v-else-if="record.content.image_list?.length">
            <image-card-item
              :item="{
                url: record.content.image_list.slice(0, 1)[0],
                file_name: record.content.cover_name,
              }"
              size="mini"
              :can-select="false"
            />
          </div>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <a-badge
            :color="getColor(publishStatusM, record.status) || 'gray'"
            :text="getText(publishStatusM, record.status) || '-'"
          />
        </template>
        <template #publish_entrance="{ record }: TableColumnSlot">
          <a-popover v-if="record.inspiration_detail?.length">
            <template #content>
              <div class="tag-list">
                <div
                  v-for="(word, index) in record.inspiration_detail"
                  :key="index"
                  class="mt-5"
                >
                  <span>
                    #{{ word.name }}
                    {{
                      index < record.inspiration_detail?.length - 1 ? '，' : ''
                    }}
                  </span>
                </div>
              </div>
            </template>
            <a-link>
              <span>
                {{ record.publish_entrance }}
                <icon-eye />
                <br />
                (共{{ record.inspiration_detail?.length }}个灵感词)
              </span>
            </a-link>
          </a-popover>
          <span v-else>{{ record.publish_entrance }}</span>
        </template>
        <template #describe="{ record }: TableColumnSlot">
          <a-popover v-if="record.content?.describe?.length > 50">
            <template #content>
              <div
                style="
                  white-space: pre-line;
                  width: 300px;
                  max-height: 300px;
                  overflow: auto;
                "
              >
                {{ record.content?.describe }}
              </div>
            </template>
            <span> {{ record.content?.describe.slice(0, 50) }}... </span>
          </a-popover>
          <span v-else>{{ record.content?.describe || '-' }}</span>
        </template>
        <template #operation="{ record }: TableColumnSlot">
          <a-button
            type="text"
            size="mini"
            :disabled="record.status !== 3"
            @click="retryAction(record)"
          >
            <template #icon>
              <icon-redo />
            </template>
            重试
          </a-button>
        </template>
      </base-table>
    </div>
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, reactive, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import { defaultEmptyShow } from '@/utils/table-utils/columns-config';
  import { publishStatusM } from '@/components/dict-select/dict-account';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import ImageCardItem from '@/views/account/material-manage/components/image-card-item.vue';
  import { Message } from '@arco-design/web-vue';

  const columns = computed(() => [
    {
      title: '账号',
      dataIndex: 'account_id',
      width: 260,
    },
    // {
    //   title: '内容名称',
    //   dataIndex: 'content.name',
    //   render: defaultEmptyShow('content.name'),
    // },
    {
      title: '素材',
      dataIndex: 'content',
      align: 'center',
      width: 160,
    },
    // {
    //   title: '封面',
    //   dataIndex: 'cover_url',
    //   align: 'center',
    //   width: 160,
    // },
    {
      title: '标题',
      dataIndex: 'content.title',
      render: defaultEmptyShow('content.title'),
    },
    {
      title: '描述',
      dataIndex: 'content.describe',
      slotName: 'describe',
    },
    {
      title: '发布方式',
      dataIndex: 'publish_entrance',
      width: 150,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      fixed: 'right',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      align: 'center',
      fixed: 'right',
    },
  ]);
  const visible = ref(false);
  const loading = ref(false);
  const theTable = ref();
  const multipleAction = ref(false);
  const info = ref<any>({});
  function defaultForm() {
    return {};
  }
  const formModel = ref(defaultForm());

  const tableParams = computed(() => ({
    ...formModel.value,
    publish_id: info.value.id,
  }));

  // 数据选择相关
  let rowSelection = reactive({
    type: 'checkbox',
    fixed: true,
    width: 30,
    onlyCurrent: true,
    showCheckedAll: true,
    selectedRowKeys: [] as any[],
    selectedRows: [] as any[],
    getCheckboxProps: (record: any) => ({
      disabled: record.status !== 3,
      checked: record.status === 3,
    }),
    checkStrictly: true,
  });
  const selectionChange = (selectedRowKeys: any[], selectedRows: any[]) => {
    // 只保留 status 为 3 的记录
    const validRows = selectedRows.filter((row) => row.status === 3);
    rowSelection.selectedRowKeys = validRows.map((row) => row.id);
    rowSelection.selectedRows = validRows;
  };
  const resetHandler = () => {
    multipleAction.value = false;
    rowSelection.selectedRowKeys = [];
    rowSelection.selectedRows = [];
  };

  function queryAction() {
    theTable.value?.search();
  }

  const show = (dinfo: any) => {
    info.value = dinfo || {};
    visible.value = true;
    nextTick(() => {
      queryAction();
    });
  };

  let cancelToken: AbortController;
  function getList(data: any) {
    resetHandler();
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    return request(
      '/api/contentPublish/detail',
      {
        ...data,
      },
      cancelToken.signal
    );
  }

  // 重试
  const retryAction = (record: any) => {
    console.log(rowSelection.selectedRows);
    let ids = [];
    if (multipleAction.value) {
      ids = rowSelection.selectedRowKeys;
    } else {
      ids = [record.id];
    }
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    request(
      '/api/contentPublish/resendTask',
      {
        send_ids: ids,
      },
      cancelToken.signal
    )
      .then(() => {
        Message.success('重试成功，请稍后查看任务结果');
        // handleCancel();
        // emit('refresh');
        getList();
      })
      .catch(() => {
        loading.value = false;
      });
  };

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-item {
    position: relative;
    background: var(--color-bg-1);
    width: 107px;
    height: 60px;
    display: inline-block;
    overflow: hidden;
    .img-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(5px);
      transform: scale(2);
      z-index: 1;
    }
    .img-cover {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
      position: relative;
      z-index: 2;
    }
    .preview-icon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 24px;
      z-index: 2;
      background: rgba(0, 0, 0, 0.3);
      padding: 5px;
      border-radius: 50%;
      opacity: 0.8;
      cursor: pointer;
    }
    &:hover {
      .preview-icon {
        opacity: 1;
      }
    }
    &:hover {
      box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
    }
  }

  .tag-list {
    max-height: 300px;
    overflow: auto;
  }
</style>

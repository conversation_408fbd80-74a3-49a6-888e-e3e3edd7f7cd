<template>
  <div
    class="video-card"
    :class="{
      'is-selected': isSelected && canSelect,
      'is-mini': size === 'mini',
      'can-select': canSelect,
    }"
    @click="$emit('select', item)"
  >
    <div class="video-card-preview">
      <div
        class="video-blur-bg"
        :style="{ backgroundImage: `url(${item.thumb})` }"
      ></div>
      <img :src="item.thumb" class="video-cover" :alt="item.file_name" />
      <div class="video-name" :title="item.file_name">{{ item.file_name }}</div>
      <div v-if="item.publish_num > 0" class="publish-num-tag">
        <span class="publish-num-text">已发布{{ item.publish_num }}次</span>
      </div>
      <div
        class="video-play-icon"
        @click.stop="
          $previewPlayer({
            videoUrl: item.url,
            title: item.file_name,
            extra: item,
          })
        "
      >
        <icon-play-circle-fill />
      </div>
      <div v-if="canSelect" class="video-select-indicator">
        <icon-check-circle-fill />
      </div>
    </div>
    <div class="video-card-info"> </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    isSingle: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'normal',
    },
    canSelect: {
      type: Boolean,
      default: true,
    },
  });

  defineEmits(['select']);
</script>

<style scoped lang="less">
  .video-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 100%;
    border: 1px solid var(--color-border-1);

    .video-select-indicator {
      position: absolute;
      top: 0px;
      left: 2px;
      color: #fff;
      font-size: 20px;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 2;
    }

    &.is-mini {
      width: 120px;
      .video-play-icon {
        font-size: 32px;
      }
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);

      .video-play-icon {
        opacity: 1;
        &:hover {
          opacity: 0.9;
          scale: 1.2;
        }
      }
    }

    &.is-selected {
      position: relative;
      &::before {
        content: '已选择';
        position: absolute;
        left: -1px;
        top: 0;
        background: rgb(var(--primary-6));
        color: #fff;
        font-size: 10px;
        padding: 4px 8px;
        width: max-content;
        height: max-content;
        border-radius: var(--border-radius-large) 0 var(--border-radius-large) 0;
        z-index: 99;
      }
      .video-select-indicator {
        opacity: 0;
      }
    }
  }

  .video-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .video-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .video-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .video-name {
      position: absolute;
      right: 0px;
      bottom: 0px;
      padding: 0px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 11px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }

    .publish-num-tag {
      position: absolute;
      left: 5px;
      bottom: 20px;
      z-index: 2;
      .publish-num-text {
        font-size: 10px;
        color: #fff;
      }
    }
    .video-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      color: #fff;
      font-size: 40px;
      opacity: 0;
      translate: -50% -50%;
      transition: all 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 99;
    }
  }

  .video-card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.65),
      rgba(0, 0, 0, 0.2) 50%,
      transparent
    );
    transition: all 0.3s ease;
    opacity: 1;
    border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);

    &:hover {
      background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.45),
        rgba(0, 0, 0, 0.3) 50%,
        transparent
      );
    }
  }

  .video-title {
    font-size: 14px;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
    padding: 0;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    max-width: 100%;

    :deep(.arco-checkbox-label),
    :deep(.arco-radio-label) {
      color: white;
      max-width: calc(100% - 10px);
      display: inline-block;
      vertical-align: middle;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    :deep(.arco-checkbox-icon-hover::before) {
      display: none;
    }
  }
</style>

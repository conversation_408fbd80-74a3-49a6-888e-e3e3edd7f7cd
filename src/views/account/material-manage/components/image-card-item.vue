<template>
  <div
    class="image-card"
    :class="{
      'is-selected': selected && canSelect,
      'can-select': canSelect,
      'is-mini': size === 'mini',
    }"
    @click="$emit('select', item)"
  >
    <div class="image-card-preview">
      <div
        class="image-blur-bg"
        :style="{ backgroundImage: `url(${item.url})` }"
      ></div>
      <img :src="item.url" :alt="item.file_name" class="image-cover" />
      <div v-if="item.file_name" class="image-name" :title="item.file_name">
        {{ item.file_name }}
      </div>
      <div v-if="item.ai_flag == 1" class="ai-icon">
        <img src="@/assets/images/ai-icon-3.png" alt="" />
      </div>
      <div v-if="item.publish_num > 0" class="publish-num-tag">
        <span class="publish-num-text">已发布{{ item.publish_num }}次</span>
      </div>
      <div
        class="image-preview-icon"
        @click.stop="
          $previewPlayer({
            mediaList: [
              {
                url: item.url,
                title: item.file_name,
                extra: item,
              },
            ],
          })
        "
      >
        <icon-search />
      </div>
      <div v-if="canSelect" class="image-select-indicator">
        <icon-check-circle-fill />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps({
    item: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      default: false,
    },
    canSelect: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'default',
    },
  });

  defineEmits(['select', 'preview']);
</script>

<style scoped lang="less">
  .image-card {
    position: relative;
    background: var(--color-bg-2);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 220px;
    border: 1px solid var(--color-border-1);

    .image-select-indicator {
      position: absolute;
      top: 0px;
      left: 2px;
      color: #fff;
      font-size: 20px;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 99;
    }
    &.is-mini {
      width: 120px;
      .video-play-icon {
        font-size: 32px;
      }
      .image-preview-icon {
        font-size: 32px;
      }
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      .image-preview-icon {
        opacity: 1;
        z-index: 99;
        &:hover {
          opacity: 0.95;
          scale: 1.2;
        }
      }
    }

    &.is-selected {
      position: relative;
      &::before {
        content: '已选择';
        position: absolute;
        left: -1px;
        top: 0;
        background: rgb(var(--primary-6));
        color: #fff;
        font-size: 10px;
        padding: 2px 8px;
        width: max-content;
        height: max-content;
        border-radius: var(--border-radius-large) 0 var(--border-radius-large) 0;
        z-index: 2;
      }
      .image-select-indicator {
        opacity: 0;
      }
    }
  }

  .image-card-preview {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 aspect ratio
    background: var(--color-fill-2);
    overflow: hidden;

    .image-blur-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(24px);
      transform: scale(3);
      z-index: 0;
    }

    .image-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      z-index: 1;
    }

    .image-name {
      position: absolute;
      right: 0px;
      bottom: 0px;
      padding: 2px 6px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0 0 4px;
      color: #fff;
      font-size: 10px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      z-index: 2;
    }
    .ai-icon {
      position: absolute;
      left: 5px;
      bottom: 5px;
      width: 20px;
      height: 20px;
      z-index: 2;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 100%;
      overflow: hidden;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
      box-shadow: 0 2px 2px 1px rgba(0, 0, 0, 0.2);
      img {
        width: 100%;
        height: 100%;
        scale: 1.2;
      }
    }
    .publish-num-tag {
      position: absolute;
      left: 5px;
      bottom: 20px;
      z-index: 2;
      .publish-num-text {
        font-size: 10px;
        color: #fff;
      }
    }

    .image-preview-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      color: #fff;
      font-size: 40px;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.5s ease;
      translate: -50% -50%;
    }
  }
</style>

<template>
  <d-modal
    :visible="visible"
    unmount-on-close
    :simple="false"
    title="上传素材"
    :mask-closable="false"
    title-align="start"
    :ok-loading="okLoading"
    width="460px"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="formRef" :model="formModel" auto-label-width>
      <a-form-item label="文件夹" field="dir_id" :rules="requiredRule">
        <folder-tree-select
          v-model="formModel.dir_id"
          :send-params="dirParams"
          value-key="id"
          label-key="dir_name"
          request-url="/api/material/dirList"
          :format-data="(arr:any) => arr?.children || []"
          :allow-clear="false"
        ></folder-tree-select>
      </a-form-item>
      <a-form-item
        v-if="formModel.dir_id"
        label="上传文件"
        field="files"
        :rules="requiredRuleArr"
      >
        <upload-file
          :model-value="formModel.files.map((item:any)=>item.url)"
          :file-type="fileType"
          :limit="20"
          :send-params="{ type: props.sendParams?.type }"
          @update:list="(item:any)=>formModel.files=item"
        >
        </upload-file>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script setup lang="ts">
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, ref } from 'vue';
  import UploadFile from '@/components/upload-file/upload-file.vue';
  import request from '@/api/request';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    sendParams: {
      type: [Object],
      default: null,
    },
    fileType: {
      type: String,
      default: '',
    },
  });
  const visible = ref(false);
  const okLoading = ref(false);
  const formRef = ref();
  const emits = defineEmits(['refresh']);
  function defaultForm() {
    return {
      files: [],
      dir_id: '',
    };
  }
  const formModel = ref(defaultForm());
  const dirParams = computed(() => ({
    ...props.sendParams,
  }));

  function handleCancel() {
    visible.value = false;
    formModel.value = defaultForm();
    emits('refresh');
  }

  function handleBeforeOk() {
    formRef.value?.validate((err: boolean) => {
      if (!err) {
        okLoading.value = true;
        request('/api/material/fileSave', {
          ...formModel.value,
          files: formModel.value.files.map((item: any) => ({
            file_name: item.name,
            url: item.response?.data?.file_path,
            format_data: item.response?.data?.format_data,
            thumb: item.response?.data?.thumb,
            type: props.sendParams?.type,
          })),
        })
          .then(() => {
            Message.success('保存成功');
            emits('refresh');
            handleCancel();
          })
          .finally(() => {
            okLoading.value = false;
          });
      }
    });
  }

  function show(params: any) {
    formModel.value = defaultForm();
    formModel.value.dir_id = params.dir_id || '';
    visible.value = true;
  }
  defineExpose({
    show,
  });
</script>

<style scoped lang="less"></style>

<template>
  <d-modal
    v-model:visible="visible"
    title="自动采集规则"
    width="65vw"
    unmount-on-close
    :esc-to-close="false"
    :footer="null"
  >
    <div class="table-card">
      <div class="table-card-header mt-10">
        <a-space> </a-space>
        <a-space>
          <a-button size="small" type="primary" @click="showConfigFn({})">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
        </a-space>
      </div>
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :auto-request="false"
        :send-params="tableParams"
      >
        <template #account_id="{ record }: TableColumnSlot">
          <account-display :multiple="true" :accounts="record.accounts" />
        </template>
        <template #state="{ record }: TableColumnSlot">
          <a-switch
            :model-value="record.state == 1"
            size="medium"
            @change="updateState(record)"
          >
            <template #checked> 开启 </template>
            <template #unchecked> 关闭 </template>
          </a-switch>
        </template>
        <template #operation="{ record }: TableColumnSlot">
          <a-button type="text" size="small" @click="showConfigFn(record)">
            <template #icon>
              <icon-edit />
            </template>
            编辑
          </a-button>
        </template>
      </base-table>
    </div>
    <collect-rule-config-modal
      ref="configModal"
      type-title="自动"
      @refresh="queryAction"
    />
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, reactive, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import { defaultEmptyShow } from '@/utils/table-utils/columns-config';
  import { publishStatusM } from '@/components/dict-select/dict-account';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import ImageCardItem from '@/views/account/material-manage/components/image-card-item.vue';
  import { Message } from '@arco-design/web-vue';
  import CollectRuleConfigModal from '@/views/tools/content-collect/components/collect-rule-config-modal.vue';
  import AccountDisplay from '@/components/account-display/account-display.vue';

  const configModal = ref();
  const showConfigFn = (record: any) => {
    configModal.value?.show(record);
  };

  const columns = computed(() => [
    {
      title: '状态',
      dataIndex: 'state',
      width: 100,
      align: 'center',
      fixed: 'right',
    },
    {
      title: '规则ID',
      dataIndex: 'id',
    },
    {
      title: '账号',
      dataIndex: 'account_id',
      maxWidth: 300,
    },
    {
      title: '创建人',
      dataIndex: 'create_user_name',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      align: 'center',
      fixed: 'right',
    },
  ]);
  const visible = ref(false);
  const loading = ref(false);
  const theTable = ref();
  const multipleAction = ref(false);
  const info = ref<any>({});
  function defaultForm() {
    return {};
  }
  const formModel = ref(defaultForm());

  const tableParams = computed(() => ({
    ...formModel.value,
    publish_id: info.value.id,
  }));

  function queryAction() {
    theTable.value?.search();
  }

  const show = (dinfo: any) => {
    info.value = dinfo || {};
    visible.value = true;
    nextTick(() => {
      queryAction();
    });
  };

  const editFn = (record: any) => {
    console.log(record);
  };

  let cancelToken: AbortController;
  function getList(data: any) {
    cancelToken?.abort('重复请求取消');
    cancelToken = new AbortController();
    return request(
      '/api/collect/autoSettingList',
      {
        ...data,
      },
      cancelToken.signal
    );
  }

  function updateState(record: any) {
    record.loading = true;
    request('/api/collect/autoSettingStatusAdjust', {
      ...record,
      state: record.state === 1 ? 0 : 1,
    })
      .then(() => {
        record.state = record.state === 1 ? 0 : 1;
        Message.success('操作成功');
      })
      .finally(() => {
        record.loading = false;
      });
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .img-item {
    position: relative;
    background: var(--color-bg-1);
    width: 107px;
    height: 60px;
    display: inline-block;
    overflow: hidden;
    .img-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(5px);
      transform: scale(2);
      z-index: 1;
    }
    .img-cover {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
      position: relative;
      z-index: 2;
    }
    .preview-icon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 24px;
      z-index: 2;
      background: rgba(0, 0, 0, 0.3);
      padding: 5px;
      border-radius: 50%;
      opacity: 0.8;
      cursor: pointer;
    }
    &:hover {
      .preview-icon {
        opacity: 1;
      }
    }
    &:hover {
      box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
    }
  }

  .tag-list {
    max-height: 300px;
    overflow: auto;
  }
</style>

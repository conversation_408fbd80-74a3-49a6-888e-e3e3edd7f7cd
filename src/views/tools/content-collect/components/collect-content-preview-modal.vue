<template>
  <a-modal
    :visible="visible"
    :title="title"
    :width="1050"
    :footer="false"
    @cancel="handleClose"
  >
    <div class="collect-content-preview">
      <!-- Header with actions -->
      <div class="preview-header">
        <div class="header-left">
          <a-space>
            <a-checkbox
              :model-value="isAllSelected"
              :indeterminate="
                selectedItems.length !== 0 &&
                selectedItems.length !== contentList.length
              "
              @change="handleSelectAll"
            >
              全选
            </a-checkbox>
            <!-- 提示 -->
            <template v-if="isAllSelected">
              <a-switch v-model="formModel.is_across_select">
                <template #checked>跨页全选</template>
                <template #unchecked>当前页</template>
              </a-switch>
              <a-tooltip content="切换勾选模式">
                <icon-question-circle />
              </a-tooltip>
            </template>
            <span v-if="!selectedItems.length" class="select-tip">
              <icon-info-circle />
              勾选素材后可批量下载
            </span>
            <div v-if="selectedItems.length" class="selection-info">
              <a-tag color="blue">
                <template #icon><icon-check /></template>
                已选择
                {{
                  isAllSelected && formModel.is_across_select
                    ? total
                    : selectedItems.length
                }}
                个内容
                <template v-if="formModel.is_across_select">
                  (跨页模式)
                </template>
              </a-tag>

              <a-button type="text" size="mini" @click="handleSelectAll(false)">
                <template #icon>
                  <icon-close-circle />
                </template>
                取消
              </a-button>
            </div>
          </a-space>
        </div>
        <div class="header-right">
          <a-space>
            <a-input-search
              v-model="formModel.keywords"
              placeholder="搜索内容"
              allow-clear
              size="small"
              style="width: 200px"
              @search="handleSearch"
              @clear="handleSearch"
              @keydown.enter="handleSearch"
            />
            <a-button
              type="primary"
              size="small"
              :disabled="!selectedItems.length"
              @click="handleDownload"
            >
              <template #icon><icon-download /></template>
              下载选中内容
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- Content grid -->
      <div class="preview-content">
        <a-spin :loading="loading" style="width: 100%">
          <div v-if="contentList.length" class="content-grid">
            <template v-for="item in contentList" :key="item.id">
              <image-content-item
                v-if="item.content_type === 'image_text'"
                :item="{
                  ...item,
                  describe: item.desc,
                  image_list: item.content_urls,
                }"
                :select-keys="selectedItems"
                @select="handleSelect"
              />
              <video-content-item
                v-else-if="item.content_type === 'video'"
                :item="{
                  ...item,
                  video_url: item.content_urls[0],
                }"
                :select-keys="selectedItems"
                @select="handleSelect"
              />
              <image-content-item
                v-else
                :item="{
                  ...item,
                  describe: item.desc,
                  image_list: item.content_urls,
                }"
                :select-keys="selectedItems"
                type="mix"
                @select="handleSelect"
              />
            </template>
          </div>
          <a-empty v-else description="暂无内容" />
        </a-spin>
      </div>

      <!-- Pagination -->
      <div class="preview-footer">
        <a-pagination
          :current="formModel.page"
          :page-size="formModel.pageSize"
          :total="total"
          show-total
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import request from '@/api/request';
  import { Message, Modal } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import { createAndPollDownload } from '@/utils/download-utils';

  const router = useRouter();

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['update:visible']);

  // Local state for record and visibility
  const localRecord = ref({});
  const visible = ref(false);

  // Form model for all data parameters
  const total = ref(0);
  const formModel = ref({
    keywords: null,
    is_across_select: false,
    type: 'all',
    page: 1,
    pageSize: 24,
  });

  // State
  const loading = ref(false);
  const contentList = ref([]);
  const selectedItems = ref([]);
  const allSelectedItems = ref(new Set()); // Track all selected items across pages

  // Computed
  const title = computed(() => {
    if (!localRecord.value?.id) return '采集内容预览';
    return `采集内容预览 - ${localRecord.value.account_name}`;
  });

  const isAllSelected = computed(() => {
    return (
      contentList.value.length > 0 &&
      selectedItems.value.length === contentList.value.length
    );
  });

  // Methods
  const resetState = () => {
    contentList.value = [];
    selectedItems.value = [];
    allSelectedItems.value.clear();
    formModel.value = {
      keywords: null,
      type: 'all',
      page: 1,
      pageSize: 24,
      is_across_select: false,
    };
    total.value = 0;
  };

  const handleClose = () => {
    visible.value = false;
    resetState();
  };

  const fetchContent = async () => {
    if (!localRecord.value?.id) return;
    loading.value = true;
    try {
      const res = await request('/api/collect/taskDetails', {
        id: localRecord.value.id,
        ...formModel.value,
        type: formModel.value.type === 'all' ? undefined : formModel.value.type,
      });
      contentList.value = res.data?.data || [];
      total.value = res.data?.total || 0;

      // Update selectedItems based on allSelectedItems when in cross-page mode
      if (formModel.value.is_across_select) {
        selectedItems.value = contentList.value
          .filter((item) => allSelectedItems.value.has(item.id))
          .map((item) => item.id);
      }
    } catch (error) {
      console.error('Failed to fetch content:', error);
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  const handleSelect = (item) => {
    const index = selectedItems.value.indexOf(item.id);
    if (index > -1) {
      selectedItems.value.splice(index, 1);
      if (formModel.value.is_across_select) {
        allSelectedItems.value.delete(item.id);
      }
    } else {
      selectedItems.value.push(item.id);
      if (formModel.value.is_across_select) {
        allSelectedItems.value.add(item.id);
      }
    }
  };

  const handleSelectAll = (val: boolean) => {
    if (formModel.value.is_across_select) {
      if (val) {
        contentList.value.forEach((item) => {
          allSelectedItems.value.add(item.id);
        });
      } else {
        contentList.value.forEach((item) => {
          allSelectedItems.value.delete(item.id);
        });
      }
      selectedItems.value = val ? contentList.value.map((item) => item.id) : [];
    } else {
      selectedItems.value = val ? contentList.value.map((item) => item.id) : [];
    }
  };

  const handleSearch = () => {
    formModel.value.page = 1;
    fetchContent();
  };

  const handleTypeChange = () => {
    formModel.value.page = 1;
    fetchContent();
  };

  const handlePageChange = (page: number) => {
    formModel.value.page = page;
    fetchContent();
  };

  const handlePageSizeChange = (size: number) => {
    formModel.value.pageSize = size;
    formModel.value.page = 1;
    fetchContent();
  };

  const handleDownload = async () => {
    if (!selectedItems.value.length || !localRecord.value?.id) return;

    try {
      let sendData = {
        id: localRecord.value.id,
        content_ids: selectedItems.value,
        is_across_select: formModel.value.is_across_select,
      };
      if (formModel.value.is_across_select) {
        delete sendData.content_ids;
      }
      await createAndPollDownload('/api/collect/batchDownload', sendData, {
        pollingInterval: 1000,
        onError: (error) => {
          console.error('Failed to download content:', error);
        },
      });
    } catch (error) {
      console.error('Failed to download content:', error);
    }
  };

  // Expose methods to parent component
  defineExpose({
    show: (record) => {
      if (record) {
        localRecord.value = record;
        fetchContent();
      }
      visible.value = true;
    },
  });
</script>

<style scoped lang="less">
  .collect-content-preview {
    display: flex;
    flex-direction: column;
    height: 70vh;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .header-left {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 6px;
        height: 100%;
        .selection-info {
          margin-top: 2px;
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .select-tip {
          display: flex;
          align-items: center;
          gap: 5px;
          color: var(--color-primary-6);
          font-size: 12px;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
      }
    }

    .preview-content {
      flex: 1;
      overflow-y: auto;
      padding: 0 2px;
      width: 100%;

      .content-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(228px, 1fr));
        gap: 12px;
        padding: 6px;
        width: 100%;
      }
    }

    .preview-footer {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid var(--color-border-2);
      display: flex;
      justify-content: flex-end;
    }
  }
</style>

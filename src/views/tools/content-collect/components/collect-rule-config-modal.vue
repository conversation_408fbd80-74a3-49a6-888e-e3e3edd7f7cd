<template>
  <d-modal
    :visible="visible"
    :title="typeTitleAll"
    width="560px"
    unmount-on-close
    :esc-to-close="false"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <!-- 表单 -->
    <a-form
      ref="formRef"
      :model="formModel"
      :rules="formRules"
      :auto-label-width="true"
    >
      <a-form-item v-if="typeTitle !== '手动'" label="开关" field="state">
        <a-switch
          v-model="formModel.state"
          :checked-value="1"
          :unchecked-value="0"
        />
      </a-form-item>
      <!-- 账号 -->
      <a-form-item label="采集账号" field="accounts" validate-trigger="blur">
        <account-select v-model="formModel.accounts" placeholder="请选择账号" />
      </a-form-item>
      <!-- 采集频次 -->
      <a-form-item
        v-if="typeTitle !== '手动'"
        label="采集频次"
        field="collect_frequency_day"
      >
        <div class="line-flex">
          每间隔
          <a-input-number
            v-model="formModel.collect_frequency_day"
            class="input-number"
            size="small"
            placeholder="请输入"
            :min="1"
            :max="1440"
          />
          天采集一次，采集的时间
          <a-time-picker
            v-model="formModel.collect_frequency_time"
            size="small"
            format="HH:mm"
            style="width: 120px"
            class="ml-5"
          />
        </div>
      </a-form-item>
      <!-- 采集模式 -->
      <a-form-item label="采集模式" field="collect_mod" validate-trigger="blur">
        <div class="type-wrap">
          <a-radio-group v-model="formModel.collect_mod" type="button">
            <a-radio :value="3"
              >增量更新
              <a-tooltip content="将会增量更新7天内发布的所有内容">
                <icon-info-circle />
              </a-tooltip>
            </a-radio>
            <a-radio :value="1">按条数</a-radio>
            <a-radio :value="2">按时间</a-radio>
          </a-radio-group>
          <div v-if="formModel.collect_mod === 1" class="line-flex mt-10">
            采集最新
            <a-input-number
              v-model="formModel.collect_mod_value"
              class="input-number"
              size="small"
              placeholder="请输入"
              :min="1"
            />条内容
          </div>
          <div v-if="formModel.collect_mod === 2" class="line-flex mt-10">
            采集最近
            <a-input-number
              v-model="formModel.collect_mod_value"
              class="input-number"
              size="small"
              placeholder="请输入"
              :min="1"
            />天发布的内容
          </div>
        </div>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script lang="ts" setup>
  import DModal from '@/components/d-modal/d-modal.vue';
  import { computed, nextTick, reactive, ref } from 'vue';
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import request from '@/api/request';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import VideoContentItem from '@/views/account/content-manage/video-content/video-content-item.vue';
  import ImageContentItem from '@/views/account/content-manage/image-content/image-content-item.vue';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import ImageCardItem from '@/views/account/material-manage/components/image-card-item.vue';
  import { Message } from '@arco-design/web-vue';
  import AccountSelect from '@/views/tools/content-collect/components/account-select.vue';
  import type { FormInstance } from '@arco-design/web-vue';

  const visible = ref(false);
  const info = ref<any>({});
  const formRef = ref<FormInstance>();
  const props = defineProps<{
    typeTitle: string | null;
  }>();
  const emit = defineEmits<{
    (e: 'refresh'): void;
  }>();

  const formRules = computed(() => {
    const baseRules = {
      accounts: [
        {
          required: true,
          message: '请选择采集账号',
          trigger: 'blur',
        },
      ],
      collect_mod: [
        {
          required: true,
          message: '请选择采集模式',
          trigger: 'blur',
        },
      ],
      collect_mod_value: [
        {
          required: true,
          message: '请输入采集数量',
          trigger: 'blur',
        },
      ],
    };

    if (props.typeTitle !== '手动') {
      return {
        ...baseRules,
        state: [
          {
            required: true,
            message: '请选择开关状态',
            trigger: 'blur',
          },
        ],
        collect_frequency_day: [
          {
            required: true,
            message: '请输入采集频次天数',
            trigger: 'blur',
          },
        ],
        collect_frequency_time: [
          {
            required: true,
            message: '请选择采集时间',
            trigger: 'blur',
          },
        ],
      };
    }

    return baseRules;
  });

  function defaultForm() {
    return {
      id: null,
      accounts: [],
      collect_frequency_day: null,
      collect_frequency_time: null,
      collect_mod: 1,
      collect_mod_value: null,
      state: 1,
    };
  }
  const formModel = ref(defaultForm());

  const typeTitleAll = computed(() => {
    return `${formModel.value.id ? '编辑' : '新增'}${
      props.typeTitle === '手动' ? '手动采集' : '自动采集规则'
    }`;
  });

  const show = async (dinfo: any) => {
    formModel.value = defaultForm();
    if (dinfo?.title) {
      // 切割字符串，"_"
      let account_id = dinfo.title.split('_').slice(-1)[0];
      const res = await request('/api/collect/accountList', {
        keywords: account_id,
      });
      if (res?.data) {
        formModel.value.accounts = res.data.data;
      }
    }
    Object.assign(formModel.value, dinfo);
    visible.value = true;
  };

  const editFn = (record: any) => {
    console.log(record);
  };

  let cancelToken: AbortController;
  async function handleOk() {
    const res = await formRef.value?.validate();
    if (!res) {
      cancelToken?.abort('重复请求取消');
      cancelToken = new AbortController();

      let API_URL = '/api/collect/autoSettingAddOrEdit';
      if (props.typeTitle === '手动') {
        API_URL = '/api/collect/manualCollect';
      }
      await request(
        API_URL,
        {
          ...formModel.value,
        },
        cancelToken.signal
      );
      Message.success('操作成功');
      visible.value = false;
      emit('refresh');
    }
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .line-flex {
    display: flex;
    align-items: center;
    .input-number {
      margin: 0 5px;
      width: 80px;
    }
    .custom-time-picker {
      margin-left: 10px !important;
    }
  }

  .account-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--color-fill-2);

    .account-item {
      height: 60px;
      width: 180px;
      display: flex;
      align-items: center;
      border: 1px solid var(--color-fill-2);
      border-radius: 6px;
      padding: 0 12px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &::before {
          opacity: 1;
        }

        .remove-btn {
          opacity: 1;
          transform: scale(1);
        }
      }

      .account-info {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;
        min-width: 0;

        .avatar-wrap {
          position: relative;
          width: 32px;
          height: 32px;
          flex-shrink: 0;

          .avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid var(--color-fill-3);
          }

          .platform-icon {
            position: absolute;
            right: -2px;
            bottom: -2px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 1px solid var(--color-fill-2);
            background: var(--color-fill-2);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          }
        }

        .account-detail {
          display: flex;
          flex-direction: column;
          gap: 2px;
          min-width: 0;

          .account-name {
            font-size: 13px;
            color: var(--color-text-1);
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .platform-name {
            font-size: 12px;
            color: var(--color-text-3);
            display: flex;
            align-items: center;
            gap: 4px;

            &::before {
              content: '';
              display: inline-block;
              width: 3px;
              height: 3px;
              background: var(--color-text-3);
              border-radius: 50%;
            }
          }
        }
      }

      .remove-btn {
        margin-left: 8px;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.2s ease;
        padding: 4px;
        border-radius: 4px;
        flex-shrink: 0;

        &:hover {
          background: rgb(var(--red-1));
          color: rgb(var(--red-6));
        }
      }
    }

    .add-account-btn {
      height: 60px;
      width: 180px;
      padding: 0 16px;
      border-style: dashed;
      border-width: 1.5px;
      border-color: rgb(var(--arcoblue-4));
      color: rgb(var(--arcoblue-6));
      display: flex;
      align-items: center;
      gap: 6px;
      border-radius: 6px;
      transition: all 0.2s ease;
      background: rgb(var(--arcoblue-1));
      font-weight: 500;

      &:hover {
        border-color: rgb(var(--arcoblue-5));
        color: rgb(var(--arcoblue-7));
        background: rgb(var(--arcoblue-2));
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .arco-icon {
        font-size: 16px;
      }
    }
  }

  .platform-radio {
    :deep(.arco-radio-button) {
      .platform-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .platform-icon-wrap {
        display: flex;
        align-items: center;
      }
    }
  }

  .modal-footer {
    margin-top: 24px;
    text-align: right;
  }
</style>

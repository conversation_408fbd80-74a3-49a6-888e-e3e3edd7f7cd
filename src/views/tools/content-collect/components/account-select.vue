<template>
  <div class="account-select">
    <div class="select-container">
      <a-popover
        :popup-visible="popupVisible"
        position="right"
        trigger="click"
        class="no-padding-popover"
        :popup-container="document.body"
        :popup-style="{ maxWidth: '100vw' }"
        @popup-visible-change="handlePopupVisibleChange"
      >
        <div class="account-list">
          <div class="account-items">
            <div
              v-for="(account, index) in selectedAccounts"
              :key="account.id"
              class="account-item"
            >
              <div class="account-info">
                <div class="avatar-wrap">
                  <img :src="account.profile_photo" class="avatar" />
                  <img
                    src="@/assets/images/instagram.png"
                    alt="Instagram"
                    class="platform-icon"
                  />
                </div>
                <div class="account-detail">
                  <span class="account-name">{{ account.account_name }}</span>
                  <span class="platform-name">{{ account.account_id }}</span>
                </div>
              </div>
              <a-button
                type="text"
                status="danger"
                size="mini"
                class="remove-btn"
                @click.stop="removeAccount(index)"
              >
                <icon-close />
              </a-button>
            </div>
          </div>
          <div v-if="!selectedAccounts.length" class="placeholder">
            请选择账号
          </div>
          <div class="action-btns">
            <icon-down class="dropdown-icon" />
          </div>
        </div>

        <template #content>
          <div class="account-dropdown">
            <div class="dropdown-header">
              <div class="header-left">
                <span class="title">选择账号</span>
                <a-button
                  v-if="selectedAccounts.length"
                  type="text"
                  size="small"
                  class="reset-btn"
                  @click.stop="resetAccounts"
                >
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </div>
              <a-input-search
                v-model="searchKeyword"
                placeholder="搜索账号"
                allow-clear
                size="small"
                class="search-input"
                @search="fetchAccounts"
                @clear="fetchAccounts"
                @keydown.enter="fetchAccounts"
              />
            </div>
            <div class="account-options">
              <a-spin :loading="loading" class="loading-wrapper">
                <template v-if="!loading && filteredAccounts.length">
                  <div
                    v-for="account in filteredAccounts"
                    :key="account.id"
                    class="account-option"
                    :class="{ 'is-selected': isAccountSelected(account) }"
                    @click="toggleAccount(account)"
                  >
                    <div class="account-info">
                      <div class="avatar-wrap">
                        <img :src="account.profile_photo" class="avatar" />
                        <img
                          src="@/assets/images/instagram.png"
                          alt="Instagram"
                          class="platform-icon"
                        />
                      </div>
                      <div class="account-detail">
                        <span class="account-name">{{
                          account.account_name
                        }}</span>
                        <span class="platform-name">{{
                          account.account_id
                        }}</span>
                      </div>
                    </div>
                    <icon-check
                      v-if="isAccountSelected(account)"
                      class="check-icon"
                    />
                  </div>
                </template>
                <a-empty
                  v-else-if="!loading && !filteredAccounts.length"
                  description="暂无账号"
                />
              </a-spin>
            </div>
            <div class="dropdown-footer">
              <a-button
                type="text"
                class="add-account-btn"
                size="small"
                @click="showAddAccount"
              >
                <icon-plus />
                添加新账号
              </a-button>
            </div>
          </div>
        </template>
      </a-popover>
    </div>

    <!-- 添加账号弹窗 -->
    <add-account-modal ref="accountModalRef" @confirm="handleAccountConfirm" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import request from '@/api/request';
  import AddAccountModal from './add-account-modal.vue';

  interface Account {
    id: string;
    account: string;
    platform: string;
    avatar: string;
    name: string;
    status: number;
  }

  const props = defineProps<{
    modelValue: Account[];
  }>();

  const emit = defineEmits<{
    (e: 'update:modelValue', value: Account[]): void;
  }>();

  const popupVisible = ref(false);
  const accountModalRef = ref();
  const accountList = ref<Account[]>([]);
  const loading = ref(false);
  const searchKeyword = ref('');

  const selectedAccounts = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  const filteredAccounts = computed(() => {
    return accountList.value;
  });

  const isAccountSelected = (account: Account) => {
    return selectedAccounts.value.some((item) => item.id === account.id);
  };

  const toggleAccount = (account: Account) => {
    const newValue = [...selectedAccounts.value];
    const index = newValue.findIndex((item) => item.id === account.id);
    if (index > -1) {
      newValue.splice(index, 1);
    } else {
      newValue.push(account);
    }
    selectedAccounts.value = newValue;
  };

  // 获取账号列表
  const fetchAccounts = async () => {
    loading.value = true;
    try {
      const res = await request('/api/collect/accountList', {
        keywords: searchKeyword.value,
      });
      if (res?.data) {
        accountList.value = res.data.data;
      }
      loading.value = false;
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchAccounts();
  });

  const handlePopupVisibleChange = (visible: boolean) => {
    popupVisible.value = visible;
    if (visible) {
      fetchAccounts();
    }
  };

  const removeAccount = (index: number) => {
    const newValue = [...selectedAccounts.value];
    newValue.splice(index, 1);
    selectedAccounts.value = newValue;
  };

  const showAddAccount = () => {
    accountModalRef.value?.show();
  };

  const handleAccountConfirm = (account: Account) => {
    if (!isAccountSelected(account)) {
      selectedAccounts.value = [...selectedAccounts.value, account];
    }
  };

  const resetAccounts = () => {
    selectedAccounts.value = [];
  };

  const { document } = window;
</script>

<style scoped lang="less">
  .account-select {
    width: 100%;
  }
  .select-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }

  .account-list {
    display: flex;
    padding: 8px 12px;
    border-radius: var(--border-radius-large);
    min-height: 32px;
    background: var(--color-fill-2);
    position: relative;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;

    .action-btns {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: auto;
      padding-left: 12px;
      border-left: 1px solid var(--color-fill-2);

      .reset-btn {
        color: var(--color-text-3);
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 4px;
        transition: all 0.2s ease;
        float: right;

        &:hover {
          color: var(--color-text-2);
          background: var(--color-fill-2);
        }
      }

      .dropdown-icon {
        color: var(--color-text-3);
        font-size: 14px;
        transition: transform 0.2s ease;
      }
    }

    .placeholder {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--color-text-2);
      font-size: 14px;
    }

    .account-items {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      min-width: 0;
    }

    .account-item {
      height: 52px;
      width: 180px;
      display: flex;
      align-items: center;
      border: 1px solid var(--color-fill-2);
      border-radius: var(--border-radius-large);
      padding: 0 10px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      background: var(--color-bg-2);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .account-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;

        .avatar-wrap {
          position: relative;
          width: 28px;
          height: 28px;
          flex-shrink: 0;

          .avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid var(--color-fill-3);
          }

          .platform-icon {
            position: absolute;
            right: -2px;
            bottom: -2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid var(--color-fill-2);
            background: var(--color-fill-2);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          }
        }

        .account-detail {
          display: flex;
          flex-direction: column;
          gap: 1px;
          min-width: 0;

          .account-name {
            font-size: 12px;
            color: var(--color-text-1);
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .platform-name {
            font-size: 11px;
            color: var(--color-text-3);
            display: flex;
            align-items: center;
            gap: 3px;
          }
        }
      }

      .remove-btn {
        margin-left: 6px;
        padding: 3px;
        border-radius: 4px;
        flex-shrink: 0;
        opacity: 0.6;
        transition: all 0.2s ease;

        &:hover {
          opacity: 1;
          background: rgb(var(--red-1));
          color: rgb(var(--red-6));
        }
      }
    }

    &:hover {
      border-color: var(--color-fill-3);
    }
  }

  .account-dropdown {
    width: 320px;
    max-height: 360px;
    display: flex;
    flex-direction: column;
    background: var(--color-bg-2);
    border-radius: 8px;
    overflow: hidden;

    .dropdown-header {
      padding: 5px 5px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      background: var(--color-bg-2);

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: space-between;

        .title {
          font-size: 13px;
          font-weight: 500;
          color: var(--color-text-1);
        }

        .reset-btn {
          color: var(--color-text-3);
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            color: var(--color-text-2);
            background: var(--color-fill-2);
          }
        }
      }

      .search-input {
        width: 100%;
      }
    }

    .account-options {
      flex: 1;
      overflow-y: auto;
      padding: 0 8px 8px;
      background: var(--color-bg-2);

      .loading-wrapper {
        width: 100%;
        min-height: 100px;
        display: flex;
        flex-direction: column;
      }

      .empty-tip {
        padding: 24px 0;
        text-align: center;
        color: var(--color-text-3);
        font-size: 12px;
      }

      .account-option {
        height: 52px;
        padding: 0 10px;
        margin: 2px 0;
        border-radius: var(--border-radius-large);
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: all 0.2s ease;
        background: var(--color-fill-1);

        &:hover {
          background: var(--color-fill-2);
        }

        &.is-selected {
          background: rgb(var(--primary-1));

          .account-name {
            color: rgb(var(--primary-6));
          }
        }

        .account-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          min-width: 0;

          .avatar-wrap {
            position: relative;
            width: 28px;
            height: 28px;
            flex-shrink: 0;

            .avatar {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
              border: 1px solid var(--color-fill-3);
            }

            .platform-icon {
              position: absolute;
              right: -2px;
              bottom: -2px;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 1px solid var(--color-fill-2);
              background: var(--color-fill-2);
              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            }
          }

          .account-detail {
            display: flex;
            flex-direction: column;
            gap: 1px;
            min-width: 0;

            .account-name {
              font-size: 12px;
              color: var(--color-text-1);
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .platform-name {
              font-size: 11px;
              color: var(--color-text-3);
              display: flex;
              align-items: center;
              gap: 3px;
            }
          }
        }

        .check-icon {
          color: rgb(var(--primary-6));
          font-size: 14px;
        }
      }
    }

    .dropdown-footer {
      padding: 8px 12px;
      border-top: 1px solid var(--color-fill-2);
      display: flex;
      justify-content: center;
      background: var(--color-bg-2);

      .add-account-btn {
        color: rgb(var(--primary-6));
        font-weight: 500;
        font-size: 12px;
        padding: 4px 12px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: rgb(var(--primary-7));
          background: rgb(var(--primary-1));
        }

        .arco-icon {
          font-size: 14px;
        }
      }
    }
  }

  .add-account-btn {
    border-style: dashed;
    border-width: 1px;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    font-size: 12px;
    flex-shrink: 0;

    .arco-icon {
      font-size: 14px;
    }
  }
</style>

<template>
  <a-modal
    :visible="visible"
    :title="accountForm.id ? '编辑账号' : '添加账号'"
    :mask-closable="false"
    :footer="false"
    width="360px"
    @cancel="handleCancel"
    @update:visible="handleVisibleChange"
  >
    <a-form :model="accountForm" auto-label-width>
      <a-form-item field="platform" label="平台">
        <a-radio-group
          v-model="accountForm.platform"
          type="button"
          class="platform-radio"
          :disabled="accountForm.id"
        >
          <a-radio value="ins">
            <span class="platform-icon-wrap">
              <img
                src="@/assets/images/instagram.png"
                alt="Instagram"
                class="platform-icon"
              />
              Instagram
            </span>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="account_id" label="账号ID">
        <a-input
          v-model="accountForm.account_id"
          placeholder="请输入账号ID"
          :disabled="accountForm.id"
          allow-clear
        />
      </a-form-item>
      <!-- 产品 -->
      <a-form-item field="product_id" label="产品">
        <a-input-group style="width: 100%">
          <request-select
            ref="productSelectRef"
            v-model="accountForm.product_id"
            api="product"
          />
          <a-button type="text" @click="showAddProduct">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
        </a-input-group>
      </a-form-item>

      <a-form-item field="sync_switch" label="自动采集">
        <a-switch
          v-model="accountForm.sync_switch"
          :checked-value="1"
          :unchecked-value="-1"
        >
          <template #checked> 开启 </template>
          <template #unchecked> 关闭 </template>
        </a-switch>
        <a-tooltip content="系统每日8:00，自动采集7天内发布的所有内容">
          <icon-info-circle class="ml-5" />
        </a-tooltip>
      </a-form-item>
      <div class="modal-footer">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="loading" @click="handleConfirm">
            {{ loading ? '自动添加中...' : '确定' }}
          </a-button>
        </a-space>
      </div>
    </a-form>
    <product-manage-edit ref="addProductRef" @save="saveNewProduct" />
  </a-modal>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import request from '@/api/request';
  import RequestSelect from '@/components/select/request-select.vue';
  import ProductManageEdit from '@/views/manage/product-manage/product-manage-edit.vue';

  const emit = defineEmits<{
    (
      e: 'confirm',
      account: {
        platform: string;
        account_id: string;
        profile_photo: string;
        account_name: string;
      }
    ): void;
    (e: 'refresh'): void;
  }>();

  const visible = ref(false);
  const loading = ref(false);
  const accountForm = reactive({
    platform: 'ins',
    account_id: '',
    profile_photo: '',
    account_name: '',
    sync_switch: -1,
  });

  const resetForm = () => {
    accountForm.platform = 'ins';
    accountForm.account_id = '';
    accountForm.product_id = '';
    accountForm.id = null;
  };

  const handleVisibleChange = (value: boolean) => {
    visible.value = value;
  };

  const handleCancel = () => {
    resetForm();
    visible.value = false;
  };

  function addAccount() {
    if (!accountForm.account_id) {
      Message.warning('请输入账号ID');
      return;
    }

    loading.value = true;
    request('/api/collect/addOrEditAccount', {
      ...accountForm,
    })
      .then((res) => {
        if (res.code === 0) {
          Message.success(res.msg || '操作成功');
          emit('confirm', {
            platform: accountForm.platform,
            account_id: accountForm.account_id,
            profile_photo: accountForm.profile_photo,
            account_name: accountForm.account_name,
          });
          emit('refresh');
          handleCancel();
        }
      })
      .finally(() => {
        loading.value = false;
        resetForm();
      });
  }

  // 先获取账号信息
  async function getAccountInfo() {
    loading.value = true;

    const res = await request('/api/collect/getAccountInfo', {
      platform: accountForm.platform,
      account_id: accountForm.account_id,
    });
    console.log(res);
    if (res.code === 0) {
      Object.assign(accountForm, res.data);
      loading.value = false;
      return true;
    }
    loading.value = false;

    Message.error(res.message);
    return false;
  }

  const handleConfirm = async () => {
    if (!accountForm.account_id) {
      Message.warning('请输入账号ID');
      return;
    }
    // 获取账号信息
    if (!accountForm.id) {
      await getAccountInfo();
    }
    await addAccount();
  };

  const productSelectRef = ref();
  const addProductRef = ref();
  const showAddProduct = () => {
    addProductRef.value?.show();
  };
  const saveNewProduct = (productInfo: any) => {
    productSelectRef.value?.getList();
    accountForm.product_id = productInfo.id;
  };

  const show = (record: any) => {
    visible.value = true;
    loading.value = false;
    if (record) {
      Object.assign(accountForm, record);
    } else {
      resetForm();
    }
  };

  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .platform-radio {
    :deep(.arco-radio-button) {
      .platform-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .platform-icon-wrap {
        display: flex;
        align-items: center;
      }
    }
  }

  .modal-footer {
    margin-top: 24px;
    text-align: right;
  }
</style>

<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.last_state"
            :data-list="[
              { label: '全部', value: '' },
              ...collectAccountStatusM,
            ]"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.keywords"
            placeholder="请输入账号ID或名称"
            allow-clear
            class="w-300"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
            @clear="handleSubmit()"
          />
        </a-space>
        <a-space>
          <!-- 新建 -->
          <a-button
            type="primary"
            status="success"
            @click="showAddAccountModal()"
          >
            <template #icon>
              <icon-plus />
            </template>
            添加账号
          </a-button>
        </a-space>
      </div>
      <!-- 提示区域 -->
      <div class="hint-message">
        <div class="hint-icon">
          <icon-info-circle />
        </div>
        <div class="hint-content">
          <div class="hint-text"
            >采集的内容将自动存储于内容库中，您可以在内容库中查看、编辑已采集的内容。支持自动采集和手动采集两种方式。</div
          >
        </div>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="table-card"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <!-- 产品 -->
        <template #product_name="{ record }: TableColumnSlot">
          <div class="product-name-wrap" @click="showAddAccountModal(record)">
            <icon-apps v-if="record.product_name" />
            <span>{{ record.product_name || '-' }}</span>
            <icon-edit
              :class="
                record.product_name ? 'edit-icon primary_text' : 'primary_text'
              "
            />
          </div>
        </template>
        <template #account_id="{ record }: TableColumnSlot">
          <account-display :account="record" />
        </template>
        <template #last_state_text="{ record }: TableColumnSlot">
          <status-badge
            :list="collectAccountStatusM"
            :value="record.last_state"
          />
        </template>
        <template #platform_text="{ record }: TableColumnSlot">
          <div class="platform-wrap">
            <img
              src="@/assets/images/instagram.png"
              alt="Instagram"
              class="platform-icon"
            />
            <span>{{ record.platform_text }}</span>
          </div>
        </template>
        <template #sync_switch="{ record }: TableColumnSlot">
          <a-switch
            :loading="record.loading"
            :model-value="record.sync_switch === 1"
            @change="updateState(record)"
          >
            <template #checked> 开启 </template>
            <template #unchecked> 关闭 </template>
          </a-switch>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space size="mini" class="action-buttons">
            <a-button
              type="text"
              size="small"
              class="action-btn"
              @click="createFn(record)"
            >
              <icon-tool />
              手动采集
            </a-button>
            <a-badge
              :count="record.is_show_red_point ? record.new_content_num : 0"
              :offset="[-2, 2]"
            >
              <a-tooltip content="查看采集记录">
                <a-button
                  type="text"
                  size="small"
                  class="action-btn"
                  @click="showTableManageLog(record)"
                >
                  <icon-history />采集记录
                  <span v-if="record.result_num" class="result-num"
                    >({{ record.result_num }})</span
                  >
                </a-button>
              </a-tooltip>
            </a-badge>
            <a-popconfirm
              content="请确认是否删除此账号？"
              position="left"
              @ok="deleteFn(record)"
            >
              <a-button
                type="text"
                size="small"
                class="action-btn delete-btn"
                status="danger"
              >
                <icon-delete />删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <collect-rule-list-detail ref="detailRef" />
    <collect-handle-config-modal
      ref="handleConfigModalRef"
      type-title="手动"
      @refresh="handleSubmit()"
    />
    <collect-content-preview-modal ref="contentPreviewModal" />
    <table-manage-log ref="tableManageLogRef" />
    <!-- 添加账号弹窗 -->
    <add-account-modal ref="accountModalRef" @refresh="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { collectAccountStatusM } from '@/components/dict-select/dict-account';
  import { TableColumnSlot } from '@/global';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import CollectRuleListDetail from '@/views/tools/content-collect/collect-rule-list-detail.vue';
  import CollectHandleConfigModal from '@/views/tools/content-collect/components/collect-handle-config-modal.vue';
  import AccountDisplay from '@/components/account-display/account-display.vue';
  import { useDataCacheStore, useAppStore, useUserStore } from '@/store';
  import AddAccountModal from '@/views/tools/content-collect/components/add-account-modal.vue';
  import { Message } from '@arco-design/web-vue';
  import CollectContentPreviewModal from './components/collect-content-preview-modal.vue';
  import TableManageLog from './table-manage-log.vue';

  const init = ref(1);
  const generateFormModel = () => {
    return {
      last_state: null,
      keywords: null,
      init,
    };
  };

  const dataCache = useDataCacheStore();

  const loading = ref(false);

  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    init.value = 0;
    return request('/api/collect/accountList', {
      ...data,
    });
  };

  const handleConfigModalRef = ref();
  const detailRef = ref();
  const createFn = (record: any) => {
    handleConfigModalRef.value?.show(record);
  };

  const accountModalRef = ref();
  const showAddAccountModal = (record) => {
    accountModalRef.value?.show(record);
  };

  // 采集记录
  const tableManageLogRef = ref();
  const showTableManageLog = (record: any) => {
    record.is_show_red_point = false;
    tableManageLogRef.value?.show(record);
  };

  const columns = [
    {
      title: '账号',
      dataIndex: 'account_id',
      fixed: 'left',
      maxWidth: 180,
    },
    // {
    //   title: '平台',
    //   dataIndex: 'platform_text',
    //   slotName: 'platform_text',
    // },
    // 产品
    {
      title: '产品',
      dataIndex: 'product_name',
      slotName: 'product_name',
    },
    // 自动采集开关
    {
      title: '自动采集',
      dataIndex: 'sync_switch',
      slotName: 'sync_switch',
      align: 'center',
      ellipsis: true,
      description: '系统每日8:00，自动采集7天内发布的所有内容',
    },
    {
      title: '最新采集时间',
      dataIndex: 'last_created_at',
      align: 'center',
    },
    {
      title: '当前采集状态',
      dataIndex: 'last_state_text',
      slotName: 'last_state_text',
      align: 'center',
      width: 130,
    },
    {
      title: '添加日期',
      dataIndex: 'created_at',
      align: 'center',
    },
    {
      title: '添加人',
      dataIndex: 'create_user_name',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 120,
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  const contentPreviewModal = ref(null);

  const handlePreview = (record) => {
    record.new_content_flag = 2;
    contentPreviewModal.value.show(record);
  };

  function updateState(record: any) {
    record.loading = true;
    request('/api/collect/addOrEditAccount', {
      ...record,
      sync_switch: record.sync_switch === 1 ? -1 : 1,
    })
      .then(() => {
        record.sync_switch = record.sync_switch === 1 ? -1 : 1;
      })
      .finally(() => {
        record.loading = false;
      });
  }

  function deleteFn(record: any) {
    request('/api/collect/deleteAccount', {
      ...record,
      ids: [record.id],
    })
      .then(() => {
        Message.success('删除成功');
        theTable.value?.search();
      })
      .finally(() => {
        record.loading = false;
      });
  }
</script>

<style scoped lang="less">
  .progress-box {
    display: flex;
    flex-direction: column;
  }
  .progress-box-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
    .progress-box-info-item {
      margin-right: 10px;
    }
  }
  .progress-bar {
    cursor: pointer;
  }

  :deep(.arco-table-tr-empty) {
    height: 65vh !important;
  }

  .hint-message {
    display: flex;
    align-items: flex-start;
    padding: 8px 12px;
    background: linear-gradient(
      to right,
      rgb(var(--arcoblue-1)),
      rgb(var(--arcoblue-1))
    );
    border-radius: var(--border-radius-large);
    margin-bottom: 12px;

    .hint-icon {
      margin-right: 6px;
      color: rgb(var(--arcoblue-6));
      font-size: 14px;
      margin-top: 1px;
    }

    .hint-content {
      flex: 1;

      .hint-text {
        font-size: 12px;
        line-height: 1.4;
        color: rgb(var(--gray-8));
      }
    }
  }

  .platform-wrap {
    display: flex;
    align-items: center;
    gap: 4px;
    .platform-icon {
      width: 18px;
      height: 18px;
    }
  }

  .source-text-wrap {
    display: flex;
    flex-direction: column;

    .source-text {
      font-size: 13px;
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 4px;

    .action-btn {
      padding: 2px 4px;
      height: 24px;
      display: flex;
      align-items: center;
      gap: 2px;

      .result-num {
        font-size: 12px;
        color: var(--color-text-3);
      }

      &.delete-btn {
        &:hover {
          background-color: var(--color-danger-light-1);
        }
      }
    }
  }

  .product-name-wrap {
    display: flex;
    align-items: center;
    gap: 4px;
    color: rgb(var(--primary-6));
    cursor: pointer;
    // font-size: 12px;

    .edit-icon {
      opacity: 0;
    }
    &:hover {
      .edit-icon {
        opacity: 1;
      }
    }
  }
</style>

<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :width="1180"
    :footer="false"
    @cancel="handleClose"
  >
    <div>
      <a-card size="small" class="table-card">
        <div class="table-card-header">
          <a-space>
            <a-button type="primary" @click="theTable?.fetchData()">
              <template #icon>
                <icon-refresh />
              </template>
              <span>刷新</span>
            </a-button>
            <dict-radio
              v-model="formModel.state"
              :data-list="[{ label: '全部', value: '' }, ...collectStatusM]"
              @change="handleSubmit()"
            />
            <a-input-search
              v-model="formModel.account_id"
              placeholder="请输入账号ID或名称"
              allow-clear
              class="w-300"
              @search="handleSubmit()"
              @keydown.enter="handleSubmit()"
            />
          </a-space>
          <a-space> </a-space>
        </div>
        <!--table 区域-->
        <base-table
          ref="theTable"
          v-model:loading="loading"
          class="table-card"
          :columns-config="columns"
          :scroll-percent="{ x: 900, y: '75vh' }"
          :data-config="getList"
          :send-params="formModel"
          :auto-request="false"
        >
          <template #task_batch_id="{ record }">
            <div>
              <div>
                {{ record.task_batch_id }}
              </div>
              <div v-if="record.source == 2" class="source-text-wrap">
                <span class="source-text">
                  <icon-mind-mapping />
                  自动
                </span>
              </div>
              <div v-else class="source-text-wrap">
                <span class="source-text">
                  <icon-tool />
                  手动
                </span>
              </div>
            </div>
          </template>
          <template #account_id="{ record }: TableColumnSlot">
            <account-display :account="record" />
          </template>
          <template #state_text="{ record }: TableColumnSlot">
            <status-badge :list="collectStatusM" :value="record.state" />
          </template>
          <template #platform_text="{ record }: TableColumnSlot">
            <div class="platform-wrap">
              <img
                src="@/assets/images/instagram.png"
                alt="Instagram"
                class="platform-icon"
              />
              <span>{{ record.platform_text }}</span>
            </div>
          </template>
          <template #source_text="{ record }: TableColumnSlot">
            <div v-if="record.auto_setting_id" class="source-text-wrap">
              <span class="source-text">
                <icon-mind-mapping />
                自动采集
              </span>
            </div>
            <div v-else class="source-text-wrap">
              <span class="source-text">
                <icon-tool />
                手动采集
              </span>
            </div>
          </template>
          <template #action="{ record }: TableColumnSlot">
            <a-badge
              v-if="record.account_id"
              :count="record.new_content_flag == 1 ? 1 : 0"
              dot
              :offset="[-6, 4]"
            >
              <a-space>
                <a-tooltip
                  :content="`${
                    !record.result_num
                      ? '本次采集没有新增内容'
                      : '本次采集新增' + record.result_num + '条内容'
                  }`"
                >
                  <a-button
                    type="text"
                    :disabled="!record.result_num"
                    size="mini"
                    @click="handlePreview(record)"
                  >
                    <icon-eye />查看内容
                    <span> ({{ record.result_num }}) </span>
                  </a-button>
                </a-tooltip>
              </a-space>
            </a-badge>
          </template>
        </base-table>
      </a-card>
      <collect-content-preview-modal ref="contentPreviewModal" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, nextTick } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { collectStatusM } from '@/components/dict-select/dict-account';
  import { TableColumnSlot } from '@/global';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import AccountDisplay from '@/components/account-display/account-display.vue';
  import { useDataCacheStore, useAppStore, useUserStore } from '@/store';
  import DModal from '@/components/d-modal/d-modal.vue';
  import CollectContentPreviewModal from './components/collect-content-preview-modal.vue';

  const generateFormModel = () => {
    return {
      state: null,
      account_id: null,
    };
  };

  const dataCache = useDataCacheStore();

  const visible = ref(false);
  const title = ref('采集记录');

  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/collect/taskList', {
      ...data,
    });
  };

  const columns = [
    {
      title: '批次号',
      dataIndex: 'task_batch_id',
      width: 220,
      fixed: 'left',
      slotName: 'task_batch_id',
    },
    {
      title: '账号',
      dataIndex: 'account_id',
      width: 200,
      fixed: 'left',
    },
    // {
    //   title: '平台',
    //   dataIndex: 'platform_text',
    //   slotName: 'platform_text',
    // },

    // {
    //   title: '来源',
    //   dataIndex: 'source_text',
    //   slotName: 'source_text',
    //   align: 'center',
    // },
    {
      title: '采集人',
      dataIndex: 'create_user_name',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 180,
      align: 'center',
    },
    {
      title: '完成时间',
      dataIndex: 'complete_at',
      align: 'center',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'state_text',
      slotName: 'state_text',
      width: 90,
      fixed: 'right',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 120,
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  const contentPreviewModal = ref(null);

  const handlePreview = (record) => {
    record.new_content_flag = 2;
    contentPreviewModal.value.show(record);
  };

  const handleClose = () => {
    visible.value = false;
  };

  const accountInfo = ref({});
  const show = (record: any) => {
    visible.value = true;
    title.value = `采集记录`;
    formModel.account_id = record.account_id;
    Object.assign(accountInfo.value, record);
    nextTick(() => {
      handleSubmit({});
    });
  };
  defineExpose({
    show,
  });
</script>

<style scoped lang="less">
  .progress-box {
    display: flex;
    flex-direction: column;
  }
  .progress-box-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
    .progress-box-info-item {
      margin-right: 10px;
    }
  }
  .progress-bar {
    cursor: pointer;
  }

  :deep(.arco-table-tr-empty) {
    height: 65vh !important;
  }

  .hint-message {
    display: flex;
    align-items: flex-start;
    padding: 8px 12px;
    background: linear-gradient(
      to right,
      rgb(var(--arcoblue-1)),
      rgb(var(--arcoblue-1))
    );
    border-radius: var(--border-radius-large);
    margin-bottom: 12px;

    .hint-icon {
      margin-right: 6px;
      color: rgb(var(--arcoblue-6));
      font-size: 14px;
      margin-top: 1px;
    }

    .hint-content {
      flex: 1;

      .hint-text {
        font-size: 12px;
        line-height: 1.4;
        color: rgb(var(--gray-8));
      }
    }
  }

  .platform-wrap {
    display: flex;
    align-items: center;
    gap: 4px;
    .platform-icon {
      width: 20px;
      height: 20px;
      border-radius: 50%;
    }
  }

  .source-text-wrap {
    display: flex;
    flex-direction: column;
    color: var(--color-text-3);
    font-size: 12px;

    .source-text {
      font-size: 13px;
    }
  }
</style>

<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.status"
            :data-list="[
              { label: '全部', value: '' },
              ...downloadCenterStatusM,
            ]"
            @change="handleSubmit()"
          />
        </a-space>
        <a-space> </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="table-card"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <template #progress="{ record }: TableColumnSlot">
          <div class="progress-box" @click="detailRef?.show(record)">
            <div class="progress-box-info">
              <span
                class="progress-box-info-item"
                style="color: rgb(var(--green-4))"
              >
                已完成 {{ record.complete_num }}
              </span>
              <!-- <span class="mr-5"> / </span> -->
              <span
                class="progress-box-info-item"
                style="color: rgb(var(--arcoblue-6))"
              >
                共 {{ record.total_row }}个
              </span>
            </div>
            <a-progress
              class="progress-bar"
              :steps="Math.min(record.total_row, 30)"
              :percent="Number(record.progress_rate) / 100"
              size="large"
              :style="{ width: '100%' }"
            />
          </div>
        </template>
        <template #status="{ record }: TableColumnSlot">
          <status-badge :list="downloadCenterStatusM" :value="record.status" />
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-button
              type="text"
              size="small"
              :disabled="!record.file_url"
              @click="
                downloadProgressFile(record.file_url, null, {
                  total: 1,
                  current: 1,
                })
              "
            >
              <icon-download />下载
            </a-button>
          </a-space>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { downloadCenterStatusM } from '@/components/dict-select/dict-account';
  import { TableColumnSlot } from '@/global';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import AccountInfo from '@/components/account-info/account-info.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import { downloadProgressFile } from '@/utils/table-utils/table-util';

  const generateFormModel = () => {
    return {
      status: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/downloadCenter/list', {
      ...data,
    });
  };

  const detailRef = ref();

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      align: 'center',
    },
    {
      title: '来源模块',
      dataIndex: 'source_module',
    },
    {
      title: '进度',
      dataIndex: 'progress',
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'create_user_name',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      align: 'center',
    },
    {
      title: '完成时间',
      dataIndex: 'end_time',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 120,
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  // 刷新数据
  const refreshData = async () => {
    try {
      console.log('🔄 开始刷新下载中心数据');
      await theTable.value?.fetchData();
      console.log('✅ 下载中心页面数据刷新完成');
    } catch (error) {
      console.error('❌ 下载中心页面数据刷新失败:', error);
    }
  };

  // 全局刷新事件处理器
  const handleGlobalRefresh = () => {
    console.log('🔄 收到全局刷新事件，开始刷新下载中心数据');
    refreshData();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }

  // 组件挂载时添加事件监听器
  onMounted(() => {
    // 监听来自导航栏的刷新事件
    window.addEventListener('refresh-download-center', handleGlobalRefresh);
    console.log('📡 下载中心页面已注册全局刷新事件监听器');
  });

  // 组件卸载时移除事件监听器
  onBeforeUnmount(() => {
    window.removeEventListener('refresh-download-center', handleGlobalRefresh);
    console.log('🗑️ 下载中心页面已移除全局刷新事件监听器');
  });
</script>

<style scoped lang="less">
  .progress-box {
    display: flex;
    flex-direction: column;
  }
  .progress-box-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
    .progress-box-info-item {
      margin-right: 10px;
    }
  }
  .progress-bar {
    cursor: pointer;
  }

  :deep(.arco-table-tr-empty) {
    height: 75vh !important;
  }
</style>

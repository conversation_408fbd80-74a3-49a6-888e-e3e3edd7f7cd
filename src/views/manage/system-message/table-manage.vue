<template>
  <div>
    <!-- 搜索和操作区域 -->
    <a-card size="small" class="search-card">
      <div class="search-header">
        <a-space>
          <a-button type="primary" @click="refreshData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.platform"
            :data-list="contactWayListM"
            show-icon
            :show-all="true"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.account"
            placeholder="请输入账户昵称或ID"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
        </a-space>
      </div>
    </a-card>

    <!-- 卡片展示区域 -->
    <div class="cards-container">
      <a-spin :loading="loading" class="cards-loading">
        <!-- 空状态 -->
        <a-empty v-if="!loading && dataList.length === 0" class="empty-state">
          <template #image>
            <icon-message />
          </template>
          <template #description>
            <span class="empty-description">暂无系统消息数据</span>
          </template>
        </a-empty>

        <!-- 消息列表 - 左右两栏布局 -->
        <div v-else class="message-list">
          <div v-for="item in dataList" :key="item.id" class="message-item">
            <!-- 左侧区域：消息来源信息 -->
            <div class="message-source">
              <!-- 消息类型标签 -->
              <div class="message-type-tag">
                <a-tag size="small">{{ item.msg_type_str }}</a-tag>
              </div>

              <!-- 来源账号信息 -->
              <div class="source-account">
                <!-- 用户头像 -->
                <div class="account-avatar">
                  <a-avatar :size="48" class="user-avatar">
                    <img
                      v-if="item.profile_photo"
                      :src="item.profile_photo"
                      alt="用户头像"
                      @error="handleAvatarError"
                    />
                  </a-avatar>
                  <!-- 平台角标 -->
                  <img
                    class="platform-badge"
                    :src="getPlatformIcon(item.platform)"
                    alt=""
                  />
                </div>

                <!-- 账户信息 -->
                <div class="account-info">
                  <div class="account-name">{{
                    item.account_name || '未知用户'
                  }}</div>
                  <div class="account-id"
                    >ID: {{ item.account_id || item.id || '-' }}</div
                  >
                </div>
              </div>
            </div>

            <!-- 右侧区域：消息内容 -->
            <div class="message-main">
              <!-- 顶部信息行 -->
              <div class="message-header">
                <div class="message-type">
                  <icon-notification />
                  <span>{{ item.msg_title }}</span>
                </div>
                <div class="message-time">
                  {{ item.msg_time || '-' }}
                </div>
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <div class="message-text">
                  <pre style="margin: 0">{{ item.msg_describe }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="dataList.length > 0" class="pagination-wrapper">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-total="true"
            :show-jumper="true"
            :show-page-size="true"
            :page-size-options="[10, 20, 30, 50]"
            size="small"
            @change="onPageChange"
            @page-size-change="onPageSizeChange"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
  import request from '@/api/request';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import UserManageEdit from '@/views/manage/user-manage/user-manage-edit.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import {
    contactWayListM,
    customerServiceStatusListM,
  } from '@/components/dict-select/dict-travel';
  import { getPlatformIcon } from '@/utils/util';
  import { useDataCacheStore } from '@/store';
  // 获取平台图标

  // 系统消息数据项接口定义
  interface SystemMessageItem {
    id: string;
    account_name: string;
    user_name: string;
    mobile: string;
    status: string;
    add_time: string;
    update_time: string;
  }

  // 分页配置接口定义
  interface PaginationConfig {
    current: number;
    pageSize: number;
    total: number;
  }

  const router = useRouter();
  const dataCache = useDataCacheStore();

  // 生成默认表单模型
  const generateFormModel = () => {
    return {
      platform: null,
      account: null,
    };
  };

  // 响应式数据定义
  const loading = ref<boolean>(false);
  const formModel = reactive(generateFormModel());
  const dataList = ref<SystemMessageItem[]>([]);
  const pagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: 20, // 紧凑单列布局下每页显示20个
    total: 0,
  });

  /**
   * 获取系统消息列表数据
   * @param params 查询参数
   */
  const getList = async (params: any = {}) => {
    try {
      loading.value = true;
      const response = await request('/api/accountMsg/list', {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...formModel,
        ...params,
      });

      if (response && response.data) {
        dataList.value =
          response.data.data || response.data.table || response.data || [];
        pagination.total = response.data.total || 0;
        pagination.current =
          response.data.current_page || response.data.page || 1;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error('获取系统消息列表失败:', error);
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    try {
      pagination.current = 1;
      await getList();

      // 同时刷新未读消息数量
      await dataCache.getNoRead();

      console.log('✅ 系统消息页面数据刷新完成');
    } catch (error) {
      console.error('❌ 系统消息页面数据刷新失败:', error);
    }
  };

  /**
   * 处理搜索提交
   * @param resData 额外的搜索参数
   */
  const handleSubmit = (resData: any = {}) => {
    Object.assign(formModel, resData);
    pagination.current = 1;
    getList();
  };

  /**
   * 处理页码变化
   * @param page 新页码
   */
  const onPageChange = (page: number) => {
    pagination.current = page;
    getList();
  };

  /**
   * 处理页面大小变化
   * @param pageSize 新的页面大小
   */
  const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.current = 1;
    getList();
  };

  // 全局刷新事件处理器
  const handleGlobalRefresh = () => {
    console.log('🔄 收到全局刷新事件，开始刷新系统消息数据');
    refreshData();
  };

  // 组件挂载时获取数据并添加事件监听器
  onMounted(() => {
    getList();

    // 监听来自导航栏的刷新事件
    window.addEventListener('refresh-system-message', handleGlobalRefresh);
    console.log('📡 系统消息页面已注册全局刷新事件监听器');
  });

  // 组件卸载时移除事件监听器
  onBeforeUnmount(() => {
    window.removeEventListener('refresh-system-message', handleGlobalRefresh);
    console.log('🗑️ 系统消息页面已移除全局刷新事件监听器');
  });
</script>

<style scoped lang="less">
  // 搜索卡片样式 - 紧凑设计
  .search-card {
    margin-bottom: 12px; // 减小底部间距
    border-radius: 6px; // 减小圆角
    border: 1px solid var(--color-border-1); // 使用更浅的边框
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02); // 添加轻微阴影

    :deep(.arco-card-body) {
      padding: 12px 16px; // 减小内边距
    }
  }

  .search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  // 卡片容器样式
  .cards-container {
    height: calc(100vh - 200px);
    overflow: auto;
    display: flex;
    flex-direction: column;
    background: var(--color-bg-1);
    border-radius: var(--border-radius-large);
  }

  .cards-loading {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  // 空状态样式
  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .empty-description {
      color: var(--color-text-3);
      font-size: 14px;
      margin-top: 8px;
    }
  }

  // 消息列表布局 - 横向设计
  .message-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: var(--color-bg-1);

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  // 消息项样式 - 左右两栏布局
  .message-item {
    display: flex;
    align-items: stretch;
    margin-bottom: 16px;
    background: var(--color-bg-2);
    border-radius: 8px;
    // border: 1px solid var(--color-border-1);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:hover {
      border-color: var(--color-border-1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 左侧区域：消息来源信息
  .message-source {
    width: 280px;
    flex-shrink: 0;
    padding: 20px;
    border-right: 1px solid var(--color-border-1);
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative; // 为绝对定位的子元素提供定位上下文
  }

  // 消息类型标签 - 绝对定位在左上角
  .message-type-tag {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 1;
  }

  // 来源账号信息
  .source-account {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
    margin-top: 32px; // 为绝对定位的标签留出空间
  }

  .account-avatar {
    position: relative;
    display: flex;
    justify-content: center;

    .user-avatar {
      background: var(--color-fill-2);
      color: var(--color-text-3);
      font-size: 20px;
    }
  }

  .platform-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: var(--color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    border: 2px solid var(--color-bg-2);
  }

  .account-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
  }

  .account-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--color-text-1);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .account-id {
    font-size: 12px;
    color: var(--color-text-3);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 状态信息
  .source-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }

  .status-label {
    color: var(--color-text-3);
  }

  .status-value {
    color: var(--color-text-1);
  }

  // 右侧区域：消息内容
  .message-main {
    flex: 1;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  // 消息头部信息行
  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 12px;
    border-bottom: 1px solid var(--color-border-1);
  }

  .message-type {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text-1);

    svg {
      color: var(--color-primary);
    }
  }

  .message-time {
    font-size: 12px;
    color: var(--color-text-3);
  }

  // 消息内容区域
  .message-content {
    flex: 1;
    padding: 0 20px;
  }

  .message-text {
    font-size: 14px;
    line-height: 1.7;
    color: var(--color-text-1);

    p {
      margin: 0 0 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    strong {
      color: var(--color-primary);
      font-weight: 500;
    }
  }

  .contact-link {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  // 分页样式 - 紧凑设计
  .pagination-wrapper {
    padding: 16px; // 减小垂直内边距
    display: flex;
    justify-content: flex-end;
    background: var(--color-bg-1);
    flex-shrink: 0;
  }

  // 响应式布局调整 - 左右两栏适配
  @media (max-width: 1024px) {
    .message-source {
      width: 240px;
      padding: 16px;
    }

    .account-name {
      font-size: 14px;
    }

    .message-main {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    .message-list {
      padding: 4px;
    }

    .message-item {
      flex-direction: column;
      align-items: stretch;
    }

    .message-source {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid var(--color-border-1);
      padding: 16px;

      .source-account {
        flex-direction: row;
        align-items: center;
        gap: 12px;
      }

      .account-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-width: 0;
      }

      .source-status {
        flex-direction: row;
        gap: 16px;
      }
    }

    .message-main {
      padding: 16px;
    }

    .message-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .message-text {
      font-size: 13px;
      line-height: 1.6;
    }
  }
</style>

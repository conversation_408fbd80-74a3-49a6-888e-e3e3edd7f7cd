<template>
  <div>
    <search-form-fold
      :form-data="formModel"
      :get-default-form-data="generateFormModel"
      @search="handleSubmit()"
    >
      <template #formItemGroup>
        <a-form-item label="账号">
          <a-input
            v-model="formModel.account_name"
            allow-clear
            placeholder="请输入账号"
          />
        </a-form-item>
        <a-form-item label="姓名">
          <a-input
            v-model="formModel.user_name"
            allow-clear
            placeholder="请输入姓名"
          />
        </a-form-item>
        <a-form-item label="角色">
          <request-select
            v-model="formModel.roles"
            request-url="/api/user/roleList"
            label-key="role_name"
          />
        </a-form-item>
        <a-form-item field="status" label="状态">
          <dict-select v-model="formModel.state" :data-list="stateM" />
        </a-form-item>
      </template>
    </search-form-fold>

    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <div> </div>
        <a-space>
          <a-button type="primary" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
          </a-space>
        </template>
        <template #state="{ record }: TableColumnSlot">
          <a-switch
            :loading="record.loading"
            :model-value="record.state === 1"
            @change="updateState(record)"
          />
        </template>
      </base-table>
    </a-card>
    <user-manage-edit ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { stringArrShow } from '@/utils/table-utils/columns-config';
  import UserManageEdit from '@/views/manage/user-manage/user-manage-edit.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { stateM } from '@/components/dict-select/dict-common';
  import { Message } from '@arco-design/web-vue';

  const generateFormModel = () => {
    return {
      account_name: '',
      user_name: '',
      roles: [],
      state: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/user/userList', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
    },
    {
      title: '账号',
      dataIndex: 'account_name',
    },
    {
      title: '姓名',
      dataIndex: 'user_name',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    {
      title: '角色',
      dataIndex: 'role_names',
      render: stringArrShow(),
    },
    {
      title: '状态',
      dataIndex: 'state',
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function updateState(record: any) {
    record.loading = true;
    request('/api/user/userSave', {
      ...record,
      password: undefined,
      state: record.state === 1 ? -1 : 1,
    })
      .then(() => {
        record.state = record.state === 1 ? -1 : 1;
      })
      .finally(() => {
        record.loading = false;
      });
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>

<template>
  <div class="content-box no-height">
    <a-form
      ref="thisFormRef"
      direction="inline"
      :model="formModel"
      auto-label-width
    >
      <a-card class="no-padding" title="钉钉">
        <template #title>
          <div class="dis-title">
            <div>
              <span class="mr-20"><icon-twitter /> 钉钉</span>
              <a-switch
                v-model="formModel.dingDingSetting.is_open"
                :checked-value="1"
                :unchecked-value="2"
                @change="saveSystemSettingFn()"
              />
            </div>
            <a-button
              v-if="formModel.dingDingSetting.is_open === 1"
              type="primary"
              @click="saveSystemSettingFn()"
            >
              保存
            </a-button>
          </div>
        </template>
        <template v-if="formModel.dingDingSetting.is_open == 1">
          <a-form-item label="AppKey" label-col-flex="90px">
            <a-input
              v-model="formModel.dingDingSetting.app_id"
              allow-clear
              style="width: 360px"
              placeholder="请输入"
            ></a-input>
          </a-form-item>
          <a-form-item label="AgentId" label-col-flex="90px">
            <a-input
              v-model="formModel.dingDingSetting.agent_id"
              allow-clear
              style="width: 360px"
              placeholder="请输入"
            ></a-input>
          </a-form-item>
          <a-form-item label="AppSecret" label-col-flex="90px">
            <a-input
              v-model="formModel.dingDingSetting.key"
              style="width: 360px"
              allow-clear
              placeholder="请输入"
            ></a-input>
          </a-form-item>
        </template>
      </a-card>
      <a-card class="table-card no-padding" title="飞书">
        <template #title>
          <div class="dis-title">
            <div>
              <span class="mr-20"><icon-lark-color /> 飞书</span>
              <a-switch
                v-model="formModel.feiShuSetting.is_open"
                :checked-value="1"
                :unchecked-value="2"
                @change="saveSystemSettingFn()"
              />
            </div>
            <a-button
              v-if="formModel.feiShuSetting.is_open === 1"
              type="primary"
              @click="saveSystemSettingFn()"
            >
              保存
            </a-button>
          </div>
        </template>
        <template v-if="formModel.feiShuSetting.is_open == 1">
          <a-form-item label="App_ID" label-col-flex="90px">
            <a-input
              v-model="formModel.feiShuSetting.app_id"
              allow-clear
              style="width: 360px"
              placeholder="请输入"
            ></a-input>
          </a-form-item>
          <a-form-item label="Key" label-col-flex="90px">
            <a-input
              v-model="formModel.feiShuSetting.key"
              style="width: 360px"
              allow-clear
              placeholder="请输入"
            ></a-input>
          </a-form-item>
        </template>
      </a-card>
      <a-card class="table-card no-padding" title="企业微信">
        <template #title>
          <div class="dis-title">
            <div>
              <span class="mr-20"><icon-wechatpay /> 企业微信</span>
              <a-switch
                v-model="formModel.weiXinSetting.is_open"
                :checked-value="1"
                :unchecked-value="2"
                :disabled="true"
                @change="saveSystemSettingFn()"
              />
            </div>
            <a-button
              v-if="formModel.weiXinSetting.is_open === 1"
              type="primary"
              @click="saveSystemSettingFn()"
            >
              保存
            </a-button>
          </div>
        </template>
        <template v-if="formModel.weiXinSetting.is_open == 1">
          <a-form-item label="App_ID" label-col-flex="90px">
            <a-input
              v-model="formModel.weiXinSetting.app_id"
              allow-clear
              style="width: 360px"
              placeholder="请输入"
            ></a-input>
          </a-form-item>
          <a-form-item label="Key" label-col-flex="90px">
            <a-input
              v-model="formModel.weiXinSetting.key"
              style="width: 360px"
              allow-clear
              placeholder="请输入"
            ></a-input>
          </a-form-item>
        </template>
      </a-card>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, h, onMounted } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { getToken } from '@/utils/auth';

  const headers = { Authorization: getToken() };

  const loading = ref(false);
  const generateFormModel = () => {
    return {
      // 基础查询条件
      date: [],
      watermark_h: '',
      watermark_h_file: [],
      watermark_open: 2,
      watermark_s: '',
      watermark_s_file: [],
      appid: null,
      key: null,
      appid2: null,
      key2: null,
      appid3: null,
      key3: null,
      // 新的配置
      name: 'message_setting',
      type: 3,
      value: [],
      dingDingSetting: {
        type: 'dingding',
        is_open: 2,
        app_id: '',
        is_edit_app_id: false,
        key: '',
        is_edit_key: false,
      },
      feiShuSetting: {
        type: 'feishu',
        is_open: 2,
        app_id: '',
        is_edit: false,
        key: '',
      },
      weiXinSetting: {
        type: 'feishu',
        is_open: 2,
        app_id: '',
        is_edit: false,
        key: '',
      },
    };
  };
  const formModel: any = ref(generateFormModel());

  // 获取系统的配置项
  async function getThisData() {
    loading.value = true;
    // 获取列表
    let resData = await request('/api/systemSettingInfo', {
      ...formModel.value,
    });
    loading.value = false;
    if (resData && resData.code === 0) {
      // 解构当前的接口响应结果 赋值到各个配置项
      if (resData.data && resData.data.value?.length > 0) {
        resData.data.value.forEach((item: any) => {
          if (item.type === 'dingding') {
            Object.assign(formModel.value.dingDingSetting, item);
          } else if (item.type === 'feishu') {
            Object.assign(formModel.value.feiShuSetting, item);
          } else {
            console.log('暂无合适配置项');
          }
        });
      }
    } else {
      Message.error('网络异常');
    }
  }

  // 保存系统的配置项
  const saveSystemSettingFn = async () => {
    // 组装配置数据
    let sendParams = JSON.parse(JSON.stringify(formModel.value));
    sendParams.value = [sendParams.dingDingSetting, sendParams.feiShuSetting];
    let resData = await request('/api/systemSetting', sendParams);
    loading.value = false;
    if (resData && resData.code === 0) {
      Message.success('保存成功');
      getThisData();
    } else {
      Message.error('网络异常');
    }
  };

  // 初始化处理逻辑
  const handleSubmit = (resData: any = {}) => {
    Object.assign(formModel.value, resData);
    // 重置搜索 所有数据
    getThisData();
  };
  handleSubmit();
</script>

<style scoped lang="less">
  .no-padding {
    :deep(.arco-card-body) {
      padding: 16px 16px 16px !important;
    }
  }
  .dis-flex {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 580px;
    .side-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;
      .text {
        margin-top: 20px;
      }
      .water-img-h {
        width: 200px;
        height: 100px;
      }
      .preview-box {
        position: relative;
        cursor: pointer;
        .close-icon {
          position: absolute;
          top: -16px;
          right: -16px;
          font-size: 22px;
          // color: rgb(var(--primary-6));
          transition: all 0.2s linear;
          &:hover {
            color: rgb(var(--primary-6));
            transform: scale(1.05);
          }
        }
      }
    }
  }
  .dis-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>

<template>
  <d-modal
    v-model:visible="visible"
    :width="['area'].includes(showField) ? '400px' : '500px'"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="title"
    @cancel="handleCancel"
  >
    <a-spin :loading="loading" class="w100p">
      <a-form ref="formRef" :model="formModel" :auto-label-width="true">
        <a-form-item
          v-if="!showField || showField === 'area'"
          label="产品名称"
          :hide-label="!!showField"
          field="area"
          :rules="requiredRule"
        >
          <a-input v-model="formModel.area" placeholder="请输入" />
        </a-form-item>
        <template v-if="!showField || showField === 'industry'">
          <a-form-item
            label="所属行业"
            field="industry_id"
            :rules="requiredRule"
          >
            <folder-tree-select
              v-model="formModel.industry_id"
              request-url="/api/industryList"
              label-key="label"
              value-key="value"
              :allow-clear="false"
              @change="industryChange"
            ></folder-tree-select>
          </a-form-item>
        </template>
        <a-form-item
          v-if="!showField || showField === 'inspiration_search'"
          label="创意灵感搜索关键词"
          :hide-label="!!showField"
        >
          <a-space wrap>
            <a-tag
              v-for="(tag, index) of formModel.inspiration_search"
              :key="tag"
              size="large"
              color="arcoblue"
              closable
              @close="handleRemove(index)"
            >
              {{ tag }}
            </a-tag>
            <a-input-group v-if="showInput">
              <a-input
                ref="inputRef"
                v-model.trim="inputVal"
                :style="{ width: '280px' }"
                allow-clear
                placeholder="支持一次添加多个，使用分号；分隔"
                @keyup.enter="handleAdd"
                @blur="handleAdd"
              />
              <a-button type="primary" class="ml-5" @click="handleAdd">
                <template #icon>
                  <icon-check />
                </template>
              </a-button>
            </a-input-group>
            <a-tag
              v-else
              size="large"
              color="green"
              :style="{
                cursor: 'pointer',
              }"
              @click="handleEdit"
            >
              <template #icon>
                <icon-plus />
              </template>
              添加
            </a-tag>
          </a-space>
        </a-form-item>
        <a-form-item
          v-if="showField === 'ai_prompt_video'"
          label="AI视频意向度"
          :hide-label="!!showField"
          field="ai_prompt_video"
          :rules="requiredRuleArrIpt"
        >
          <a-space direction="vertical" class="w100p">
            <template
              v-for="(item, index) in formModel.ai_prompt_video"
              :key="index"
            >
              <a-textarea
                v-if="index === 1"
                v-model="item.user_prompt"
                class="area-ipt"
                :auto-size="{ minRows: 4, maxRows: showField ? 24 : 8 }"
              />
            </template>
          </a-space>
        </a-form-item>
        <a-form-item
          v-if="showField === 'ai_prompt_text'"
          label="AI文本意向度"
          field="ai_prompt_text"
          :hide-label="!!showField"
          :rules="requiredRuleArrIpt"
        >
          <a-space direction="vertical" class="w100p">
            <template
              v-for="(item, index) in formModel.ai_prompt_text"
              :key="index"
            >
              <a-textarea
                v-if="index === 1"
                v-model="item.user_prompt"
                :auto-size="{ minRows: 4, maxRows: showField ? 24 : 8 }"
              />
            </template>
          </a-space>
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { computed, nextTick, ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import {
    requiredRule,
    requiredRuleArr,
    requiredRuleArrIpt,
  } from '@/utils/util';
  import FolderTreeSelect from '@/components/fold-tree/folder-tree-select.vue';
  import { cloneDeep, uniq } from 'lodash';

  const defaultForm = () => ({
    id: null,
    area: '',
    industry_id: '',
    industry_first: '',
    industry_second: '',
    inspiration_search: [] as string[],
    ai_prompt_video: [] as any[],
    ai_prompt_text: [] as any[],
    ai_prompt_polish: [],
    ai_service_prompt: [],
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const showField = ref('');
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };
  const title = computed(() => {
    if (!formModel.value.id) {
      return `新建产品`;
    }
    switch (showField.value) {
      case 'area':
        return `编辑产品名称`;
      case 'inspiration_search':
        return `编辑创意灵感搜索关键词`;
      case 'ai_prompt_video':
        return `编辑AI视频意向度`;
      case 'ai_prompt_text':
        return `编辑AI文本意向度`;
      case 'industry':
        return `编辑所属行业`;
      default:
        return `编辑产品`;
    }
  });
  function formatPrompt() {
    formModel.value.ai_prompt_video = formModel.value.ai_prompt_video?.length
      ? formModel.value.ai_prompt_video
      : [];
    formModel.value.ai_prompt_text = formModel.value.ai_prompt_text?.length
      ? formModel.value.ai_prompt_text
      : [];
  }

  const inputRef = ref();
  const showInput = ref(false);
  const inputVal = ref('');
  const handleAdd = () => {
    if (inputVal.value) {
      let keywords = inputVal.value.replaceAll('；', ';').split(';');
      keywords = keywords.map((item) => item.trim()).filter((item) => item);
      formModel.value.inspiration_search = uniq([
        ...formModel.value.inspiration_search,
        ...keywords,
      ]);
      inputVal.value = '';
    }
    showInput.value = false;
  };
  const handleRemove = (index: number) => {
    formModel.value.inspiration_search.splice(index, 1);
  };
  const handleEdit = () => {
    showInput.value = true;
    nextTick(() => {
      inputRef.value?.focus();
    });
  };

  const show = (dinfo: any, field?: string) => {
    showField.value = field || '';
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
      // formatPrompt();
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  // 根据行业获取prompt
  function industryChange(val: any, items: any[]) {
    formModel.value.industry_first = items[0]?.label || '';
    formModel.value.industry_second = items[1]?.label || '';
    // loading.value = true;
    // request('/api/thread/generatePrompt', {
    //   ...formModel.value,
    // })
    //   .then((res) => {
    //     formModel.value.ai_prompt_video = res.data?.ai_prompt_video || [];
    //     formModel.value.ai_prompt_text = res.data?.ai_prompt_text || [];
    //     formModel.value.ai_service_prompt = res.data?.ai_service_prompt || [];
    //     formModel.value.ai_prompt_polish = res.data?.ai_prompt_polish || [];
    //     // formatPrompt();
    //   })
    //   .finally(() => {
    //     loading.value = false;
    //   });
  }

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      request('/api/thread/baseSettingSave', {
        ...formModel.value,
      })
        .then((res) => {
          Message.success('保存成功');
          handleCancel();
          emit('save', res.data);
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

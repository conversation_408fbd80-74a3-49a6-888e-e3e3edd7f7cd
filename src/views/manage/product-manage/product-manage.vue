<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
        </a-space>
        <a-space>
          <a-button type="primary" status="success" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, y: 'calc(100vh - 165px)' }"
      >
        <template #area="{ record }: TableColumnSlot">
          <div class="hover-box" @click="showEditFn(record, 'area')">
            <span class="primary_text cur-por">{{ record.area }}</span>
            <a-button type="text" size="small" class="hover-item">
              <template #icon><icon-edit /></template>
            </a-button>
          </div>
        </template>
        <template #industry="{ record }: TableColumnSlot">
          <div class="hover-box" @click="showEditFn(record, 'industry')">
            <span class="primary_text cur-por">{{
              record.industry_name || '-'
            }}</span>
            <a-button type="text" size="mini" class="hover-item">
              <template #icon><icon-edit /></template>
            </a-button>
          </div>
        </template>
        <template #ai_prompt="{ record, column }: TableColumnSlot">
          <div class="hover-box">
            <template v-if="record.ai_prompt_state === 1">
              <div class="loading-container">
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span class="loading-text">AI生成中</span>
              </div>
            </template>
            <template v-else>
              <div
                class="prompt-content"
                @click="showEditFn(record, column.dataIndex)"
              >
                <span class="truncate-text">
                  {{
                    record[column.dataIndex as string]?.[1]?.user_prompt || '-'
                  }}
                </span>
                <a-button type="text" size="mini" class="hover-item">
                  <template #icon><icon-edit /></template>
                </a-button>
              </div>
            </template>
          </div>
        </template>
        <template #inspiration_search="{ record }: TableColumnSlot">
          <div class="hover-box">
            <template v-if="record.inspiration_search?.length">
              <a-popover> </a-popover>
              <component
                :is="record.inspiration_search.length > 2 ? Popover : 'div'"
                :content="
                  record.inspiration_search
                    .map((item:any) => item?.account_name || item)
                    .join('\n')
                "
                :content-style="{
                  maxWidth: '400px',
                  minWidth: '200px',
                  maxHeight: '200px',
                  overflow: 'auto',
                }"
              >
                <template #content>
                  <a-space direction="vertical">
                    <div
                      v-for="(item, index) in record.inspiration_search"
                      :key="index"
                    >
                      #{{ item?.account_name || item }}
                    </div>
                  </a-space>
                </template>
                <a-space wrap size="mini">
                  <a-tag
                    v-for="item in record.inspiration_search.slice(0, 2)"
                    :key="item"
                    color="blue"
                  >
                    {{ item?.account_name || item }}
                  </a-tag>
                  <a-tag
                    v-if="record.inspiration_search.length > 2"
                    color="blue"
                  >
                    等共{{ record.inspiration_search.length }}个
                  </a-tag>
                  <a-button
                    type="text"
                    size="mini"
                    class="hover-item"
                    @click="showEditFn(record, 'inspiration_search')"
                  >
                    <template #icon><icon-edit /></template>
                  </a-button>
                </a-space>
              </component>
            </template>
            <template v-else>
              <span>-</span>
              <a-button
                type="text"
                class="hover-item"
                size="mini"
                @click="showEditFn(record, 'inspiration_search')"
              >
                <template #icon><icon-edit /></template>
              </a-button>
            </template>
          </div>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-button type="text" size="small" @click="showEditFn(record)">
              <template #icon><icon-edit /></template>
              编辑
            </a-button>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <product-manage-edit ref="editRef" @save="handleSubmit()" />
    <long-text-modal ref="txtRef" title="Prompt" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import { Message, Tooltip, Popover } from '@arco-design/web-vue';
  import LongTextModal from '@/components/LongTextModal.vue';
  import { dateFormatShow } from '@/utils/table-utils/columns-config';
  import ProductManageEdit from './product-manage-edit.vue';

  const generateFormModel = () => {
    return {
      account_name: '',
      user_name: '',
      roles: [],
      state: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/threadSetting/list', {
      ...data,
    });
  };

  const editRef = ref();
  const txtRef = ref();
  function showEditFn(record?: any, field: string) {
    editRef.value?.show(record, field);
  }

  const columns = [
    {
      title: '产品名称',
      dataIndex: 'area',
      width: 200,
    },
    // 行业
    {
      title: '所属行业',
      dataIndex: 'industry',
      slotName: 'industry',
      width: 220,
    },
    {
      title: '创意灵感搜索关键词',
      dataIndex: 'inspiration_search',
      slotName: 'inspiration_search',
      width: 260,
    },
    {
      title: 'AI视频意向度',
      dataIndex: 'ai_prompt_video',
      slotName: 'ai_prompt',
      width: 280,
    },
    {
      title: 'AI文本意向度',
      dataIndex: 'ai_prompt_text',
      slotName: 'ai_prompt',
      width: 280,
    },
    {
      title: '创建人',
      dataIndex: 'create_user',
      width: 130,
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
      render: dateFormatShow(),
      align: 'center',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less">
  .hover-box {
    display: flex;
    align-items: center;
    .hover-item {
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
      display: inline-block;
      line-height: 1;
      :deep(.arco-link-icon) {
        font-size: inherit;
      }
    }
    &:hover {
      .hover-item {
        pointer-events: all;
        opacity: 1;
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgb(var(--primary-6));
  }

  .loading-dots {
    display: flex;
    gap: 4px;

    span {
      width: 6px;
      height: 6px;
      background: rgb(var(--primary-6));
      border-radius: 50%;
      animation: pulse 1.4s infinite ease-in-out;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .loading-text {
    font-size: 13px;
    font-weight: 500;
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(0.8);
      opacity: 0.6;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  .prompt-content {
    display: flex;
    align-items: flex-start;
    gap: 4px;
    cursor: pointer;
  }

  .truncate-text {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    color: rgb(var(--primary-6));
  }
</style>

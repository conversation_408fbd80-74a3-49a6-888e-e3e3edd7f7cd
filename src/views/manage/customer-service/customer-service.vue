<template>
  <div>
    <a-card size="small" class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.status"
            :data-list="customerServiceStatusListM"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.user_name"
            placeholder="请输入姓名"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
        </a-space>
        <a-space>
          <a-button type="primary" status="success" @click="userRef?.show()">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
          <a-button type="primary" @click="router.push('customer-service-log')">
            <template #icon>
              <icon-list />
            </template>
            电销日志
          </a-button>
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        class="mt-10"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, y: 'calc(100vh - 165px)' }"
      >
        <template #status="{ record }: TableColumnSlot">
          <status-badge
            :list="customerServiceStatusListM"
            :value="record.status"
          ></status-badge>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
          </a-space>
        </template>
      </base-table>
    </a-card>
    <customer-service-edit ref="editRef" @save="handleSubmit()" />
    <user-manage-edit ref="userRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import { useRouter } from 'vue-router';
  import UserManageEdit from '@/views/manage/user-manage/user-manage-edit.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { TableColumnSlot } from '@/global';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import StatusBadge from '@/components/status-badge/status-badge.vue';
  import CustomerServiceEdit from './customer-service-edit.vue';

  const router = useRouter();
  const generateFormModel = () => {
    return {
      status: null,
      user_name: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/travel/allServiceUser', {
      ...data,
    });
  };

  const editRef = ref();
  const userRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: '账号',
      dataIndex: 'account_name',
    },
    {
      title: '姓名',
      dataIndex: 'user_name',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
    },
    // {
    //   title: '每日最多单数',
    //   dataIndex: 'day_max_order',
    // },
    {
      title: '创建时间',
      dataIndex: 'add_time',
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>

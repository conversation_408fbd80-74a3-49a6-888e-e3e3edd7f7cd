<template>
  <div>
    <Breadcrumb
      :items="[{ label: '电销管理', path: 'customer-service' }, '电销日志']"
      show-back
    ></Breadcrumb>
    <a-card size="small" class="table-card mt-0">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.status"
            :data-list="customerServiceStatusListM"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.user_name"
            placeholder="请输入姓名"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
        </a-space>
        <a-space>
          <!--<a-button type="primary" @click="exportAction">
            <template #icon>
              <icon-export />
            </template>
            导出
          </a-button>-->
        </a-space>
      </div>
      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
        :scroll-percent="{ x: 1000, y: 'calc(100vh - 204px)' }"
      >
        <template #status="{ record }: TableColumnSlot">
          <status-badge
            :list="customerServiceStatusListM"
            :value="record.status"
          ></status-badge>
        </template>
        <template #action="{ record }: TableColumnSlot">
          <a-spin class="jc-cen fd-r" :loading="record.loading">
            <a-space>
              <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
            </a-space>
          </a-spin>
        </template>
      </base-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';

  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { TableColumnSlot } from '@/global';
  import { getColor, getText } from '@/components/dict-select/dict-util';
  import StatusBadge from '@/components/status-badge/status-badge.vue';

  const generateFormModel = () => {
    return {
      status: null,
      user_name: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/travel/serviceUserLoginLog', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: '姓名',
      dataIndex: 'user_name',
    },
    {
      title: '更新状态',
      dataIndex: 'status',
      align: 'center',
    },
    {
      title: '更新时间',
      align: 'center',
      dataIndex: 'add_time',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less"></style>

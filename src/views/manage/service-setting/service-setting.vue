<template>
  <div class="content-box no-height">
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane key="list">
        <template #title> 尘锋SCRM </template>
      </a-tab-pane>
    </a-tabs>
    <a-form
      ref="thisFormRef"
      direction="inline"
      :model="formModel"
      auto-label-width
      class="mt-10"
    >
      <a-card class="no-padding" size="small">
        <div class="dis-flex">
          <div>
            <a-form-item
              label="ClientID"
              :required="true"
              label-col-flex="90px"
            >
              <a-input
                v-model="formModel.value.ClientID"
                allow-clear
                style="width: 360px"
                placeholder="请输入"
              ></a-input>
            </a-form-item>
            <a-form-item
              label="ClientSecret"
              :required="true"
              label-col-flex="90px"
            >
              <a-input
                v-model="formModel.value.ClientSecret"
                allow-clear
                style="width: 360px"
                placeholder="请输入"
              ></a-input>
            </a-form-item>
          </div>
          <a-button type="primary" @click="saveSystemSettingFn()">
            保存
          </a-button>
        </div>
      </a-card>
    </a-form>
    <div class="mt-10">
      <div class="line-text">
        该配置可将GetClue系统获取的“意向线索”信息同步至“尘锋SCRM”，支持在其他系统内完成对线索的跟进和转化。
      </div>
      <div class="line-text">
        详细操作指引见：
        <a-link
          class="link"
          target="_blank"
          href="https://juwei.feishu.cn/docx/DrK9d6YUQoRoX3xWbRacs0bZnYd?from=from_copylink"
          >《尘锋SCRM系统对接-操作指引》</a-link
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, h, onMounted } from 'vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { getToken } from '@/utils/auth';

  const activeKey = ref('list');
  const headers = { Authorization: getToken() };

  const loading = ref(false);
  const generateFormModel = () => {
    return {
      // 基础查询条件
      name: 'chenfeng_crm',
      type: 3,
      value: {
        ClientID: '',
        ClientSecret: '',
      },
    };
  };
  const formModel: any = ref(generateFormModel());

  // 获取系统的配置项
  async function getThisData() {
    loading.value = true;
    // 获取列表
    let resData = await request('/api/systemSettingInfo', {
      ...formModel.value,
    });
    loading.value = false;
    if (resData && resData.code === 0) {
      // 解构当前的接口响应结果 赋值到各个配置项
      Object.assign(formModel.value, resData.data);
    } else {
      Message.error('网络异常');
    }
  }

  // 保存系统的配置项
  const saveSystemSettingFn = async () => {
    // 组装配置数据
    let sendParams = JSON.parse(JSON.stringify(formModel.value));
    let resData = await request('/api/systemSetting', sendParams);
    loading.value = false;
    if (resData && resData.code === 0) {
      Message.success('保存成功');
      getThisData();
    } else {
      Message.error('网络异常');
    }
  };

  // 初始化处理逻辑
  const handleSubmit = (resData: any = {}) => {
    Object.assign(formModel.value, resData);
    // 重置搜索 所有数据
    getThisData();
  };
  handleSubmit();
</script>

<style scoped lang="less">
  .no-padding {
    :deep(.arco-card-body) {
      padding: 16px 16px 16px !important;
    }
  }
  .dis-flex {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 580px;
    .side-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;
      .text {
        margin-top: 20px;
      }
      .water-img-h {
        width: 200px;
        height: 100px;
      }
      .preview-box {
        position: relative;
        cursor: pointer;
        .close-icon {
          position: absolute;
          top: -16px;
          right: -16px;
          font-size: 22px;
          // color: rgb(var(--primary-6));
          transition: all 0.2s linear;
          &:hover {
            color: rgb(var(--primary-6));
            transform: scale(1.05);
          }
        }
      }
    }
  }
  .dis-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dis-flex {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }

  .line-text {
    line-height: 24px;
    .link {
      color: rgb(var(--primary-6));
      cursor: pointer;
    }
  }
</style>

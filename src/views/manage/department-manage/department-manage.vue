<template>
  <div class="content-box">
    <a-card class="table-card">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="handleSubmit">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <a-input-search
            v-model="formModel.department_name"
            placeholder="请输入部门名称"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
        </a-space>
        <a-space>
          <a-button type="primary" status="success" @click="saveAction()">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
        </a-space>
      </div>
      <base-table
        ref="tableRef"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getDataList"
        :send-params="formModel"
      >
        <template #user_num="{ record }">
          <div> <icon-user-group /> {{ record.user_num || 0 }} </div>
        </template>
        <template #parent_name="{ record }">
          <div> {{ record.parent_name || '-' }} </div>
        </template>
        <template #action="{ record }">
          <a-link
            :disabled="!user.hasPermission(1)"
            @click="
              saveAction({
                parent_id: record.id,
                parent_name: record.department_name,
              })
            "
          >
            新建下级部门
          </a-link>
          <a-divider direction="vertical" />
          <a-link
            :disabled="!user.hasPermission(1)"
            @click="saveAction(record)"
          >
            编辑
          </a-link>
          <a-divider direction="vertical" />
          <a-popconfirm
            :content="`确定删除【${record.department_name}】吗?`"
            @ok="delAction(record)"
          >
            <a-link
              :disabled="
                !user.hasPermission(1) ||
                (record.child && record.child.length > 0) ||
                record.user_num > 0
              "
            >
              删除
            </a-link>
          </a-popconfirm>
        </template>
      </base-table>
    </a-card>
    <save-department ref="saveRef" @create-over="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import request from '@/api/request';
  import { useUserStore } from '@/store';
  import SaveDepartment from '@/views/manage/department-manage/SaveDepartment.vue';
  import { Message } from '@arco-design/web-vue';

  const user = useUserStore();

  const formRef = ref();
  const tableRef = ref();
  const saveRef = ref();
  const loading = ref(false);
  const columns = [
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   width: 120,
    // },
    {
      title: '部门名称',
      dataIndex: 'department_name',
      align: 'center',
    },
    // {
    //   title: '上级部门',
    //   dataIndex: 'parent_name',
    //   align: 'center',
    //   slotName: 'parent_name',
    // },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'center',
    },
    {
      title: '员工数量',
      dataIndex: 'user_num',
      align: 'center',
      slotName: 'user_num',
    },
    {
      title: '创建日期',
      dataIndex: 'add_time',
      align: 'center',
    },
    {
      title: '更新日期',
      dataIndex: 'update_time',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 260,
      align: 'center',
      slotName: 'action',
      fixed: 'right',
    },
  ];

  const generateFormModel = () => {
    return {
      // 基础查询条件
      department_name: null,
      // 更多查询条件
      user_id: '',
    };
  };
  const baseSearchRules: any = ref([
    {
      field: 'department_name',
      label: '部门名称',
      value: null,
      width: '200px',
    },
  ]);
  const searchRules: any = ref([]);

  const formModel: any = ref(generateFormModel());

  function formatField(list: any[]) {
    list.forEach((item: any) => {
      if (item.child) {
        item.children = item.child || undefined;
        formatField(item.children);
      }
    });
  }
  const getDataList = (data: any) => {
    return new Promise((resolve, reject) => {
      request('/api/department/list', data)
        .then((resData) => {
          formatField(resData.data);
          resolve(resData);
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    Object.assign(formModel.value, resData);
    tableRef.value?.search();
  };
  const delAction = (data: any) => {
    loading.value = true;
    request('/api/department/del', {
      id: data.id,
    })
      .then(() => {
        Message.success('操作成功');
        handleSubmit();
      })
      .catch(() => {
        loading.value = false;
      });
  };

  function saveAction(record: any) {
    saveRef.value?.show(record);
  }
</script>

<style scoped lang="less">
  .no-padding {
    :deep(.arco-card-body) {
      padding: 16px 16px 6px !important;
    }
  }
</style>

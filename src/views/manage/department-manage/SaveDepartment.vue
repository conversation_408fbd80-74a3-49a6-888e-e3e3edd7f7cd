<template>
  <d-modal
    :title="title"
    :visible="visible"
    :ok-loading="loading"
    width="460px"
    @ok="sendInfo"
    @cancel="onClose"
  >
    <a-form ref="thisFormRef" :model="thisFormData">
      <a-form-item
        v-if="thisFormData.parent_name"
        field="parent_name"
        label="上级部门"
      >
        <a-input
          disabled
          :model-value="thisFormData.parent_name"
          placeholder="请输入"
        ></a-input>
      </a-form-item>
      <a-form-item
        label="部门名称"
        field="department_name"
        :rules="{ required: true, message: '请输入', trigger: 'blur' }"
      >
        <a-input
          v-model="thisFormData.department_name"
          placeholder="请输入"
        ></a-input>
      </a-form-item>
      <a-form-item label="备注">
        <a-input v-model="thisFormData.remark" placeholder="请输入"></a-input>
      </a-form-item>
    </a-form>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import request from '@/api/request';
  import { FieldRule, Message } from '@arco-design/web-vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import DDrawer from '@/components/d-modal/d-drawer.vue';

  const defaultForm = () => ({
    id: undefined,
    parent_id: undefined,
    department_name: '',
    parent_name: '',
    remark: '',
  });
  const requiredRules: FieldRule = {
    required: true,
    message: '请填写',
  };
  const thisFormData = ref(defaultForm());
  const visible = ref(false);
  const loading = ref(false);
  const title = ref('');
  const emits = defineEmits(['createOver']);
  const thisFormRef = ref();
  const validate = () => {
    return thisFormRef.value?.validate();
  };
  const clearValidate = () => {
    thisFormRef.value?.clearValidate();
  };
  function show(item: any) {
    if (item?.id) {
      title.value = `编辑部门(${item.department_name})`;
      Object.assign(thisFormData.value, item);
    } else {
      title.value = `新建部门`;
    }
    if (item?.parent_id) {
      thisFormData.value.parent_id = item.parent_id;
      thisFormData.value.parent_name = item.parent_name;
    }
    clearValidate();
    visible.value = true;
  }
  function onClose() {
    visible.value = false;
    thisFormData.value = defaultForm();
  }

  async function sendInfo() {
    loading.value = true;
    const result = await validate();
    if (!result) {
      request('/api/department/save', {
        ...unref(thisFormData),
      })
        .then((resData) => {
          emits('createOver');
          Message.success('操作成功');
          onClose();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  }
  defineExpose({
    show,
  });
</script>

<style scoped></style>

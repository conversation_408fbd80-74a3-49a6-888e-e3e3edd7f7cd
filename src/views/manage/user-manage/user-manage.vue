<template>
  <div class="df">
    <a-card size="small" style="width: 200px" class="mr-10">
      <user-department-list
        v-model:department-id="formModel.department_id"
        @change="handleSubmit()"
      ></user-department-list>
    </a-card>

    <a-card size="small" class="table-card" style="margin-top: 0">
      <div class="table-card-header">
        <a-space>
          <a-button type="primary" @click="theTable?.fetchData()">
            <template #icon>
              <icon-refresh />
            </template>
            <span>刷新</span>
          </a-button>
          <dict-radio
            v-model="formModel.state"
            :data-list="stateM2"
            @change="handleSubmit()"
          />
          <a-input-search
            v-model="formModel.keyword"
            placeholder="请输入关键词"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          />
          <!-- <a-input-search
            v-model="formModel.user_name"
            placeholder="请输入姓名"
            allow-clear
            class="w-200"
            @search="handleSubmit()"
            @keydown.enter="handleSubmit()"
          /> -->
          <request-select
            v-model="formModel.roles"
            style="width: 200px"
            request-url="/api/user/roleList"
            label-key="role_name"
            placeholder="请选择角色"
            :max-tag-count="1"
            @change="handleSubmit()"
          />
        </a-space>
        <a-space>
          <a-button type="primary" status="success" @click="showEditFn()">
            <template #icon>
              <icon-plus />
            </template>
            新建
          </a-button>
        </a-space>
      </div>

      <!--table 区域-->
      <base-table
        ref="theTable"
        v-model:loading="loading"
        :columns-config="columns"
        :data-config="getList"
        :send-params="formModel"
      >
        <template #action="{ record }: TableColumnSlot">
          <a-space>
            <a-link @click="showEditFn(record)"> <icon-edit />编辑 </a-link>
          </a-space>
        </template>
        <template #state="{ record }: TableColumnSlot">
          <a-switch
            :loading="record.loading"
            :model-value="record.state === 1"
            @change="updateState(record)"
          />
        </template>
      </base-table>
    </a-card>
    <user-manage-edit ref="editRef" @save="handleSubmit()" />
  </div>
</template>

<script setup lang="ts">
  import BaseTable from '@/components/juwei-compoents/base-table/base-table.vue';
  import { ref, reactive } from 'vue';
  import request from '@/api/request';
  import SearchFormFold from '@/components/juwei-compoents/search-form-fold/search-form-fold.vue';
  import { stringArrShow } from '@/utils/table-utils/columns-config';
  import UserManageEdit from '@/views/manage/user-manage/user-manage-edit.vue';
  import RequestSelect from '@/components/select/request-select.vue';
  import { stateM2 } from '@/components/dict-select/dict-common';
  import { Message } from '@arco-design/web-vue';
  import UserDepartmentList from '@/views/manage/user-manage/user-department-list.vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';

  const generateFormModel = () => {
    return {
      account_name: '',
      user_name: '',
      keyword: '',
      department_id: '',
      roles: [],
      state: null,
    };
  };
  const loading = ref(false);
  const theTable = ref();
  const formModel = reactive(generateFormModel());

  const getList = async (data: any) => {
    return request('/api/user/userList', {
      ...data,
    });
  };

  const editRef = ref();
  function showEditFn(record?: any) {
    editRef.value?.show(record);
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      fixed: 'left',
    },
    {
      title: '账号',
      dataIndex: 'account_name',
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'user_name',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    {
      title: '角色',
      dataIndex: 'role_names',
      render: stringArrShow(),
    },
    {
      title: '部门',
      dataIndex: 'department_name',
    },
    {
      title: '状态',
      dataIndex: 'state',
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  ];

  // 点击搜索时 处理逻辑
  const handleSubmit = (resData: any = {}) => {
    loading.value = true;
    Object.assign(formModel, resData);
    theTable.value?.search();
  };

  function updateState(record: any) {
    record.loading = true;
    request('/api/user/userSave', {
      ...record,
      password: undefined,
      state: record.state === 1 ? -1 : 1,
    })
      .then(() => {
        record.state = record.state === 1 ? -1 : 1;
      })
      .finally(() => {
        record.loading = false;
      });
  }

  function exportAction() {
    theTable.value?.exportTable({});
  }
</script>

<style scoped lang="less">
  .table-card {
    width: calc(100% - 300px);
  }
</style>

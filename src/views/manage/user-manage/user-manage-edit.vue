<template>
  <d-modal
    v-model:visible="visible"
    width="500px"
    :body-style="{
      maxHeight: 'calc(100vh - 150px)',
    }"
    :mask-closable="false"
    :esc-to-close="false"
    :title="`${formModel.id ? '编辑' : '添加'}用户信息`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formModel" :auto-label-width="true">
      <a-form-item label="账号" field="account_name" :rules="requiredRule">
        <a-input v-model="formModel.account_name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="密码" field="password" :rules="requiredRule">
        <a-input v-model="formModel.password" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="姓名" field="user_name" :rules="requiredRule">
        <a-input v-model="formModel.user_name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="手机号" field="mobile" :rules="requiredRule">
        <a-input v-model="formModel.mobile" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="角色" field="roles" :rules="requiredRuleArr">
        <request-select
          v-model="formModel.roles"
          request-url="/api/user/roleList"
          label-key="role_name"
        />
      </a-form-item>
      <a-form-item label="部门" field="department_id" :rules="requiredRule">
        <request-tree-select
          v-model="formModel.department_id"
          request-url="/api/department/list"
          label-key="department_name"
          child-key="child"
        />
      </a-form-item>
      <a-form-item label="状态">
        <a-switch
          :model-value="formModel.state === 1"
          @change="(val:boolean) => (formModel.state = val ? 1 : -1)"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-spin :loading="loading">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleBeforeOk()"> 确定 </a-button>
        </a-space>
      </a-spin>
    </template>
  </d-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import DModal from '@/components/d-modal/d-modal.vue';
  import request from '@/api/request';
  import { Message } from '@arco-design/web-vue';
  import { requiredRule, requiredRuleArr } from '@/utils/util';
  import { customerServiceStatusListM } from '@/components/dict-select/dict-travel';
  import RequestSelect from '@/components/select/request-select.vue';
  import RequestTreeSelect from '@/components/select/request-tree-select.vue';

  const defaultForm = () => ({
    id: null,
    account_name: '',
    user_name: '',
    password: '',
    mobile: '',
    department_id: '',
    roles: [],
    state: 1,
  });

  const emit = defineEmits(['save']);

  const visible = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const formModel = ref(defaultForm());
  const validate = () => {
    return formRef.value?.validate();
  };
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  const show = (dinfo: any) => {
    let initForm = defaultForm();
    formModel.value = initForm;
    if (dinfo) {
      Object.keys(initForm).forEach((key) => {
        // @ts-ignore
        formModel.value[key] =
          dinfo[key] || initForm[key as keyof typeof initForm];
      });
      formModel.value.password = '****';
    }

    clearValidate();
    visible.value = true;
    loading.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleBeforeOk = async () => {
    const result = await validate();
    if (!result) {
      loading.value = true;
      if (formModel.value.password === '****') {
        // @ts-ignore
        delete formModel.value.password;
      }
      request('/api/user/userSave', {
        ...formModel.value,
      })
        .then(() => {
          Message.success('保存成功');
          handleCancel();
          emit('save');
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  defineExpose({
    show,
  });
</script>

<style lang="less" scoped></style>

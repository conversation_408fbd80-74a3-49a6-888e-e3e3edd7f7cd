<template>
  <div class="chart-box">
    <div class="chart-title">
      <span class="vertical-title">{{ title }}</span>
      <dict-radio v-model="dataType" :data-list="dataTypeList" size="mini" />
    </div>
    <chart class="chart" :options="option" :height="chartHeight" autoresize />
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { numToYiOrWan, tooltipItemsHtmlString } from '@/utils/util';
  import { ToolTipFormatterParams } from '@/types/echarts';
  import { graphic } from 'echarts';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    yKeys: {
      type: Array,
      default: () => ['val'],
    },
    names: {
      type: Array,
      default: null,
    },
    chartData: {
      type: Array,
      default: () => [],
    },
    allData: {
      type: Array,
      default: null,
    },

    chartHeight: {
      type: String,
      default: '200px',
    },
  });

  const dataTypeList = computed(() => {
    const list = [{ label: '增量', value: 'grow' }];
    if (props.allData?.length) {
      list.push({ label: '总量', value: 'all' });
    }
    return list;
  });
  const dataType = ref('grow');

  const colors = ['#165dff', '#00B2FF'];

  const option = computed(() => ({
    grid: {
      left: 5,
      right: 5,
      top: 10,
      bottom: props.yKeys.length > 1 ? 30 : 0,
      containLabel: true,
    },
    legend: {
      bottom: -5,
      icon: 'circle',
      show: props.yKeys.length > 1,
    },
    xAxis: {
      type: 'category',
      show: true,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      data:
        (dataType.value === 'all' ? props.allData : props.chartData)?.map(
          (item: any) => item.date
        ) || [],
    },
    yAxis: {
      show: true,
      // minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
      axisLabel: {
        formatter(value: number, idx: number) {
          return numToYiOrWan(value, 0).text;
        },
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      className: 'echarts-tooltip-diy',
      formatter(params: ToolTipFormatterParams[]) {
        const [firstElement] = params;
        return `<div>
            <p class="tooltip-title">${firstElement.axisValueLabel}</p>
            ${tooltipItemsHtmlString(params)}
          </div>`;
      },
    },
    series: props.yKeys.map((key: any, index: number) => ({
      name: props.names?.[index] || props.title,
      type: 'line',
      showSymbol: false,
      smooth: true,
      data:
        (dataType.value === 'all' ? props.allData : props.chartData)?.map(
          (item: any) => item[key as any]
        ) || [],
      itemStyle: {
        color: colors[index],
      },
      lineStyle: {
        width: 3,
        color:
          props.yKeys.length === 1
            ? new graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: 'rgba(30, 231, 255, 1)',
                },
                {
                  offset: 0.5,
                  color: 'rgba(36, 154, 255, 1)',
                },
                {
                  offset: 1,
                  color: 'rgba(111, 66, 251, 1)',
                },
              ])
            : undefined,
      },
      areaStyle: {
        opacity: 0.8,
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(17, 126, 255, 0.16)',
          },
          {
            offset: 1,
            color: 'rgba(17, 128, 255, 0)',
          },
        ]),
      },
    })),
  }));
</script>

<style scoped lang="less"></style>

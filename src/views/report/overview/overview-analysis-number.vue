<template>
  <a-card :body-style="{ paddingLeft: 0, paddingRight: 0 }">
    <div class="card-box">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="df ai-cen jc-cen card-item"
      >
        <a-avatar :size="60" class="card-icon">
          <img alt="avatar" :src="item.icon" />
        </a-avatar>
        <a-statistic
          :title="item.title"
          :value="dataInfo[item.field] || 0"
          :value-from="0"
          animation
          show-group-separator
        />
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps({
    dataInfo: {
      type: Object,
      default: () => ({}),
    },
  });
  const list = ref([
    {
      title: '播放量',
      icon: 'icons/overview/content5.png',
      field: 'play_num_add_num',
    },
    {
      title: '意向评论数',
      icon: 'icons/overview/content2.svg',
      field: 'intention_comment_num',
    },
    {
      title: '已发私信数',
      icon: 'icons/overview/content3.svg',
      field: 'send_num',
    },
    {
      title: '意向线索数',
      icon: 'icons/overview/content4.svg',
      field: 'valid_clue_num',
    },
    {
      title: '留资线索数',
      icon: 'icons/overview/content6.png',
      field: 'contact_clue_num',
    },
  ]);
</script>

<style scoped lang="less">
  .card-box {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    .card-item {
      border-right: 1px solid var(--color-neutral-3);
      &:last-of-type {
        border-right: 0;
      }
      .card-icon {
        margin-right: 10px;
        background: var(--color-fill-2);
      }
      :deep(.arco-statistic) {
        .arco-statistic-title {
          font-size: 14px;
          font-weight: bold;
          white-space: nowrap;
          margin-bottom: 0;
        }
        .arco-statistic-content {
        }
      }
    }
  }
</style>

<template>
  <div class="chart-box">
    <div class="chart-title">
      <span class="vertical-title">线索数据趋势</span>
      <dict-radio
        v-model="dataType"
        :data-list="dataTypeList"
        size="mini"
        style="opacity: 0"
      />
    </div>
    <chart class="chart" :options="option" :height="chartHeight" autoresize />
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { ToolTipFormatterParams } from '@/types/echarts';

  function formatTooltip(params: ToolTipFormatterParams[]) {
    return params
      .map((el) => {
        let val = '';
        switch (el.seriesName) {
          case '已发私信数':
            val = `${el.value}`;
            break;
          case '意向线索率':
            val = `${el.value}% (${el.data?.valid_clue_num}个)`;
            break;
          case '留资线索率':
            val = `${el.value}% (${el.data?.contact_clue_num}个)`;
            break;
          default:
            break;
        }
        return `
              <div class="content-panel">
                <p>
                  <span style="background-color: ${el.color}" class="tooltip-item-icon"></span>
                  <span>${el.seriesName}</span>
                </p>
                <span class="tooltip-value">
                ${val}
                </span>
              </div>`;
      })
      .join('');
  }

  const props = defineProps({
    chartData: {
      type: Array,
      default: () => [],
    },
    allData: {
      type: Array,
      default: null,
    },
    chartHeight: {
      type: String,
      default: '200px',
    },
  });
  const dataTypeList = computed(() => {
    const list = [{ label: '增量', value: 'grow' }];
    if (props.allData?.length) {
      list.push({ label: '总量', value: 'all' });
    }
    return list;
  });
  const dataType = ref('grow');
  const fieldConfig = [
    {
      title: '意向线索率',
      field: 'valid_clue_rate',
      color: '#0FC6C2',
    },
    {
      title: '留资线索率',
      field: 'contact_clue_rate',
      color: '#F7BA1E',
    },
  ];
  const option = computed(() => ({
    tooltip: {
      show: true,
      trigger: 'axis',
      className: 'echarts-tooltip-diy',
      formatter: (params: ToolTipFormatterParams[]) => {
        const [firstElement] = params;
        return `<div>
            <p class = "tooltip-title">${firstElement.axisValueLabel}</p>
            ${formatTooltip(params)}
        </div>`;
      },
    },
    legend: {
      bottom: -5,
      icon: 'circle',
    },
    grid: {
      left: 0,
      right: 0,
      top: 10,
      bottom: 30,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: props.chartData?.map((item: any) => item.accept_date) || [],
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: [
      {
        type: 'value',
        min: 0,
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      {
        type: 'value',
        // max: 100,
        axisLabel: {
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '已发私信数',
        key: 'send_num',
        type: 'bar',
        barMaxWidth: '40px',
        yAxisIndex: 0,
        itemStyle: {
          color: '#4080FF',
          borderRadius: [4, 4, 0, 0],
        },
        data:
          props.chartData?.map((citem: any) => ({
            ...citem,
            value: parseFloat(citem.send_num || 0),
          })) || [],
      },
      ...fieldConfig.map((item) => ({
        name: item.title,
        key: item.field,
        type: 'line',
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 3,
        },
        itemStyle: {
          color: item.color,
        },
        yAxisIndex: 1,
        data:
          props.chartData?.map((citem: any) => ({
            ...citem,
            value: parseFloat(citem[item.field] || 0),
          })) || [],
      })),
    ],
  }));
</script>

<style scoped lang="less"></style>

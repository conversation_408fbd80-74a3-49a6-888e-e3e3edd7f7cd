<template>
  <a-card :body-style="{ paddingTop: 0 }">
    <template #title>
      <span>今日核心数据</span>
      <a-link @click="getData()">
        <template #icon>
          <icon-refresh />
        </template>
        刷新
      </a-link>
    </template>
    <a-grid :cols="24" :col-gap="12" :row-gap="12">
      <a-grid-item
        v-for="(item, index) in config"
        :key="index"
        :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 6, xxl: 6 }"
      >
        <overview-core-data-item
          :title="item.title"
          :chart-type="item.chartType"
          :chart-data="dataInfo?.[item.chartKey] || []"
          :num="dataInfo?.[item.numkey]"
          :rate="dataInfo?.[item.rateKey]"
          :loading="loading"
          :card-style="{
            background: item.background,
          }"
        />
      </a-grid-item>
    </a-grid>
  </a-card>
</template>

<script lang="ts" setup>
  import useThemes from '@/hooks/themes';
  import OverviewCoreDataItem from '@/views/report/overview/overview-core-data-item.vue';
  import { computed, PropType, ref, watch } from 'vue';
  import request from '@/api/request';

  const { isDark } = useThemes();

  const props = defineProps({
    sendParams: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });
  const config = computed(() => [
    {
      title: '新增粉丝数',
      chartKey: 'fans_add_last_7',
      numkey: 'today_fans_add_num',
      rateKey: 'today_fans_add_rate',
      chartType: 'line',
      background: isDark.value
        ? 'linear-gradient(180deg, #284991 0%, #122B62 100%)'
        : 'linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%)',
    },
    {
      title: '发布作品数',
      chartKey: 'publish_last_7',
      numkey: 'today_publish_num',
      rateKey: 'today_publish_rate',
      chartType: 'bar',
      background: isDark.value
        ? ' linear-gradient(180deg, #3D492E 0%, #263827 100%)'
        : 'linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%)',
    },
    {
      title: '点赞数',
      chartKey: 'ddg_add_last_7',
      numkey: 'today_ddg_add_num',
      rateKey: 'today_ddg_add_rate',
      chartType: 'line',
      background: isDark.value
        ? 'linear-gradient(180deg, #294B94 0%, #0F275C 100%)'
        : 'linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%)',
    },
    {
      title: '评论数',
      chartKey: 'comment_last_7',
      numkey: 'today_comment_num',
      rateKey: 'today_comment_rate',
      chartType: 'line',
      background: isDark.value
        ? 'linear-gradient(180deg, #312565 0%, #201936 100%)'
        : 'linear-gradient(180deg, #F7F7FF 0%, #ECECFF 100%)',
    },
  ]);
  const dataInfo = ref<any>({});

  const loading = ref(false);

  function getData() {
    loading.value = true;
    request('/api/overview/todayData', {
      ...props.sendParams,
    })
      .then((res) => {
        dataInfo.value = res.data || {};
      })
      .finally(() => {
        loading.value = false;
      });
  }
  watch(
    () => props.sendParams,
    () => {
      getData();
    },
    {
      immediate: true,
      deep: true,
    }
  );
</script>

<template>
  <div class="chart-box">
    <div class="chart-title">
      <span class="vertical-title">{{ title }}</span>
      <dict-radio
        v-model="dataType"
        :data-list="dataTypeList"
        size="mini"
        style="opacity: 0"
      />
    </div>
    <chart
      v-if="chartData?.length"
      class="chart"
      :options="option"
      :height="chartHeight"
      autoresize
    />
    <div v-else :style="{ height: chartHeight }" class="df ai-cen">
      <a-empty />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { numToYiOrWan, tooltipItemsHtmlString } from '@/utils/util';

  import { ToolTipFormatterParams } from '@/types/echarts';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    yKeys: {
      type: Array,
      default: () => ['val'],
    },
    names: {
      type: Array,
      default: null,
    },
    chartData: {
      type: Array,
      default: () => [],
    },
    allData: {
      type: Array,
      default: null,
    },
    isRate: {
      type: Boolean,
      default: false,
    },
    rotate: {
      type: Boolean,
      default: false,
    },
    chartHeight: {
      type: String,
      default: '200px',
    },
  });

  const dataTypeList = computed(() => {
    const list = [{ label: '增量', value: 'grow' }];
    if (props.allData?.length) {
      list.push({ label: '总量', value: 'all' });
    }
    return list;
  });
  const dataType = ref('grow');

  const colors = ['#165dff', '#00B2FF'];

  const option = computed(() => {
    const xAxis = {
      type: 'category',
      show: true,
      ...(props.rotate
        ? {
            // axisLabel: {
            //  inside: true,
            //  color: '#fff',
            // },
            // z: 10,
          }
        : {}),
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      data:
        (dataType.value === 'all' ? props.allData : props.chartData)?.map(
          (item: any) => item.service_user || item.date
        ) || [],
    };
    const yAxis = {
      show: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
      axisLabel: {
        formatter(value: number, idx: number) {
          return props.isRate ? `${value}%` : numToYiOrWan(value, 0).text;
        },
      },
    };
    return {
      grid: {
        left: props.rotate ? 18 : 5,
        right: props.rotate ? 18 : 5,
        top: props.rotate ? 0 : 10,
        bottom: 0,
        containLabel: true,
      },
      legend: {
        bottom: 0,
        icon: 'circle',
        show: false,
      },
      xAxis: props.rotate ? yAxis : xAxis,
      yAxis: props.rotate ? xAxis : yAxis,
      tooltip: {
        show: true,
        trigger: 'axis',
        className: 'echarts-tooltip-diy',
        formatter(params: ToolTipFormatterParams[]) {
          const [firstElement] = params;
          return `<div>
            <p class="tooltip-title">${firstElement.axisValueLabel}</p>
            ${tooltipItemsHtmlString(params)}
          </div>`;
        },
      },
      series: props.yKeys.map((key: any, index: number) => ({
        name: props.names?.[index] || props.title,
        type: 'bar',
        showSymbol: false,
        smooth: true,
        barMaxWidth: '20px',
        data:
          (dataType.value === 'all' ? props.allData : props.chartData)?.map(
            (item: any) => item[key as any]
          ) || [],
        itemStyle: {
          // color: colors[index],
          borderRadius: props.rotate ? [0, 4, 4, 0] : [4, 4, 0, 0],
        },
      })),
    };
  });
</script>

<style scoped lang="less"></style>

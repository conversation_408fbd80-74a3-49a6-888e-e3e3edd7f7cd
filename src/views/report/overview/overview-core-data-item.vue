<template>
  <a-spin :loading="loading" style="width: 100%">
    <a-card :bordered="false" :style="cardStyle">
      <div class="content-wrap">
        <div class="content">
          <a-statistic
            :title="title"
            :value="num"
            :value-from="0"
            animation
            show-group-separator
          />
          <div class="desc">
            <a-typography-text type="secondary" class="label">
              较昨日
            </a-typography-text>
            <a-typography-text :type="rate < 0 ? 'danger' : 'success'">
              {{ rate }}%
              <icon-arrow-fall v-if="rate < 0" />
              <icon-arrow-rise v-else />
            </a-typography-text>
          </div>
        </div>
        <div class="chart">
          <Chart v-if="!loading" :option="chartOptions" auto-resize />
        </div>
      </div>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import { PropType, CSSProperties, computed } from 'vue';
  import { ToolTipFormatterParams } from '@/types/echarts';
  import { tooltipItemsHtmlString } from '@/utils/util';

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    chartType: {
      type: String,
      default: 'line',
    },
    chartData: {
      type: Array,
      default: () => [],
    },
    num: {
      type: Number,
      default: null,
    },
    rate: {
      type: String,
      default: '',
    },
    cardStyle: {
      type: Object as PropType<CSSProperties>,
      default: () => {
        return {};
      },
    },
  });
  const chartOptions = computed(() => {
    return {
      dataset: {
        dimensions: ['date', 'val'],
        source: props.chartData || [],
      },
      grid: {
        left: 0,
        right: 0,
        top: 10,
        bottom: 5,
      },
      xAxis: {
        type: 'category',
        show: false,
        data: props.chartData.map((item: any) => item.date) || [],
      },
      yAxis: {
        show: false,
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        className: 'echarts-tooltip-diy',
        formatter(params: ToolTipFormatterParams[]) {
          const [firstElement] = params;
          return `<div>
            <p class="tooltip-title">${firstElement.axisValueLabel}</p>
            ${tooltipItemsHtmlString(params)}
          </div>`;
        },
      },
      series: {
        name: props.title,
        type: props.chartType,
        showSymbol: false,
        smooth: true,
        data: props.chartData.map((item: any) => item.val) || [],
        lineStyle: {
          color: '#165dff',
          width: 3,
          type: 'dashed',
        },
        barMaxWidth: '10px',
        itemStyle:
          props.chartType === 'bar'
            ? {
                color: '#86DF6C',
                borderRadius: [8, 8, 0, 0],
              }
            : undefined,
      },
    };
  });
</script>

<style scoped lang="less">
  :deep(.arco-card) {
    border-radius: 4px;
  }
  :deep(.arco-card-body) {
    width: 100%;
    padding: 0;
  }
  .content-wrap {
    width: 100%;
    padding: 10px 16px;
    white-space: nowrap;
    display: flex;
    align-items: center;
  }
  :deep(.content) {
    width: 130px;
  }
  :deep(.arco-statistic) {
    .arco-statistic-title {
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      margin-bottom: 0;
    }
  }

  .chart {
    width: calc(100% - 130px);
    height: 80px;
    vertical-align: bottom;
  }

  .label {
    padding-right: 8px;
    font-size: 12px;
  }
</style>

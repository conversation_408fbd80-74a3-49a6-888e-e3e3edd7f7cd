<template>
  <div class="overview">
    <div class="header-line">
      <dict-radio
        v-model="formModel.platform"
        :data-list="contactWayListM"
        show-icon
      />
    </div>
    <overview-core-data :send-params="{ platform: formModel.platform }" />
    <a-card
      title="数据分析"
      class="mt-10"
      :style="{ background: '#fff' }"
      :header-style="{
        padding: '16px 16px 10px 16px !important',
      }"
      :body-style="{ paddingTop: 0, paddingBottom: '10px' }"
    >
      <template #extra>
        <a-space>
          <dict-radio
            v-model="formModel.dateKey"
            size="normal"
            :data-list="dateList"
          />
          <a-range-picker
            v-model="formModel.date"
            size="small"
            class="w-300"
            :allow-clear="false"
            @change="formModel.dateKey = 2"
          />
        </a-space>
      </template>
      <a-row>
        <a-col :span="19">
          <a-row :gutter="[20, 20]">
            <a-col
              :span="24"
              style="
                margin-top: 10px;
                margin-bottom: 0px;
                padding-left: 0;
                padding-right: 0;
              "
            >
              <overview-analysis-number :data-info="dataInfo.data_count" />
            </a-col>
            <a-col :span="12">
              <overview-analysis-bar
                title="发布作品"
                :chart-data="dataInfo.publish_production"
                :names="['发布作品数']"
                :chart-height="chartHeight"
              />
            </a-col>
            <a-col :span="12">
              <overview-analysis-line
                title="粉丝数据趋势"
                :chart-data="dataInfo.fans_add_grow"
                :all-data="dataInfo.fans_add_total || []"
                :names="['粉丝数']"
                :chart-height="chartHeight"
              />
            </a-col>
            <a-col :span="12">
              <overview-analysis-line
                title="播放量趋势"
                :chart-data="dataInfo.play_num_grow"
                :all-data="dataInfo.play_num_total || []"
                :names="['播放量']"
                :chart-height="chartHeight"
              />
            </a-col>
            <a-col :span="12">
              <overview-analysis-line
                title="点赞数据趋势"
                :chart-data="dataInfo.ddg_num_grow"
                :all-data="dataInfo.ddg_num_total || []"
                :names="['点赞数']"
                :chart-height="chartHeight"
              />
            </a-col>
            <a-col :span="12">
              <overview-analysis-line
                title="评论数据趋势"
                :chart-data="dataInfo.comment_grow"
                :y-keys="['val', 'val_2']"
                :names="['评论数', '意向评论数']"
                :chart-height="chartHeight"
              />
            </a-col>
            <a-col :span="12">
              <overview-clue-line
                :chart-data="dataInfo.clue_trend"
                :chart-height="chartHeight"
              />
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="5">
          <div class="right-box">
            <overview-analysis-bar
              title="线索分配"
              :chart-data="dataInfo.contact_clue"
              :y-keys="['contact_clue_num']"
              :names="['留资线索数']"
              rotate
              :chart-height="rightChartHeight"
            />
            <overview-analysis-bar
              title="跟进效率"
              :chart-data="dataInfo.contact_clue"
              :y-keys="['follow_up_rate']"
              :names="['跟进率']"
              is-rate
              rotate
              :chart-height="rightChartHeight"
            />
            <overview-analysis-bar
              title="成交效率"
              :chart-data="dataInfo.contact_clue"
              :y-keys="['deal_rate']"
              :names="['成交率']"
              is-rate
              rotate
              :chart-height="rightChartHeight"
            />
          </div>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, watchEffect } from 'vue';
  import DictRadio from '@/components/dict-select/dict-radio.vue';
  import { contactWayListM } from '@/components/dict-select/dict-travel';
  import OverviewCoreData from '@/views/report/overview/overview-core-data.vue';
  import dayjs from 'dayjs';
  import request from '@/api/request';
  import OverviewAnalysisNumber from '@/views/report/overview/overview-analysis-number.vue';
  import OverviewAnalysisLine from '@/views/report/overview/overview-analysis-line.vue';
  import OverviewClueLine from '@/views/report/overview/overview-clue-line.vue';
  import OverviewAnalysisBar from '@/views/report/overview/overview-analysis-bar.vue';

  const dateList = [
    { label: '昨天', value: -1 },
    { label: '近7天', value: -6 },
    { label: '近30天', value: -29 },
    { label: '自定义', value: 2 },
  ];
  const formModel = ref({
    platform: '抖音',
    date: [
      dayjs().add(-6, 'd').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ],
    dateKey: -6,
  });

  const chartHeight = '210px';
  const rightChartHeight = '240px';

  watchEffect(() => {
    switch (formModel.value.dateKey) {
      case -1:
        formModel.value.date = [
          dayjs().add(-1, 'd').format('YYYY-MM-DD'),
          dayjs().add(-1, 'd').format('YYYY-MM-DD'),
        ];
        break;
      case 2:
        break;
      default:
        formModel.value.date = [
          dayjs().add(formModel.value.dateKey, 'd').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD'),
        ];
        break;
    }
  });
  const dateParams = computed(() => {
    let { date } = formModel.value;
    return {
      date_begin: date[0],
      date_end: date[1],
      ...formModel.value,
    };
  });

  const loading = ref(false);
  const dataInfo = ref<any>({});
  function getData() {
    loading.value = true;
    request('/api/overview/dataAnalysis', {
      ...dateParams.value,
    })
      .then((res) => {
        dataInfo.value = res.data;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  watch(
    dateParams,
    () => {
      getData();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style lang="less">
  .overview {
    :deep(.arco-card-header) {
      padding: 10px 16px;
    }
    :deep(.arco-card-body) {
      padding: 10px 16px;
    }
    .arco-card-header-title {
      font-weight: bold !important;
    }
    .header-line {
      margin-bottom: 10px;
      width: max-content;
    }
    .chart-box {
      .chart-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .vertical-title {
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
    .right-box {
      margin-left: 18px;
      padding-left: 18px;
      border-left: 1px solid var(--color-neutral-3);
      margin-top: 25px !important;
      .chart-box {
        margin-bottom: 18px;
        .chart-title {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>

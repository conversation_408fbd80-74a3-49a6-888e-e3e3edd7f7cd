#!/usr/bin/env bash

# 构建项目，清空输出目录
npm run build --emptyOutDir

# 获取Git相关信息
version="$(git symbolic-ref --short -q HEAD)"  # 获取当前分支名
hash="$(git rev-parse --short HEAD)"          # 获取当前提交的短哈希值
date="$(git show -s --format=%cI)"            # 获取提交时间
author="$(git show -s --format=%an)"          # 获取提交作者
commit="$(git log --pretty=format:'%s' -1)"   # 获取最新提交信息

# 设置发布目录名称
filename="getclue_dev"

# 切换到发布仓库并更新
cd ../clue-admin-publish || exit
git pull

# 清理旧的发布文件
rm -rf $filename

# 复制新的构建文件到发布目录
cd ../clue-admin-frontend || exit
cp -rf ./dist/$filename ../clue-admin-publish/

# 打印发布信息
echo -----------------------------
echo $filename
echo -----------------------------

# 提交并推送更改到发布仓库
cd ../clue-admin-publish || exit
git pull
git add .
git commit -m "$commit - $version:$hash|$author:$date"
git push

# 触发测试环境同步
curl -i "http://10.200.16.50:9163/sync_dev.php?dir=$filename"

# 返回前端项目目录
cd ../clue-admin-frontend || exit

# 打印完成信息
echo "完成- $commit - $version:$hash|$author:$date -更新"



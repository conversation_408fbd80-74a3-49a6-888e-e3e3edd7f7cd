#!/usr/bin/env bash

# 构建 TOS 版本
npm run build_tos --emptyOutDir

# 获取 Git 相关信息
version="$(git symbolic-ref --short -q HEAD)"  # 当前分支名
hash="$(git rev-parse --short HEAD)"          # 当前提交的短哈希值
date="$(git show -s --format=%cI)"            # 提交时间
author="$(git show -s --format=%an)"          # 提交作者
commit="$(git log --pretty=format:'%s' -1)"   # 提交信息

filename="getclue"

# 更新发布仓库
cd ../clue-admin-publish || exit
git pull
rm -rf $filename
cd ../clue-admin-frontend || exit
cp -rf ./dist/$filename ../clue-admin-publish/

# 上传到 TOS 存储
node upload_tos.js ../clue-admin-publish/$filename codestatic/travel/$filename

echo -----------------------------
echo "正在处理文件: $filename"
echo -----------------------------

# 提交并推送更改
cd ../clue-admin-publish || exit
git pull
git add .
git commit -m "$commit - $version:$hash|$author:$date"
git push

# 触发正式版本更新
curl -i "https://getclue-v2.pengwin.com/sync.php?dir=$filename"

cd ../clue-admin-frontend || exit

echo "部署完成 - 提交信息: $commit - 版本:$version:$hash|作者:$author:$date"


